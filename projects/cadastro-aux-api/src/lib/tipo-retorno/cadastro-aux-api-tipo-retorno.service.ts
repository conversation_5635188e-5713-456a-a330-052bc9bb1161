import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { TipoRetorno } from "./tipo-retorno.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiTipoRetornoService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`tipo-retorno/${id}`);
	}

	public save(tipoRetorno: TipoRetorno): Observable<any> {
		return this.restApi.post(`tipo-retorno`, tipoRetorno);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`tipo-retorno/${id}`);
	}

	public consultar(): Observable<any> {
		return this.restApi.get(`tipo-retorno`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`tipo-retorno/findAll`);
	}
}
