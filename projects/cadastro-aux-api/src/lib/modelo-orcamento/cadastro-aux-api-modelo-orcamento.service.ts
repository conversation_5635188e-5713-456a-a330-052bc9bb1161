import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { ModeloOrcamento } from "./modelo-orcamento.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiModeloOrcamentoService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`modelo-orcamento/${id}`);
	}

	public save(modeloOrcamento: ModeloOrcamento): Observable<any> {
		return this.restApi.post(`modelo-orcamento`, modeloOrcamento);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`modelo-orcamento/${id}`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`modelo-orcamento`);
	}
}
