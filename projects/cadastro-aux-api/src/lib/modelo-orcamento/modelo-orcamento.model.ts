export class ModeloOrcamento {
	codigo: number;
	descricao: string;
	situacao: string;
	modalidade: any;
	pacote: any;
	dataDefinicao: Date;
	responsavelDefinicao: any;
	texto: string;

	constructor() {
		this.codigo = 0;
		this.descricao = "";
		this.situacao = "";
		this.modalidade = null;
		this.pacote = null;
		this.dataDefinicao = null;
		this.responsavelDefinicao = null;
		this.texto = "";
	}
}

export enum SituacaoModeloOrcamentoEnum {
	INATIVA = "IN",
	ATIVA = "AT",
}

export const SITUACAO_MODELO_ORCAMENTO_OPTIONS = [
	{ id: SituacaoModeloOrcamentoEnum.INATIVA, descricao: "Inativo" },
	{ id: SituacaoModeloOrcamentoEnum.ATIVA, descricao: "Ativo" },
];
