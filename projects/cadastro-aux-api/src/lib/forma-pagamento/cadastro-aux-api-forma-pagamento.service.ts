import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { FormaPagamento } from "./forma-pagamento.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiFormaPagamentoService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`forma-pagamento/${id}`);
	}

	public save(formaPagamento: FormaPagamento): Observable<any> {
		return this.restApi.post(`forma-pagamento`, formaPagamento);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`forma-pagamento/${id}`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`forma-pagamento`);
	}
}
