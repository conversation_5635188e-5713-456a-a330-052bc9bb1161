import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { Pinpad } from "./pinpad.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiPinpadService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`pinpad/${id}`);
	}

	public save(pinpad: Pinpad): Observable<any> {
		return this.restApi.post(`pinpad`, pinpad);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`pinpad/${id}`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`pinpad`);
	}
}
