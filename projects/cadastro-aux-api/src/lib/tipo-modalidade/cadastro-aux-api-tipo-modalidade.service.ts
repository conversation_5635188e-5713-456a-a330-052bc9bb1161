import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { TipoModalidade } from "./tipo-modalidade.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiTipoModalidadeService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`tipo-modalidade/${id}`);
	}

	public save(tipoModalidade: TipoModalidade): Observable<any> {
		return this.restApi.post(`tipo-modalidade`, tipoModalidade);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`tipo-modalidade/${id}`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`tipo-modalidade`);
	}
}
