import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { Ambiente } from "./ambiente.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiAmbienteService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`ambiente/${id}`);
	}

	public save(ambiente: Ambiente): Observable<any> {
		return this.restApi.post(`ambiente`, ambiente);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`ambiente/${id}`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`ambiente`);
	}
}
