import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { TipoRemessa } from "./tipo-remessa.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiTipoRemessaService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`tipo-remessa/${id}`);
	}

	public save(tipoRemessa: TipoRemessa): Observable<any> {
		return this.restApi.post(`tipo-remessa`, tipoRemessa);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`tipo-remessa/${id}`);
	}

	public consultar(): Observable<any> {
		return this.restApi.get(`tipo-remessa`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`tipo-remessa/findAll`);
	}
}
