export class TipoRemessa {
	codigo: number;
	descricao: string;
	tipoRetorno: any;
	arquivoLayoutRemessa: string;
	tipoRemessa: string;

	constructor() {
		this.codigo = 0;
		this.descricao = "";
		this.tipoRetorno = null;
		this.arquivoLayoutRemessa = "";
		this.tipoRemessa = "";
	}
}

export enum ArquivoLayoutRemessaEnum {
	NENHUM = "",
	DCC = "LAYOUT-REMESSA-DCC",
	CARNE = "CARNE",
	BOLETO_BB = "BOLETO-BANCO-DO-BRASIL",
	BOLETO_ITAU = "ITAU",
	BOLETO_BRB = "BRB",
	BOLETO_CLUBE = "CLUBE",
	BOLETO_PADRAO = "BOLETO",
	CARNE_BRADESCO = "CARNE-BRADESCO",
	CARNE_BANCO_DO_BRASIL = "CARNE-BANCO-DO-BRASIL",
	CARNE_BANCO_DO_NORDESTE = "CARNE-BANCO-DO-NORDESTE",
	BOLETO_CAIXA = "BOLETO-CAIXA",
	BOLETO_SICOOB_240 = "BOLETO-SICOOB-240",
	BOLETO_SICOOB_240_CARNE = "BOLETO-CARNE-SICOOB-240",
	BOLETO_DAYCOVAL = "BOLETO_DAYCOVAL",
	BOLETO_SICREDI = "BOLETO-SICREDI",
	CARNE_BANCO_SICREDI = "CARNE-BANCO-SICREDI",
	BOLETO_SANTANDER = "BOLETO_SANTANDER",
	BOLETO_SAFRA = "BOLETO-SAFRA",
}

export const ARQUIVO_LAYOUT_REMESSA_OPTIONS = [
	{ value: ArquivoLayoutRemessaEnum.NENHUM, label: "NENHUM" },
	{ value: ArquivoLayoutRemessaEnum.DCC, label: "DCC/DCO" },
	{
		value: ArquivoLayoutRemessaEnum.CARNE,
		label: "CARNÊ (3 POR PÁGINA) SANTANDER",
	},
	{
		value: ArquivoLayoutRemessaEnum.BOLETO_BB,
		label: "BOLETO MODELO BANCO DO BRASIL",
	},
	{ value: ArquivoLayoutRemessaEnum.BOLETO_ITAU, label: "BOLETO MODELO ITAÚ" },
	{ value: ArquivoLayoutRemessaEnum.BOLETO_BRB, label: "BOLETO MODELO BRB" },
	{
		value: ArquivoLayoutRemessaEnum.BOLETO_CLUBE,
		label: "BOLETO MODELO CLUBE",
	},
	{ value: ArquivoLayoutRemessaEnum.BOLETO_PADRAO, label: "BOLETO PADRÃO" },
	{
		value: ArquivoLayoutRemessaEnum.CARNE_BRADESCO,
		label: "CARNÊ (3 POR PÁGINA) BRADESCO",
	},
	{
		value: ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_BRASIL,
		label: "CARNÊ (3 POR PÁGINA) BANCO DO BRASIL",
	},
	{
		value: ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_NORDESTE,
		label: "CARNÊ (3 POR PÁGINA) BANCO DO NORDESTE",
	},
	{
		value: ArquivoLayoutRemessaEnum.BOLETO_CAIXA,
		label: "BOLETO MODELO CAIXA",
	},
	{
		value: ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240,
		label: "BOLETO MODELO SICOOB 240",
	},
	{
		value: ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE,
		label: "CARNÊ (3 POR PÁGINA) SICOOB 240",
	},
	{ value: ArquivoLayoutRemessaEnum.BOLETO_DAYCOVAL, label: "BOLETO_DAYCOVAL" },
	{ value: ArquivoLayoutRemessaEnum.BOLETO_SICREDI, label: "BOLETO SICREDI" },
	{
		value: ArquivoLayoutRemessaEnum.CARNE_BANCO_SICREDI,
		label: "CARNE BANCO SICREDI",
	},
	{
		value: ArquivoLayoutRemessaEnum.BOLETO_SANTANDER,
		label: "BOLETO SANTANDER",
	},
	{ value: ArquivoLayoutRemessaEnum.BOLETO_SAFRA, label: "BOLETO SAFRA" },
];
