import { Injectable } from "@angular/core";
import { CadastroAuxApiModule } from "../cadastro-aux-api.module";
import { CadastroAuxApiBaseService } from "../cadastro-aux-api-base.service";
import { Observable } from "rxjs";
import { NivelTurma } from "./nivel-turma.model";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiNivelTurmaService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`nivel-turma/${id}`);
	}

	public save(nivelTurma: NivelTurma): Observable<any> {
		return this.restApi.post(`nivel-turma`, nivelTurma);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`nivel-turma/${id}`);
	}

	public findAll(): Observable<any> {
		return this.restApi.get(`nivel-turma`);
	}
}
