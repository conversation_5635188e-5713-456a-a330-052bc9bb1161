import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { JwtHelperService } from "@auth0/angular-jwt";
import { from, Observable, of, zip } from "rxjs";
import { catchError, map, switchMap, tap } from "rxjs/operators";
import { environment } from "../environments/environment";
import { MovideskChatService } from "../movidesk-chat.service";
import {
	ApiOamdResponseSingle,
	ClientDiscoveryService,
} from "./client-discovery.service";
import { CryptoService } from "./crypto.service";
import { CacheEmpresaDTO } from "./models/cache-empresa.model";
import {
	ClientDiscoveryData,
	LoginDiscoveryData,
	LoginDiscoveryRequestData,
	PlataformaModulo,
} from "./models/client-discovery.model";
import {
	DecodedToken,
	LoginUrlQueries,
	ValidTokenResponse,
} from "./models/client-model";
import { ConfigRedeEmpresa } from "./models/config-rede-empresa.modelo";
import { EmpresaFinanceiro } from "./models/empresa-financeiro.model";
import { FuncionalidadeVO } from "./models/funcionalidade.model";
import { InfoMigracao } from "./models/info-migracao.model";
import { OperacaoPactoStore } from "./models/movidesk.model";
import { PerfilAcessoDetalheSdk } from "./models/perfil-acesso.model";
import { ApiResponseList, ApiResponseSingle } from "./models/rest.model";
import { SessionLogin } from "./models/session-login.model";
import { PefilUsuarioAdm, UsuarioBase } from "./models/user-model";
import { NotificarRecursoEmpresaService } from "./notificar-recurso-empresa.service";
import { OamdService } from "./oamd.service";

export interface SetupParams {
	chave: string;
	usuarioZwId: string;
	usuarioOamd: string;
	empresaId: string;
	zwUrl: string;
	oamdUrl: string;
	codigoFinanceiro: string;
	paginaDirecionar: string;
	marketplace: string;
	treinoIndependente: boolean;
	treinoUrl: string;
	zwBootUrl?: string;
}

@Injectable({
	providedIn: "root",
})
export class SessionService {
	modulosHabilitados: Array<PlataformaModulo> = [];
	empresas: EmpresaFinanceiro[] = [];
	financeiroEmpresas: EmpresaFinanceiro[] = [];
	empresaId: string;
	chave: string;
	colaborador: any;
	public codUsuarioZW: any;
	public empresasAcesso = [];
	perfilUsuario: PerfilAcessoDetalheSdk;
	perfilUsuarioAdm: PefilUsuarioAdm;
	perfilUsuarioTreino: PerfilAcessoDetalheSdk;
	loggedUser: UsuarioBase = undefined;
	usuarioOamd: string;
	jwtService: JwtHelperService;
	linkZw: string;
	linkGOR: string;
	goBackPage?: string;
	goBackModule?: string;
	private _zwJSId?: string;
	apresentarMenu = true;
	apresentarPactoStore = false;
	moduloTreinoHabilitado = false;
	funcionalidadesInativas: string[];
	funcionalidadesAtivas: string[] = [];
	configRedeEmpresa: ConfigRedeEmpresa;
	infoMigracaoHabilitados: Array<InfoMigracao> = new Array<InfoMigracao>();

	getCacheFuncionalidadesInativas(
		chave: string,
		empresa: number
	): CacheEmpresaDTO {
		try {
			const cacheKey = this.cacheKey(chave, empresa);
			const storage = localStorage.getItem(cacheKey);
			const cache: CacheEmpresaDTO =
				storage !== null && storage !== undefined ? JSON.parse(storage) : null;

			if (cache && cache.data) {
				const dataCache = new Date(cache.data);
				var diferencaEmMilissegundos = Math.abs(
					new Date().getTime() - dataCache.getTime()
				);
				var diferencaEmMinutos = Math.floor(
					diferencaEmMilissegundos / (1000 * 60)
				);
				if (diferencaEmMinutos < environment.validadeCacheNichoEmMinutos) {
					return cache;
				} else {
					this.clearCacheFuncionalidadesInativas(chave, empresa);
					return null;
				}
			}
		} catch (e) {
			console.error(e);
			return null;
		}
	}

	clearCacheFuncionalidadesInativas(chave: string, empresa: number) {
		const cacheKey = this.cacheKey(chave, empresa);
		localStorage.removeItem(cacheKey);
	}

	setCacheFuncionalidadesInativas(
		chave: string,
		empresa: number,
		cacheEmpresa: CacheEmpresaDTO
	) {
		const cacheKey = this.cacheKey(chave, empresa);
		const storage = localStorage.setItem(
			cacheKey,
			JSON.stringify(cacheEmpresa)
		);
	}

	load(params: SetupParams): Observable<boolean> {
		this.apresentarMenu = true;
		this.apresentarPactoStore = true;
		this.params = params;

		if (this.paramsValid()) {
			this.obterApresentarPactoStore();
			return zip(
				this.obterEmpresaFinanceiro(params),
				this.obterUsuarioLogadoZw(params)
			).pipe(
				map((result) => {
					this.empresaFinanceiro = result[0];
					this.loggedUser = result[1];
					return result[0] && (result[1] as boolean);
				})
			);
		} else {
			return of(false);
		}
	}

	get isUsuarioPacto(): boolean {
		return (
			this.loggedUser && this.loggedUser.username.toLowerCase() === "pactobr"
		);
	}

	get isUsuarioPactoSolucoes(): boolean {
		return (
			this.loggedUser &&
			["pactobr", "admin", "recor"].includes(
				this.loggedUser.username.toLowerCase()
			)
		);
	}

	get multiUnidade(): boolean {
		return (
			(this.empresas && this.empresas.length > 1) ||
			(this.empresasAcesso && this.empresasAcesso.length > 1)
		);
	}

	get currentEmpresa(): EmpresaFinanceiro {
		return this.empresas.find(
			(item) =>
				parseInt(`${item.codigo}`, 10) === parseInt(`${this.empresaId}`, 10)
		);
	}

	get decodedApiToken(): DecodedToken {
		const apiToken = this.token;
		return this.jwtService.decodeToken(apiToken);
	}

	get decodedApiTokenContent() {
		return JSON.parse(this.decodedApiToken.content);
	}

	get tokenChave() {
		if (this.decodedApiToken) {
			let contentToken = this.jwtService.decodeToken(this.token).content;
			contentToken = JSON.parse(contentToken);
			return contentToken.k;
		} else {
			return null;
		}
	}

	get tokenColaboradorId() {
		if (this.decodedApiToken) {
			return this.decodedApiToken.colaboradorid;
		} else {
			return null;
		}
	}

	get tokenProvider() {
		if (this.decodedApiToken) {
			return this.decodedApiToken.provider;
		} else {
			return null;
		}
	}

	get apiTokenExpiration() {
		return this.decodedApiToken.exp;
	}

	get integracaoZW() {
		if (this.modulosHabilitados) {
			return this.modulosHabilitados.includes(PlataformaModulo.NZW);
		} else {
			return false;
		}
	}

	get recursos() {
		if (this.perfilUsuario) {
			return this.perfilUsuario.recursos;
		} else {
			return null;
		}
	}

	get funcionalidades() {
		return this.perfilUsuario.funcionalidades;
	}

	get funcionalidadesPermitidas() {
		const funcionalidadesPermitidas = [];
		this.funcionalidades.forEach((v, key) => {
			if (v) {
				funcionalidadesPermitidas.push(key);
			}
		});
		return funcionalidadesPermitidas;
	}

	getRecursosByFunction(
		functionToCall: "consultar" | "incluir" | "excluir" | "editar" = "consultar"
	) {
		const recursos = [];
		this.recursos.forEach((value, key) => {
			if (functionToCall === "consultar" && value.consultar) {
				recursos.push(key);
			}
			if (functionToCall === "incluir" && value.incluir) {
				recursos.push(key);
			}
			if (functionToCall === "excluir" && value.excluir) {
				recursos.push(key);
			}
			if (functionToCall === "editar" && value.editar) {
				recursos.push(key);
			}
		});
		return recursos;
	}

	constructor(
		private http: HttpClient,
		private cryptoService: CryptoService,
		private clientDiscoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale,
		private movideskChatService: MovideskChatService,
		private notificarRecursoEmpresaService: NotificarRecursoEmpresaService,
		private oamdService: OamdService
	) {
		this.jwtService = new JwtHelperService();
	}

	/**
	 * Verifica se o modulo está habilidatado
	 */
	isModuloHabilitado(modulo: PlataformaModulo): boolean {
		if (modulo === PlataformaModulo.CCL) {
			return true;
		}
		return this.modulosHabilitados.includes(modulo);
	}

	isUserLogged(): boolean {
		return this.loggedUser !== undefined;
	}

	get zwJSId() {
		return this.decodedApiTokenContent.zwJSId || this._zwJSId;
	}

	set zwJSId(zwJSId: string) {
		this._zwJSId = zwJSId;
	}

	logOut(): void {
		const zwJSId = this._zwJSId;
		this.clientDiscoveryService
			.linkZw(this.usuarioOamd, this.empresaId)
			.pipe(
				switchMap((response) => {
					response += `&logout=true&zwJSId=${zwJSId}`;
					const headers = new HttpHeaders().set(
						"Content-Type",
						"text/plain;charset=utf-8"
					);
					return this.http.get(response, { headers, responseType: "text" });
				}),
				catchError((_) => of(null))
			)
			.subscribe((_) => {
				this.loggedUser = undefined;

				const urlMap = this.clientDiscoveryService.getUrlMap();
				let url = "";
				if (
					environment.newLoginEnabled &&
					urlMap.loginFrontUrl !== undefined &&
					urlMap.loginFrontUrl !== ""
				) {
					url = `${urlMap.loginFrontUrl}/${this.locale}/logout`;
				} else {
					url = `${urlMap.loginAppUrl}/${this.tokenChave}`;
				}
				localStorage.clear();
				sessionStorage.clear();
				location.replace(url);
			});
	}

	private fetchTokenFromLogin_NOVO(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		console.log("1_fetchTokenFromLogin executando...");
		return this.modulosHabilitados.includes(PlataformaModulo.SEC)
			? this.fetchTokenFromLogin_new(loginUrl, sessionId)
			: this.fetchTokenFromLogin_old(loginUrl, sessionId);
	}

	private fetchTokenFromLogin_old(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		const session$ = fetch(`${loginUrl}/prest/session/${sessionId}`).then((r) =>
			r.json()
		);
		return from(session$).pipe(
			tap((session) => session),
			catchError(() => of(null))
		);
	}

	private fetchTokenFromLogin_new(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		const body = {
			sessionId,
		};
		const bodyCrip = this.cryptoService.encrypt(JSON.stringify(body));
		const url = `${loginUrl}/prest/session-auth`;
		return this.http
			.post<ApiResponseSingle<SessionLogin>>(url, { data: bodyCrip })
			.pipe(
				map((response: any) => {
					const resp = this.cryptoService.decrypt(response.content);
					return JSON.parse(resp);
				})
			);
	}

	private fetchTokenFromLogin_ANTIGO(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		const session$ = fetch(`${loginUrl}/prest/session/${sessionId}`).then((r) =>
			r.json()
		);
		return from(session$).pipe(
			tap((session) => session),
			catchError(() => of(null))
		);
	}

	private decodeToken(token: string): DecodedToken {
		if (token) {
			return this.jwtService.decodeToken(token);
		} else {
			return null;
		}
	}

	set token(token: string) {
		localStorage.setItem("apiToken", token);
	}

	get token(): string {
		return localStorage.getItem("apiToken");
	}

	/**
	 * Starts up SPA
	 *
	 * Caso o token não for definido é necessário definir a chave.
	 * Nesse caso o token é obtido através de uma chamada para a API do Login.
	 *
	 * Returns false if able to load the system.
	 */
	loadSession(loginParams: LoginUrlQueries): Observable<boolean> {
		localStorage.removeItem("client-ip");
		this.empresaId = loginParams.empresaId;
		this.goBackPage = loginParams.goBackPage;
		this.goBackModule = loginParams.goBackModule;
		this._zwJSId = loginParams.zwJSId;
		this.apresentarMenu = true;
		this.apresentarPactoStore = true;
		let discovery: ClientDiscoveryData;

		let usuarioId;
		if (loginParams.dev) {
			this.chave = loginParams.chave;

			let objLogin: LoginDiscoveryRequestData;
			objLogin = {
				chave: loginParams.chave,
				username: "PACTOBR",
				senha: "123",
			};

			return this.clientDiscoveryService
				.find(objLogin)
				.pipe(
					switchMap((resultDiscovery) => {
						this.empresas = resultDiscovery.empresas as any;
						this.financeiroEmpresas = resultDiscovery.financeiroEmpresas as any;
						this.modulosHabilitados = resultDiscovery.modulosHabilitados;
						return this.clientDiscoveryService.login(
							resultDiscovery.serviceUrls.autenticacaoUrl,
							objLogin
						);
					}),
					map((loginDiscoveryData: LoginDiscoveryData) => {
						this.token = loginDiscoveryData.token;
						this.chave = objLogin.chave;
						usuarioId = loginDiscoveryData.dados.codZW;
						this.codUsuarioZW = usuarioId;
					}),

					switchMap(() => {
						return this.oamdService
							.detalhesEmpresa(this.chave, this.empresaId)
							.pipe(
								catchError((err) => {
									console.error(
										"Error on prepare find company details on OAMD",
										err
									);
									return of(null);
								})
							);
					}),
					tap((response: any) => {
						if (response) {
							let objRetorno = response.return;
							if (objRetorno && objRetorno.nivelAtendimento) {
								localStorage.setItem("nivelAtendimento", response);
							}
						}
					}),

					switchMap(() => {
						return this.clientDiscoveryService.discover(this.chave, this.token);
					}),
					tap((result) => {
						discovery = result;
						this.empresas = result.empresas as any;
						this.financeiroEmpresas = result.financeiroEmpresas as any;
						this.modulosHabilitados = result.modulosHabilitados;
						this.modulosHabilitados.push(PlataformaModulo.AGN);
						this.moduloTreinoHabilitado = this.temModuloTreino();
						this.checkRede(discovery, this.chave);
					}),

					switchMap(() => {
						if (this.modulosHabilitados.includes(PlataformaModulo.SEC)) {
							const opt = {
								headers: {
									Authorization: this.token,
									empresaId: this.empresaId,
								},
							};
							const zwBootUrl = discovery.serviceUrls.zwBack;
							return this.http.get(`${zwBootUrl}/adm/validate-token`, opt).pipe(
								map((response: any) => {
									const resp = this.cryptoService.decrypt(response.content);
									return { content: JSON.parse(resp) };
								})
							);
						} else {
							const zwUrl = `${discovery.serviceUrls.zwUrl}/insec/validateToken?chave=${this.chave}&usuario=${usuarioId}&empresaId=${this.empresaId}`;
							return this.http.get(zwUrl);
						}
					}),
					tap((response: ApiResponseSingle<ValidTokenResponse>) => {
						const empresasPermitidas = [];
						this.empresas.forEach((empresa) => {
							response.content.unidadesEmpresa.forEach((empresaZW) => {
								const codigoEmpresaZW = parseInt(empresaZW.id, 0);
								if (codigoEmpresaZW === empresa.codigo) {
									empresa.siglaNovaPlataforma = empresaZW.siglaNovaPlataforma;
									empresasPermitidas.push(empresa);
								}
							});
						});
						this.empresas = empresasPermitidas;

						const validatedToken = response.content;
						this.loggedUser = validatedToken.user;
						this.addModulosPactobr();
						// TODO: Perfil do usuário do treino estava sendo substituido pelo perfil do usuário do adm. Isso gera problemas em permissão.
						// É preciso refatorar para utilizar os objetos perfilUsuarioAdm e do perfilUsuarioTreino em vez de perfilUsuario
						this.perfilUsuario = new PerfilAcessoDetalheSdk(
							validatedToken.perfilUsuario
						);
						// TODO: não será mais necessário o cast type depois de refatorar o this.perfilUsuario para this.perfilUsuarioAdm e this.perfilUsuarioTreino
						this.perfilUsuarioAdm =
							validatedToken as unknown as PefilUsuarioAdm;
					}),
					switchMap(() => {
						if (this.moduloTreinoHabilitado) {
							const treinoUrl = `${discovery.serviceUrls.treinoApiUrl}/psec/validateToken`;
							return this.http.get(treinoUrl);
						} else {
							return of(null);
						}
					}),
					map((response: ApiResponseSingle<ValidTokenResponse>) => {
						if (this.moduloTreinoHabilitado) {
							const validatedToken = response.content;
							this.perfilUsuarioTreino = new PerfilAcessoDetalheSdk(
								validatedToken.perfilUsuario
							);
							return true;
						}
					}),
					catchError(() => of(false))
				)
				.pipe(
					switchMap(() => {
						const cache = this.getCacheFuncionalidadesInativas(
							this.chave,
							parseInt(this.empresaId)
						);
						if (cache) {
							return of({
								content: cache.funcionalidadesInativas,
								cache: true,
							});
						} else {
							const url = `${discovery.serviceUrls.recursoMsUrl}/v1/funcionalidade/inativas?empresa=${this.empresaId}&chave=${this.chave}`;
							return this.http.get(url);
						}
					}),
					map((response: ApiResponseList<FuncionalidadeVO>) => {
						if (response.content) {
							this.funcionalidadesInativas = response.content.map(
								(funcionalidade) => funcionalidade.name
							);
						}

						if (!response.cache) {
							this.setCacheFuncionalidadesInativas(
								this.chave,
								parseInt(this.empresaId),
								{
									chave: this.chave,
									empresa: parseInt(this.empresaId),
									data: new Date(),
									funcionalidadesInativas: response.content,
								}
							);
						}
					}),
					map(() => true),
					catchError((e) => {
						console.error("Error on prepare session from token ", e);
						return of(true);
					})
				);
		} else {
			if (loginParams.token) {
				this.token = loginParams.token;

				try {
					let contentToken = this.jwtService.decodeToken(this.token).content;
					contentToken = JSON.parse(contentToken);
					this.chave = contentToken.k;
					usuarioId = contentToken.cz;
					this.codUsuarioZW = usuarioId;
				} catch (ex) {
					let contentToken = this.jwtService.decodeToken(this.token);
					this.chave = contentToken.chave;
					usuarioId = contentToken.colaboradorid;
					this.codUsuarioZW = usuarioId;
				}

				return this.clientDiscoveryService
					.discover(this.chave, loginParams.token)
					.pipe(
						tap((result) => {
							discovery = result;
							this.empresas = result.empresas as any;
							this.financeiroEmpresas = result.financeiroEmpresas as any;
							this.modulosHabilitados = result.modulosHabilitados;
							this.modulosHabilitados.push(PlataformaModulo.AGN);
							this.moduloTreinoHabilitado = this.temModuloTreino();
							this.checkRede(discovery, this.chave);
						}),
						switchMap(() => {
							return this.oamdService
								.detalhesEmpresa(this.chave, this.empresaId)
								.pipe(
									catchError((err) => {
										console.error(
											"Error on prepare find company details on OAMD",
											err
										);
										return of(null);
									})
								);
						}),
						tap((response: any) => {
							if (response) {
								let objRetorno = response.return;
								if (objRetorno && objRetorno.nivelAtendimento) {
									localStorage.setItem("nivelAtendimento", response);
								}
							}
						}),
						switchMap((result) => {
							if (this.modulosHabilitados.includes(PlataformaModulo.SEC)) {
								const opt = {
									headers: {
										Authorization: this.token,
										empresaId: this.empresaId,
									},
								};
								const zwBootUrl = discovery.serviceUrls.zwBack;
								return this.http
									.get(`${zwBootUrl}/adm/validate-token`, opt)
									.pipe(
										map((response: any) => {
											const resp = this.cryptoService.decrypt(response.content);
											return { content: JSON.parse(resp) };
										})
									);
							} else {
								const zwUrl = `${discovery.serviceUrls.zwUrl}/insec/validateToken?chave=${this.chave}&usuario=${usuarioId}&empresaId=${this.empresaId}`;
								return this.http.get(zwUrl);
							}
						}),
						tap((response: ApiResponseSingle<ValidTokenResponse>) => {
							const empresasPermitidas = [];
							this.empresas.forEach((empresa) => {
								response.content.unidadesEmpresa.forEach((empresaZW) => {
									const codigoEmpresaZW = parseInt(empresaZW.id, 0);
									if (codigoEmpresaZW === empresa.codigo) {
										empresa.siglaNovaPlataforma = empresaZW.siglaNovaPlataforma;
										empresasPermitidas.push(empresa);
									}
								});
							});
							this.empresas = empresasPermitidas;

							this.loggedUser = response.content.user;
							this.addModulosPactobr();
							// TODO: Perfil do usuário do treino estava sendo substituido pelo perfil do usuário do adm. Isso gera problemas em permissão.
							// É preciso refatorar para utilizar os objetos perfilUsuarioAdm e do perfilUsuarioTreino em vez de perfilUsuario
							this.perfilUsuario = new PerfilAcessoDetalheSdk(
								response.content.perfilUsuario
							);

							// TODO: não será mais necessário o cast type depois de refatorar o this.perfilUsuario para this.perfilUsuarioAdm e this.perfilUsuarioTreino
							this.perfilUsuarioAdm =
								response.content as unknown as PefilUsuarioAdm;
						}),
						switchMap(() => {
							if (this.moduloTreinoHabilitado) {
								const treinoUrl = `${discovery.serviceUrls.treinoApiUrl}/psec/validateToken`;
								return this.http.get(treinoUrl);
							} else {
								return of(null);
							}
						}),
						map((response: ApiResponseSingle<ValidTokenResponse>) => {
							if (this.moduloTreinoHabilitado) {
								const validatedToken = response.content;
								this.perfilUsuarioTreino = new PerfilAcessoDetalheSdk(
									validatedToken.perfilUsuario
								);
							}
						}),
						switchMap(() => {
							const url = `${discovery.serviceUrls.admMsUrl}/v1/perfildeacesso/perfis?unificado=1&page=0&size=1`;
							return this.http.get(url);
						}),
						map((response: ApiResponseList<FuncionalidadeVO>) => {
							if (
								response.content &&
								response.content &&
								response.content.length > 0
							) {
								this.funcionalidadesAtivas.push("PERFIL_ACESSO_UNIFICADO");
							}
						}),
						catchError((e) => {
							console.error("Error on prepare session from token ", e);
							return of(true);
						})
					)
					.pipe(
						switchMap(() => {
							const cache = this.getCacheFuncionalidadesInativas(
								this.chave,
								parseInt(this.empresaId)
							);
							if (cache) {
								return of({
									content: cache.funcionalidadesInativas,
									cache: true,
								});
							} else {
								const url = `${discovery.serviceUrls.recursoMsUrl}/v1/funcionalidade/inativas?empresa=${this.empresaId}&chave=${this.chave}`;
								return this.http.get(url);
							}
						}),
						map((response: ApiResponseList<FuncionalidadeVO>) => {
							if (response.content) {
								this.funcionalidadesInativas = response.content.map(
									(funcionalidade) => funcionalidade.name
								);
							}

							if (!response.cache) {
								this.setCacheFuncionalidadesInativas(
									this.chave,
									parseInt(this.empresaId),
									{
										chave: this.chave,
										empresa: parseInt(this.empresaId),
										data: new Date(),
										funcionalidadesInativas: response.content,
									}
								);
							}
						}),
						map(() => true),
						catchError((e) => {
							console.error("Error on prepare session from token ", e);
							return of(true);
						})
					);
			} else if (loginParams.sessionId) {
				return this.clientDiscoveryService
					.discoverUrls()
					.pipe(
						switchMap((resultDiscovery) => {
							return this.fetchTokenFromLogin_ANTIGO(
								resultDiscovery.serviceUrls.loginAppUrl,
								loginParams.sessionId
							);
						}),
						tap((resultSessionLogin) => {
							this.token = resultSessionLogin.loginToken;
							this.chave = resultSessionLogin.key;
							usuarioId = resultSessionLogin.usuarioId;
							this.codUsuarioZW = usuarioId;
						}),
						switchMap(() => {
							return this.oamdService
								.detalhesEmpresa(this.chave, this.empresaId)
								.pipe(
									catchError((err) => {
										console.error(
											"Error on prepare find company details on OAMD",
											err
										);
										return of(null);
									})
								);
						}),
						tap((response: any) => {
							if (response) {
								let objRetorno = response.return;
								if (objRetorno && objRetorno.nivelAtendimento) {
									localStorage.setItem("nivelAtendimento", response);
								}
							}
						}),
						switchMap(() => {
							return this.clientDiscoveryService.discover(
								this.chave,
								this.token
							);
						}),
						tap((result) => {
							discovery = result;
							this.empresas = result.empresas as any;
							this.financeiroEmpresas = result.financeiroEmpresas as any;
							this.modulosHabilitados = result.modulosHabilitados;
							this.modulosHabilitados.push(PlataformaModulo.AGN);
							this.moduloTreinoHabilitado = this.temModuloTreino();
							this.checkRede(discovery, this.chave);
						}),
						switchMap((result) => {
							if (this.modulosHabilitados.includes(PlataformaModulo.SEC)) {
								const opt = {
									headers: {
										Authorization: this.token,
										empresaId: this.empresaId,
									},
								};
								const zwBootUrl = discovery.serviceUrls.zwBack;
								return this.http
									.get(`${zwBootUrl}/adm/validate-token`, opt)
									.pipe(
										map((response: any) => {
											const resp = this.cryptoService.decrypt(response.content);
											return { content: JSON.parse(resp) };
										})
									);
							} else {
								const zwUrl = `${discovery.serviceUrls.zwUrl}/insec/validateToken?chave=${this.chave}&usuario=${usuarioId}&empresaId=${this.empresaId}`;
								return this.http.get(zwUrl);
							}
						}),
						tap((response: ApiResponseSingle<ValidTokenResponse>) => {
							const empresasPermitidas = [];
							this.empresas.forEach((empresa) => {
								response.content.unidadesEmpresa.forEach((empresaZW) => {
									const codigoEmpresaZW = parseInt(empresaZW.id, 0);
									if (codigoEmpresaZW === empresa.codigo) {
										empresa.siglaNovaPlataforma = empresaZW.siglaNovaPlataforma;
										empresasPermitidas.push(empresa);
									}
								});
							});
							this.empresas = empresasPermitidas;
							const validatedToken = response.content;
							this.loggedUser = validatedToken.user;
							this.addModulosPactobr();
							this.perfilUsuario = new PerfilAcessoDetalheSdk(
								validatedToken.perfilUsuario
							);
							this.perfilUsuarioAdm =
								response.content as unknown as PefilUsuarioAdm;
						}),
						catchError((e) => {
							console.error("Error on prepare session from session_id ", e);
							return of(true);
						})
					)
					.pipe(
						switchMap(() => {
							if (this.moduloTreinoHabilitado) {
								const treinoUrl = `${discovery.serviceUrls.treinoApiUrl}/psec/validateToken`;
								return this.http.get(treinoUrl);
							} else {
								return of(null);
							}
						}),
						map((response: ApiResponseSingle<ValidTokenResponse>) => {
							if (this.moduloTreinoHabilitado) {
								const validatedToken = response.content;
								this.perfilUsuarioTreino = new PerfilAcessoDetalheSdk(
									validatedToken.perfilUsuario
								);
							}
						}),
						switchMap(() => {
							const url = `${discovery.serviceUrls.admMsUrl}/v1/perfildeacesso/perfis?unificado=1&page=0&size=1`;
							return this.http.get(url);
						}),
						map((response: ApiResponseList<FuncionalidadeVO>) => {
							if (
								response.content &&
								response.content &&
								response.content.length > 0
							) {
								this.funcionalidadesAtivas.push("PERFIL_ACESSO_UNIFICADO");
							}
						}),
						switchMap(() => {
							const cache = this.getCacheFuncionalidadesInativas(
								this.chave,
								parseInt(this.empresaId)
							);
							if (cache) {
								return of({
									content: cache.funcionalidadesInativas,
									cache: true,
								});
							} else {
								const url = `${discovery.serviceUrls.recursoMsUrl}/v1/funcionalidade/inativas?empresa=${this.empresaId}&chave=${this.chave}`;
								return this.http.get(url);
							}
						}),
						map((response: ApiResponseList<FuncionalidadeVO>) => {
							if (response.content) {
								this.funcionalidadesInativas = response.content.map(
									(funcionalidade) => funcionalidade.name
								);
							}

							if (!response.cache) {
								this.setCacheFuncionalidadesInativas(
									this.chave,
									parseInt(this.empresaId),
									{
										chave: this.chave,
										empresa: parseInt(this.empresaId),
										data: new Date(),
										funcionalidadesInativas: response.content,
									}
								);
							}
						}),
						map(() => true),
						catchError((e) => {
							console.error("Error on prepare session from session_id ", e);
							return of(true);
						})
					);
			} else {
				console.log(
					`>>>> SPA SETUP ERROR: CASO O TOKEN NÃO SEJA FORNECIDO PELA URL A SESSION_ID DEVE SER INFORMADA.`
				);
				return of(false);
			}
		}
	}

	private addModulosPactobr() {
		if (this.verificarUsuarioLogadoPacto()) {
			this.modulosHabilitados.push(PlataformaModulo.NBIS);
		}
	}

	cacheKey(chave: string, empresa: number) {
		return `chave:${chave}:empresa:${empresa}`;
	}

	public get linkZwUrlFull(): string {
		return this.clientDiscoveryService.getUrlMap().zwUrlFull;
	}

	public montarChatMovDesk() {
		const base = this.clientDiscoveryService.getUrlMap().personagemMsUrl;
		const urlColab =
			this.tokenProvider === "tr"
				? `${base}/colaboradores/tr/${this.tokenColaboradorId}`
				: `${base}/colaboradores/${this.tokenColaboradorId}`;

		this.http.get(urlColab).subscribe((json: any) => {
			this.colaborador = json.content;
			localStorage.setItem("nameMoviDesk", this.colaborador.nome);
			this.financeiroEmpresas.forEach((obj, index) => {
				if (obj.empresazw === Number(this.empresaId)) {
					localStorage.setItem(
						"CodRefAdditional",
						String(obj.codigoFinanceiro)
					);
					localStorage.setItem(
						"OrganizationCodeReference",
						String(obj.codigoFinanceiro)
					);

					const codColaboradorMovidesk = String(
						obj.codigoFinanceiro + "-" + this.tokenColaboradorId
					);
					localStorage.setItem("CodeReference", codColaboradorMovidesk);
				}
			});
			this.colaborador.telefones.forEach((obj, index) => {
				localStorage.setItem("PhoneNumber", obj.number);
			});
			this.colaborador.emails.forEach((obj, index) => {
				localStorage.setItem("email", obj);
			});
		});

		this.initChatMovidesk();
	}

	private initChatMovidesk() {
		if (this.clientDiscoveryService.isUsarChatMovDesk()) {
			localStorage.setItem(
				"movidesk-idchage",
				this.clientDiscoveryService.getIdChatMovDesk()
			);
			this.movideskChatService.initConfigNotificarRecursoEmpresa(
				this.empresaId,
				this.loggedUser,
				this.chave,
				this.currentEmpresa.nome,
				this.currentEmpresa.estado,
				this.currentEmpresa.cidade,
				this.currentEmpresa.pais
			);
			this.movideskChatService.initMovidesk(
				this.clientDiscoveryService.getIdChatMovDesk(),
				false
			);
		}
	}

	public verificarUsuarioPacto(username: string): boolean {
		if (username) {
			return username.toUpperCase() === "PACTOBR";
		}
		return false;
	}

	public verificarUsuarioLogadoPacto(): boolean {
		if (this.loggedUser && this.loggedUser.username) {
			return this.loggedUser.username.toUpperCase() === "PACTOBR";
		}
		return false;
	}

	public notificarRecursoEmpresa(recursoNotificar: string) {
		this.notificarRecursoEmpresaService.empresaId = this.empresaId;
		this.notificarRecursoEmpresaService.loggedUser = this.loggedUser;
		this.notificarRecursoEmpresaService.chave = this.chave;
		this.notificarRecursoEmpresaService.nomeEmpresa = this.currentEmpresa.nome;
		this.notificarRecursoEmpresaService.estado = this.currentEmpresa.estado;
		this.notificarRecursoEmpresaService.cidade = this.currentEmpresa.cidade;
		this.notificarRecursoEmpresaService.pais = this.currentEmpresa.pais;
		this.notificarRecursoEmpresaService.notificarRecursoEmpresa(
			recursoNotificar
		);
	}

	public getModulesOrder(): Observable<any> {
		const base = this.clientDiscoveryService.getUrlMap().admMsUrl;

		if (base) {
			const url = `${base}/v1/modulo`;

			return this.http.get(url, {
				headers: {
					Authorization: `Bearer ${localStorage.getItem("apiToken")}`,
				},
			});
		} else {
			return of([]);
		}
	}

	consultaApresentarPactoStore(): Observable<{ erro?: string; retorno?: any }> {
		const dto = new OperacaoPactoStore("apresentarPactoStore");
		dto.empresa = this.params.empresaId;
		dto.usuario = this.params.usuarioZwId;
		const zwUrl = this.params.zwUrl;
		const url = `${zwUrl}/prest/pactostore`;
		const params: any = {
			chave: this.params.chave,
		};
		return this.http.post(url, JSON.stringify(dto), { params }).pipe(
			map((response: any) => {
				return { retorno: response.dados };
			}),
			catchError((error) => {
				return of({ erro: error.dados });
			})
		);
	}

	private obterApresentarPactoStore() {
		if (!this.treinoIndependente) {
			this.consultaApresentarPactoStore()
				.pipe(
					tap((result) => {
						if (result.retorno) {
							this.apresentarPactoStore = result.retorno;
						} else {
							this.apresentarPactoStore = false;
						}
					})
				)
				.subscribe();
		}
	}

	get treinoIndependente() {
		return this.params.treinoIndependente;
	}

	private paramsValid() {
		const valid = this.chave && this.empresaId && this.usuarioZwId;
		if (valid) {
			return true;
		} else {
			console.info(
				'O parametro "chave" deve ser informado para includes em aplicação externa.'
			);
			console.info(
				'O parametro "moduleId" deve ser informado com código do modulo.'
			);
			console.info(
				'O parametro "empresaId" deve ser informado e somente com 1 modulo para includes em aplicação externa.'
			);
			console.info(
				'O parametro "usuarioZwId" deve ser informado para includes em aplicação externa.'
			);
			return false;
		}
	}

	private obterEmpresaFinanceiro(
		params: SetupParams
	): Observable<EmpresaFinanceiro> {
		const oamdUrl = params.oamdUrl;
		let url;
		if (this.codigoFinanceiro != null) {
			url = `${oamdUrl}/prest/empresaFinanceiro/empresas/${this.codigoFinanceiro}`;
		} else {
			url = `${oamdUrl}/prest/empresaFinanceiro/${this.chave}/empresas/${this.empresaId}`;
		}
		return this.http.get(url).pipe(
			map((res: ApiOamdResponseSingle<EmpresaFinanceiro>) => {
				return res.return;
			})
		);
	}

	temModuloTreino(): boolean {
		if (this.modulosHabilitados) {
			return (
				this.modulosHabilitados.find((m) => m === "TR") !== undefined ||
				this.modulosHabilitados.find((m) => m === "NTR") !== undefined
			);
		}
		return false;
	}

	temModuloIA(): boolean {
		if (this.modulosHabilitados) {
			return this.modulosHabilitados.find((m) => m === "IA") !== undefined;
		}
		return false;
	}

	get codigoFinanceiro() {
		return this.params.codigoFinanceiro;
	}

	get usuarioZwId() {
		return this.params.usuarioZwId;
	}

	private obterUsuarioLogadoZw(params: SetupParams): Observable<UsuarioBase> {
		if (this.treinoIndependente) {
			const zwUrl = params.treinoUrl;
			const url = `${zwUrl}/prest/usuario/${this.chave}/find?id=${this.usuarioZwId}`;
			return this.http.get(url).pipe(
				map((usuario: UsuarioBase) => {
					return usuario;
				})
			);
		} else if (this.modulosHabilitados.includes(PlataformaModulo.SEC)) {
			const opt = {
				headers: {
					empresaId: this.empresaId,
				},
			};
			const zwBootUrl = params.zwBootUrl;
			return this.http.get(`${zwBootUrl}/adm/obter-usuario`, opt).pipe(
				map((response: any) => {
					const resp = this.cryptoService.decrypt(response.content);
					return JSON.parse(resp);
				})
			);
		} else {
			const zwUrl = params.zwUrl;
			const url = `${zwUrl}/prest/auth/usuario/${this.usuarioZwId}?chave=${this.chave}&empresa=${this.empresaId}`;
			return this.http.get(url).pipe(
				map((usuario: UsuarioBase) => {
					return usuario;
				})
			);
		}
	}

	get empresaFinanceiro(): EmpresaFinanceiro {
		return JSON.parse(sessionStorage.getItem("empresaFinanceiro"));
	}

	set empresaFinanceiro(empresaFinanceiro: EmpresaFinanceiro) {
		sessionStorage.setItem(
			"empresaFinanceiro",
			JSON.stringify(empresaFinanceiro)
		);
	}

	set params(params: SetupParams) {
		sessionStorage.setItem("params", JSON.stringify(params));
	}

	get params(): SetupParams {
		return JSON.parse(sessionStorage.getItem("params"));
	}

	get cclOamdUrl() {
		return this.params.oamdUrl;
	}

	get cclZwUrl() {
		return this.params.zwUrl;
	}

	temPermissaoAdm(codigoPermissao: string): boolean {
		let permitido = false;

		if (
			this.perfilUsuarioAdm &&
			this.perfilUsuarioAdm.perfilUsuario &&
			this.perfilUsuarioAdm.perfilUsuario.funcionalidades
		) {
			const funcionalidade =
				this.perfilUsuarioAdm.perfilUsuario.funcionalidades.filter(
					(funcionaliade) => {
						if (
							funcionaliade &&
							funcionaliade.referenciaFuncionalidade &&
							funcionaliade.referenciaFuncionalidade.trim().toLowerCase() ===
								codigoPermissao.trim().toLowerCase()
						) {
							return true;
						}
					}
				);

			if (funcionalidade && funcionalidade.length > 0) {
				permitido = true;
			}
		}

		return permitido;
	}

	private checkRede(discovery: ClientDiscoveryData, chave: string) {
		try {
			this.configsRede(discovery, chave).subscribe((response) => {
				this.configRedeEmpresa = response;
			});
		} catch (e) {
			this.configRedeEmpresa = {
				empresaLogadoIsFranqueadora: false,
				chaveFranqueadora: "",
				empresaLogadaNaoPossuiFranqueadora: false,
			};
		}
	}

	private configsRede(
		discovery: ClientDiscoveryData,
		chave: string
	): Observable<ConfigRedeEmpresa> {
		const oamdUrl = discovery.serviceUrls.oamdUrl;
		const url = `${oamdUrl}/prest/empresaFinanceiro/configsTreinoRede?`;
		return this.http.get(url, { params: { chaveZW: chave } }).pipe(
			map((result: any) => {
				const config = new ConfigRedeEmpresa();
				config.chaveFranqueadora = result.configsTreinoRede.chaveFranqueadora;
				config.empresaLogadoIsFranqueadora = config.chaveFranqueadora === chave;
				config.empresaLogadaNaoPossuiFranqueadora =
					!config.chaveFranqueadora || config.chaveFranqueadora === "";
				return config;
			})
		);
	}
}
