import { Injectable } from "@angular/core";
import {
	<PERSON>ttp<PERSON><PERSON>,
	<PERSON>ttp<PERSON><PERSON><PERSON>,
	<PERSON>ttpInterceptor,
	HttpRequest,
} from "@angular/common/http";

import { Observable } from "rxjs";

import { SessionService } from "./session.service";
import { ClientDiscoveryService } from "./client-discovery.service";
import { environment } from "../environments/environment";

const URLS_ADD_ID_EMPRESA_HEADERS = [
	"planoMsUrl",
	"treinoApiUrl",
	"admMsUrl",
	"zwBack",
	"cadastroAuxiliarUrl",
	"produtoMsUrl",
	"relatorioMsUrl", //
	"clubeVantagensMsUrl", //
	"acessoSistemaMsUrl",
	"admCoreUrl",
];

@Injectable({
	providedIn: "root",
})
export class AddIdEmpresaHeadersInterceptor implements HttpInterceptor {
	urlsAddHeaderImpresa: Array<any> = [];

	constructor(
		private sessionService: SessionService,
		private discover: ClientDiscoveryService
	) {}

	intercept(
		request: HttpRequest<any>,
		next: <PERSON>ttpHand<PERSON>
	): Observable<HttpEvent<any>> {
		if (
			(this.urlsAddHeaderImpresa.length === 0 ||
				this.urlsAddHeaderImpresa.length <
					URLS_ADD_ID_EMPRESA_HEADERS.length + 1) &&
			this.discover.getUrlMap()
		) {
			Object.keys(this.discover.getUrlMap()).forEach((key) => {
				const urlDiscovery = this.discover.getUrlMap()[key];
				if (
					URLS_ADD_ID_EMPRESA_HEADERS.includes(key) &&
					urlDiscovery &&
					!this.discover.getUrlMap().zwUrl.includes(urlDiscovery)
				) {
					this.urlsAddHeaderImpresa.push(urlDiscovery);
				}
			});
			this.urlsAddHeaderImpresa.push(environment.discoveryMsUrl);
		}
		if (this.allowedTarget(request)) {
			const empresaId = this.sessionService.empresaId
				? this.sessionService.empresaId
				: null;
			const requisicao = request.clone({
				headers: request.headers.set("empresaId", `${empresaId}`),
			});
			return next.handle(requisicao);
		} else {
			return next.handle(request);
		}
	}

	private allowedTarget(request: HttpRequest<any>): boolean {
		const url = this.urlsAddHeaderImpresa.filter((u) =>
			request.url.includes(u)
		);
		if (url.length > 0) {
			return request.url.includes(url[0]);
		}
		return false;
	}
}
