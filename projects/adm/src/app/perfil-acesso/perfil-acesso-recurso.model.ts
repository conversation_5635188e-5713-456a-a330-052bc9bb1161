export enum PerfilAcessoRecursoNome {
	CANCELAR_BALANCO = "cancelarbalanco",
	PLANO = "plano",
	HORARIO = "horario",
	PACOTE = "composicao",
	MODALIDADE = "modalidade",
	PRODUTO = "produto",
	CATEGORIA_PRODUTO = "categoriaproduto",
	OPERADORA_CARTAO = "operadoracartao",
	ABERTURA_META = "aberturameta",
	CATEGORIA = "categoria",
	COMPOSICAO = "composicao",
	CONTA_CORRENTE = "contacorrente",
	PERFIL_ACESSO = "perfilacesso",
	PLANO_TIPO = "planotipo",
	PERGUNTA = "pergunta",
	CONFIGURACAO_SISTEMA = "configuracaositema",
	CONTRATO = "contrato",
	LOCAL_ACESSO = "localacesso",
	PARENTESCO = "parentesco",
	CONVENIO_COBRANCA = "conveniocobranca",
	JUSTIFICATIVA_OPERACAO = "justificativaoperacao",
	PASSIVO = "passivo",
	EMPRESA = "empresa",
	TURMA = "turma",
	CONVENIO_DESCONTO = "conveniodesconto",
	RESPOSTA_PERGUNTA_CLIENTE = "respostaperguntacliente",
	ZW_MOBILE = "zwmobile",
	PESSOA = "pessoa",
	INDICE_FINANCEIRO_REAJUSTE_PRECO = "indicefinanceiroreajustepreco",
	CONFIGURACAO_CONVENIO = "configuracaoconvenio",
	ESTORNO_RECIBO = "estornorecibo",
	CIDADE = "cidade",
	VINCULO = "vinculo",
	INDICACAO = "indicacao",
	ZW_MOBILE_REDE = "zwmobilerede",
	CLIENTE_OBSERVACAO = "clienteobservacao",
	BI_OPERACAO_EXCECOES = "bioperacaoexececoes",
	QUESTIONARIO_CLIENTE = "questionariocliente",
	ESTORNO_MOV_PRODUTO = "estornomovproduto",
	EVENTO = "evento",
	BI_COBRANCA_CONVENIO = "bicobrancaconvenio",
	MODELO_ORCAMENTO = "modeloorcamento",
	ROTATIVIDADE_CRM = "rotatividadecrm",
	GRUPO_COLABORADOR = "grupocolaborador",
	GERADOR_CONSULTAS = "geradorconsultas",
	TIPO_RETORNO = "tiporetorno",
	CLASSIFICACAO = "classificacao",
	QUESTIONARIO_PERGUNTA = "questionariopergunta",
	MODELO_MENSAGEM = "modelomensagem",
	ENDERECO = "endereco",
	RESPOSTA_PERGUNTA = "respostapergunta",
	CONFIGURACAO_SISTEMA_CRM = "configuracaosistemacrm",
	BANCO = "banco",
	FAMILIAR = "familiar",
	AGENDA = "agenda",
	PROFISSAO = "profissao",
	MOV_PRODUTO = "movproduto",
	PAIS = "pais",
	BALANCO = "cadastrarbalanco", // aqui
	CLIENTE = "cliente",
	CLIENTE_GRUPO = "clientegrupo",
	COLABORADOR = "colaborador",
	GESTAO_REMESSAS = "gestaoremessas",
	BI_GESTAO_ACESSO = "bigestaoacesso",
	USUARIO = "usuario",
	USUARIOS = "usuarios",
	FERIADO = "feriado",
	AULA_AVULSA_DIARIA = "aulaavulsadiaria",
	ITEM_CAMPANHA = "itemcampanha",
	ORGANIZADOR_CARTEIRA = "organizadorcarteira",
	BI_VERIFICARCAO_CLIENTES = "biverificarcaoclientes",
	CUPOM_FISCAL = "cupomfiscal",
	QUESTIONARIO = "questionario",
	FORMA_PAGAMENTO = "formapagamento",
	TELEFONE = "telefone",
	LOG = "log",
	TIPO_REMESSA = "tiporemessa",
	DATA_ESTORNO_RECIBO = "dataestornorecibo",
	DESCONTO = "desconto",
	PLANO_TESTO_PADRAO = "planotextopadrao",
	AMBIENTE = "ambiente",
	NIVEL_TURMA = "nivelturma",
	BRINDE = "brinde",
	GRUPO = "grupo",
	TIPO_MODALIDADE = "tipomodalidade",
	PERGUNTA_CLIENTE = "perguntacliente",
	VENDA_AVULSA = "vendaavulsa",
	CAMAPANHA_DURACAO = "campanhaduracao",
	MOV_PAGAMENTO = "movpagamento",
	OBJECAO = "objecao",
	GRAU_INSTRUCAO = "grauinstrucao",
	MOV_PARCELA = "movparcela",
	CLIENTE_CLASSIFICACAO = "clienteclassificacao",
	EMAIL = "email",
	ESTORNO_CONTRATO = "estornocontrato",
	MOVIMENTO_CONTA_CORRENTE_CLIENTE = "movimentocontacorrentecliente",
	AUTORIZACAO_COBRANCA_CLIENTE = "autorizacaocobrancacliente",
	CONDICAO_PAGAMENTO = "condicaopagamento",
	ALTERAR_SITUACAO_PRODUTO_ESTOQUE = "alterarsituacaoprodutoestoque",
	CAMPANHA_CUPOM_DESCONTO = "campanhacupomdesconto",
}

export enum PerfilAcessoFuncionalidadeNome {
	LANCAMENTO_PRODUTO_COLETIVO = "lancamentoprodutocoletivo",
}

export enum PerfilRecursoPermissoTipo {
	CONSULTAR = "CONSULTAR",
	INCLUIR = "INCLUIR",
	EDITAR = "EDITAR",
	EXCLUIR = "EXCLUIR",
	TOTAL_EXCETO_EXCLUIR = "TOTAL_EXCETO_EXCLUIR",
	TOTAL = "TOTAL",
	INCLUIR_CONSULTAR = "INCLUIR_CONSULTAR",
}
