import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { NivelTurmaComponent } from "@adm/nivel-turma/nivel-turma.component";
import { NivelTurmaFormComponent } from "@adm/nivel-turma/nivel-turma-form/nivel-turma-form.component";

const routes: Routes = [
	{
		path: "",
		component: NivelTurmaComponent,
	},
	{
		path: "novo",
		component: NivelTurmaFormComponent,
	},
	{
		path: ":id",
		component: NivelTurmaFormComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class NivelTurmaRoutingModule {}
