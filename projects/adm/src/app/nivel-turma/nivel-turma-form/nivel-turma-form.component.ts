import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { CadastroAuxApiNivelTurmaService, NivelTurma } from "cadastro-aux-api";
import { AdmRestService } from "@adm/adm-rest.service";
import { SessionService } from "@base-core/client/session.service";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { ZwBootNivelMgbService, NivelMgb } from "adm-legado-api";

@Component({
	selector: "adm-nivel-turma-form",
	templateUrl: "./nivel-turma-form.component.html",
	styleUrls: ["./nivel-turma-form.component.scss"],
})
export class NivelTurmaFormComponent implements OnInit {
	form: FormGroup;
	nivelTurma: NivelTurma = new NivelTurma();
	id: string;
	niveisDisponiveis = [];
	isEdicao = false;
	urlLog: string;
	apresentarBtnSyncMGB = false;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private admRest: AdmRestService,
		private activatedRoute: ActivatedRoute,
		private configCache: TreinoConfigCacheService,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private nivelTurmaService: CadastroAuxApiNivelTurmaService,
		private cd: ChangeDetectorRef,
		private nivelMgbService: ZwBootNivelMgbService
	) {}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id && this.id !== "novo";

		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			descricao: ["", [Validators.required, Validators.maxLength(45)]],
			codigomgb: [null],
		});
		this.loadIntegracaoMgb();
		if (this.isEdicao) {
			this.carregarNivelTurma();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`log/NIVELTURMA/${this.id}`
			);
		}
	}

	get tituloFormulario(): string {
		return this.isEdicao ? "Editar Nível de Turma" : "Novo Nível de Turma";
	}

	loadIntegracaoMgb() {
		const mgb: any = this.configCache.configuracoesIntegracoesListaMGB;
		const find = mgb.find(
			(x) => x.empresa === Number(this.sessionService.empresaId)
		);
		const tokenMbg = find ? find.token : null;
		this.apresentarBtnSyncMGB = tokenMbg !== null && tokenMbg.length > 0;
		if (this.apresentarBtnSyncMGB) {
			this.carregarNiveisMgb();
		}
	}

	carregarNiveisMgb() {
		this.nivelMgbService.consultar().subscribe(
			(response) => {
				if (response && response.content) {
					this.niveisDisponiveis = response.content.map((nivel: NivelMgb) => ({
						publicId: nivel.publicId,
						namePTBR: nivel.nome,
					}));
				} else {
					this.niveisDisponiveis = [];
				}
				this.cd.detectChanges();
			},
			(error) => {
				if (
					error &&
					error.error &&
					error.error.meta &&
					error.error.errorMessage
				) {
					this.notificationService.error(error.error.meta.errorMessage);
				} else {
					this.notificationService.warning("Erro ao carregar níveis MGB.");
				}
			}
		);
	}

	carregarNivelTurma() {
		this.nivelTurmaService.find(this.id).subscribe(
			(response) => {
				this.nivelTurma = response.content;
				this.form.patchValue(this.nivelTurma);
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar nível de turma");
				this.voltarListagem();
			}
		);
	}

	voltarListagem() {
		this.router.navigate(["adm", "nivel-turma"]);
	}

	salvar() {
		if (this.form.invalid) {
			this.notificationService.error("Preencha todos os campos obrigatórios");
			return;
		}

		const dadosFormulario = this.form.getRawValue();
		if (dadosFormulario.descricao) {
			dadosFormulario.descricao = dadosFormulario.descricao.toUpperCase();
		}

		Object.assign(this.nivelTurma, dadosFormulario);

		this.nivelTurmaService.save(this.nivelTurma).subscribe(
			(response) => {
				this.notificationService.success(
					this.isEdicao
						? "Nível de turma atualizado com sucesso!"
						: "Nível de turma cadastrado com sucesso!"
				);
				this.voltarListagem();
			},
			(error) => {
				const errorMessage =
					error &&
					error.error &&
					error.error.meta &&
					error.error.meta.messageValue
						? error.error.meta.messageValue
						: "Erro ao salvar nível de turma.";
				this.notificationService.error(errorMessage);
			}
		);
	}

	novo() {
		this.form.reset();
		this.nivelTurma = new NivelTurma();
		this.isEdicao = false;
		this.cd.detectChanges();
	}
}
