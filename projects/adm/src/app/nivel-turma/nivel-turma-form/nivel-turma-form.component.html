<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@nivel-turma:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							formControlName="descricao"
							placeholder="Digite a descrição do nível de turma" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Nível MGB -->
			<div class="form-row-centered" *ngIf="apresentarBtnSyncMGB">
				<div class="label-column">
					<label>Nível MGB:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="niveisDisponiveis"
							[useFullOption]="false"
							[placeholder]="'Selecione o nível MGB'"
							[valueKey]="'publicId'"
							[nameKey]="'namePTBR'"
							[addEmptyOption]="true"
							[formControl]="form.get('codigomgb')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				titulo="Log nível de turma"
				[url]="urlLog"
				class="mr-2"></pacto-log>

			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Cancelar
			</button>
			<button
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
