<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@nivel-turma:modulo"
	i18n-pageTitle="@@nivel-turma:title"
	modulo="Administrativo"
	pageTitle="Nível de Turma">
	<div class="table-wrapper">
		<pacto-relatorio
			*ngIf="table"
			#tableNivelTurma
			(btnAddClick)="novoNivelTurma()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editNivelTurma($event)"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="nivel-turma"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@nivel-turma:filtro-situacao" xingling="filtro-situacao">
		Situação
	</span>
	<span i18n="@@nivel-turma:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@nivel-turma:desativado" xingling="inativo">Inativo</span>
	<span i18n="@@TOOLTIP_EDIT" xingling="TOOLTIP_EDIT">Editar</span>
	<span i18n="@@TOOLTIP_DELETE" xingling="TOOLTIP_DELETE">Excluir</span>
	<span i18n="@@DELETED" xingling="DELETED">Registro excluído com sucesso</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@nivel-turma:columnCodigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@nivel-turma:columnDescricao">Descrição</span>
</ng-template>
<ng-template #columnNivelMgb let-item="item">
	{{ obterNomeNivelMgb(item.codigomgb) }}
</ng-template>
