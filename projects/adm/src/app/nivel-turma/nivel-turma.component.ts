import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
	GridFilterConfig,
	GridFilterType,
	ConfirmDialogDeleteComponent,
} from "ui-kit";
import { Api, SessionService } from "sdk";
import { CadastroAuxApiNivelTurmaService } from "cadastro-aux-api";
import { AdmRestService } from "../adm-rest.service";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { ZwBootNivelMgbService, NivelMgb } from "adm-legado-api";

@Component({
	selector: "adm-nivel-turma",
	templateUrl: "./nivel-turma.component.html",
	styleUrls: ["./nivel-turma.component.scss"],
})
export class NivelTurmaComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnNivelMgb", { static: true })
	columnNivelMgb: TemplateRef<any>;
	@ViewChild("tableNivelTurma", { static: false })
	tableNivelTurma: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	apresentarBtnSyncMGB = false;
	niveisDisponiveis: Array<{ publicId: string; namePTBR: string }> = [];

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private nivelTurmaService: CadastroAuxApiNivelTurmaService,
		private ngbModal: NgbModal,
		private configCache: TreinoConfigCacheService,
		private sessionService: SessionService,
		private nivelMgbService: ZwBootNivelMgbService
	) {}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoNivelTurma() {
		this.router.navigate(["adm", "nivel-turma", "novo"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editNivelTurma") {
			this.editNivelTurma(event.row);
		} else if (event.iconName === "deleteNivelTurma") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este nível de turma?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteNivelTurma(event.row);
				}
			})
			.catch((error) => {});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.loadIntegracaoMgb();
		if (!this.apresentarBtnSyncMGB) {
			this.initTable();
			this.cd.detectChanges();
		}
	}

	loadIntegracaoMgb() {
		const mgb: any = this.configCache.configuracoesIntegracoesListaMGB;
		const find = mgb.find(
			(x) => x.empresa === Number(this.sessionService.empresaId)
		);
		const tokenMbg = find ? find.token : null;
		this.apresentarBtnSyncMGB = tokenMbg !== null && tokenMbg.length > 0;
		this.carregarNiveisMgb();
	}

	ngAfterViewInit() {}

	editNivelTurma(nivelTurma) {
		this.router.navigate(["adm", "nivel-turma", nivelTurma.codigo]);
	}

	deleteNivelTurma(row: any) {
		this.nivelTurmaService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				this.tableNivelTurma.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	private initTable() {
		const configColumns: any[] = [
			{
				nome: "codigo",
				titulo: "Código",
				visible: true,
				ordenavel: true,
			},
			{
				nome: "descricao",
				titulo: "Descrição",
				visible: true,
				ordenavel: true,
			},
		];
		if (this.apresentarBtnSyncMGB) {
			configColumns.push({
				nome: "codigomgb",
				titulo: "Nível MGB",
				visible: true,
				celula: this.columnNivelMgb,
				ordenavel: true,
			});
		}
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrlCadAux("nivel-turma"),
				quickSearch: true,
				logUrl: this.admRest.buildFullUrlCadAux(`log/NIVELTURMA`),
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: configColumns,
				actions: [
					{
						nome: "editNivelTurma",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
						actionFn: (row) => this.editNivelTurma(row),
					},
					{
						nome: "deleteNivelTurma",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
						actionFn: (row) => this.deleteNivelTurma(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	obterNomeNivelMgb(codigomgb: string): string {
		if (!codigomgb || !this.niveisDisponiveis.length) {
			return codigomgb || "";
		}
		const nivel = this.niveisDisponiveis.find((n) => n.publicId === codigomgb);
		return nivel ? nivel.namePTBR : codigomgb;
	}

	carregarNiveisMgb() {
		this.nivelMgbService.consultar().subscribe(
			(response) => {
				if (response && response.content) {
					this.niveisDisponiveis = response.content.map((nivel: NivelMgb) => ({
						publicId: nivel.publicId,
						namePTBR: nivel.nome,
					}));
				} else {
					this.niveisDisponiveis = [];
				}
				this.initTable();
				this.cd.detectChanges();
			},
			(error) => {
				if (
					error &&
					error.error &&
					error.error.meta &&
					error.error.errorMessage
				) {
					this.notificationService.error(error.error.meta.errorMessage);
				} else {
					this.notificationService.warning("Erro ao carregar níveis MGB.");
				}
				this.initTable();
			}
		);
	}
}
