<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@movimento-produto:modulo"
	i18n-pageTitle="@@movimento-produto:title"
	modulo="Administrativo"
	pageTitle="Movimento do Produto">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableMovimentoProduto
			[customActions]="customFilter"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editMovimentoProduto($event)"
			[enableDs3]="true"
			[showBtnAdd]="false"
			[showShare]="false"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="movimento-produto"></pacto-relatorio>
	</div>

	<ng-template #customFilter>
		<form [formGroup]="formGroup">
			<div class="row">
				<div class="col-md-6">
					<ds3-form-field>
						<ds3-input-date
							id="movimento-produto-data-inicio"
							dateType="datepicker"
							[position]="'middle-left'"
							[control]="formGroup.get('dataInicio')"
							ds3Input></ds3-input-date>
					</ds3-form-field>
				</div>
				<div class="col-md-6">
					<ds3-form-field>
						<ds3-input-date
							id="movimento-produto-data-fim"
							dateType="datepicker"
							[position]="'middle-left'"
							[control]="formGroup.get('dataFim')"
							ds3Input></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>
		</form>
	</ng-template>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@movimento-produto:filtro-data" xingling="filtro-data">
		Data de Lançamento
	</span>
	<span i18n="@@tooltip:edit" xingling="TOOLTIP_EDIT">Editar</span>
	<span i18n="@@tooltip:delete" xingling="TOOLTIP_DELETE">Excluir</span>
</pacto-traducoes-xingling>

<!-- Column Templates -->
<ng-template #columnCodigo>
	<span i18n="@@movimento-produto:codigo">Código</span>
</ng-template>

<ng-template #columnContrato>
	<span i18n="@@movimento-produto:contrato">Contrato</span>
</ng-template>

<ng-template #columnResponsavel>
	<span i18n="@@movimento-produto:responsavel">Responsável</span>
</ng-template>

<ng-template #columnProduto>
	<span i18n="@@movimento-produto:produto">Produto</span>
</ng-template>

<ng-template #columnCliente>
	<span i18n="@@movimento-produto:cliente">Cliente</span>
</ng-template>

<ng-template #columnEmpresa>
	<span i18n="@@movimento-produto:empresa">Empresa</span>
</ng-template>
