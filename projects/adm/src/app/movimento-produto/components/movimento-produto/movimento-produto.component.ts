import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ZwBootMovimentoProdutoService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { Api, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import moment from "moment";

@Component({
	selector: "adm-movimento-produto",
	templateUrl: "./movimento-produto.component.html",
	styleUrls: ["./movimento-produto.component.scss"],
})
export class MovimentoProdutoComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnContrato", { static: true })
	columnContrato: TemplateRef<any>;
	@ViewChild("columnResponsavel", { static: true })
	columnResponsavel: TemplateRef<any>;
	@ViewChild("columnProduto", { static: true }) columnProduto: TemplateRef<any>;
	@ViewChild("columnCliente", { static: true }) columnCliente: TemplateRef<any>;
	@ViewChild("columnEmpresa", { static: true }) columnEmpresa: TemplateRef<any>;
	@ViewChild("tableMovimentoProduto", { static: false })
	tableMovimentoProduto: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	formGroup: FormGroup;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private admRest: AdmRestService,
		private fb: FormBuilder
	) {}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		this.editMovimentoProduto(event.row);
	}

	ngAfterViewInit() {}

	ngOnInit() {
		this.initFormGroup();
		this.initTable();
		this.cd.detectChanges();
	}

	editMovimentoProduto(movimentoProduto) {
		this.router.navigate(["adm", "movimento-produto", movimentoProduto.codigo]);
	}

	paramAdapterFn = (params: any) => {
		params = params || {};
		// Adicionar filtros de data se estiverem preenchidos
		if (this.formGroup) {
			const dataInicio = this.formGroup.get("dataInicio").value;
			const dataFim = this.formGroup.get("dataFim").value;

			if (dataInicio) {
				params.dtInicio = moment(dataInicio).format("YYYY-MM-DD");
			}

			if (dataFim) {
				params.dtFim = moment(dataFim).format("YYYY-MM-DD");
			}
		}
		return params;
	};

	private initTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlZwBack("movimento-produto"),
			paramAdapterFn: this.paramAdapterFn,
			quickSearch: true,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: true,
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "contrato",
					titulo: this.columnContrato,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "responsavel",
					titulo: this.columnResponsavel,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "produto",
					titulo: this.columnProduto,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "cliente",
					titulo: this.columnCliente,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "empresa",
					titulo: this.columnEmpresa,
					visible: true,
					ordenavel: true,
				},
			],
		});
	}
	private initFormGroup() {
		// Primeiro dia do mês atual
		const primeiroDiaDoMes = moment().startOf("month").toDate();
		// Último dia do mês atual
		const ultimoDiaDoMes = moment().endOf("month").toDate();

		this.formGroup = this.fb.group({
			dataInicio: new FormControl(primeiroDiaDoMes),
			dataFim: new FormControl(ultimoDiaDoMes),
		});

		// Adicionar listeners para filtrar automaticamente quando as datas mudarem
		this.formGroup.get("dataInicio").valueChanges.subscribe(() => {
			this.filtrarAutomaticamente();
		});

		this.formGroup.get("dataFim").valueChanges.subscribe(() => {
			this.filtrarAutomaticamente();
		});
	}

	private filtrarAutomaticamente() {
		// Força a atualização da tabela com os novos filtros
		if (this.tableMovimentoProduto) {
			this.tableMovimentoProduto.reloadData();
		}
	}
}
