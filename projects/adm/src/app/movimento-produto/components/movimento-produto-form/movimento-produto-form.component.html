<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@movimento-produto:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Empresa -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Empresa:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="empresa" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Cliente -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Cliente:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="cliente" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Contrato -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Contrato:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="contrato" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Produto -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="produto" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricao" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Quantidade -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Quantidade:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="number"
							ds3Input
							readonly
							formControlName="quantidade"
							min="1" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Preço Unitário -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Preço Unitário:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							[options]="{
								align: 'left',
								prefix: 'R$ ',
								thousands: '.',
								decimal: ','
							}"
							currencyMask
							ds3Input
							formControlName="precoUnitario"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Valor Desconto -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Valor Desconto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							readonly
							[options]="{
								align: 'left',
								prefix: 'R$ ',
								thousands: '.',
								decimal: ','
							}"
							currencyMask
							ds3Input
							formControlName="desconto" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Total Final -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Total Final:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							[options]="{
								align: 'left',
								prefix: 'R$ ',
								thousands: '.',
								decimal: ','
							}"
							currencyMask
							ds3Input
							placeholder="R$ 0,00"
							formControlName="totalFinal"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Data de Lançamento -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Data de Lançamento:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							formControlName="dataLancamento"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered" *ngIf="isEdicao">
				<div class="label-column">
					<label>Responsável Lançamento:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							formControlName="responsavel"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Mês Referência:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							formControlName="mesReferencia"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Ano Referência:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							formControlName="anoReferencia"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Data de Início de Vigência:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="dataInicio" readonly />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Data Final de Vigência:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="dataFim" readonly />
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Voltar para a lista
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
