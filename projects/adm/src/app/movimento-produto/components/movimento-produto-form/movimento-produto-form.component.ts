import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { LoaderService } from "ui-kit";
import {
	ZwBootMovimentoProdutoService,
	MovimentoProduto,
} from "adm-legado-api";
import { AdmCoreApiEmpresaService } from "adm-core-api";

@Component({
	selector: "adm-movimento-produto-form",
	templateUrl: "./movimento-produto-form.component.html",
	styleUrls: ["./movimento-produto-form.component.scss"],
})
export class MovimentoProdutoFormComponent implements OnInit {
	form: FormGroup;
	movimentoProduto: MovimentoProduto = new MovimentoProduto();
	id: string;
	empresas = [];
	clientes = [];
	produtos = [];
	contratos = [];
	isEdicao = false;
	tituloFormulario = "Movimento do Produto";

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private loaderService: LoaderService,
		private movimentoProdutoService: ZwBootMovimentoProdutoService,
		private empresaService: AdmCoreApiEmpresaService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id;
		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			empresa: [null],
			cliente: [null],
			contrato: [null],
			produto: [null],
			descricao: [{ value: "", disabled: true }],
			quantidade: [1],
			precoUnitario: [{ value: 0, disabled: true }],
			desconto: [{ value: 0, disabled: true }],
			totalFinal: [{ value: 0, disabled: true }],
			dataLancamento: [null],
			dataInicio: [null],
			mesReferencia: [null],
			anoReferencia: [null],
			dataFim: [null],
			responsavel: [{ value: "", disabled: true }],
		});

		if (this.isEdicao) {
			this.carregarMovimentoProduto();
		}
	}

	carregarMovimentoProduto() {
		this.movimentoProdutoService.find(this.id).subscribe(
			(response) => {
				this.movimentoProduto = response.content;
				this.form.patchValue(this.movimentoProduto);
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar movimento do produto");
			}
		);
	}

	calcularTotalFinal() {
		const quantidadeControl = this.form.get("quantidade");
		const precoUnitarioControl = this.form.get("precoUnitario");
		const valorDescontoControl = this.form.get("valorDesconto");
		const totalFinalControl = this.form.get("totalFinal");

		if (
			!quantidadeControl ||
			!precoUnitarioControl ||
			!valorDescontoControl ||
			!totalFinalControl
		) {
			return;
		}

		const quantidade = quantidadeControl.value || 0;
		const precoUnitario = precoUnitarioControl.value || 0;
		const valorDesconto = valorDescontoControl.value || 0;

		if (quantidade && precoUnitario) {
			const total = quantidade * precoUnitario - valorDesconto;
			totalFinalControl.setValue(total);
		}
	}

	voltarListagem() {
		this.router.navigate(["adm", "movimento-produto"]);
	}
}
