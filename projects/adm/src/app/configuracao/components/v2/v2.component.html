<pacto-cat-card-plain class="config-card-plain">
	<div class="config-wrapper pacto-shadow">
		<div class="functions d-flex">
			<pacto-cat-form-input
				[id]="'configSearchInput'"
				[icon]="'pct pct-search icon-search-bar'"
				[placeholder]="'Busca por uma configuração, exemplo: Catraca'"
				[type]="'text'"
				[control]="searchControl"></pacto-cat-form-input>

			<div class="functions-actions">
				<pacto-log
					[table]="true"
					ds3-outlined-button
					#logComponent
					[dataGridConfig]="logDataGridConfig"
					[dataGridFilterConfig]="logDataGridFilterConfig"></pacto-log>
			</div>
			<ng-template #colunaValorAnterior let-item="item">
				<span class="column-value" (click)="onClickColumnValue(item)">
					{{ valueTransFromBolleanToString(item?.valorCampoAnterior) }}
				</span>
			</ng-template>

			<ng-template #colunaValorAlterado let-item="item">
				<span class="column-value" (click)="onClickColumnValue(item)">
					{{ valueTransFromBolleanToString(item?.valorCampoAlterado) }}
				</span>
			</ng-template>
		</div>
		<div *ngIf="searching" class="content-display col-12">
			<div class="content">
				<div *ngIf="groupedInputs && groupedInputs.length > 0">
					<div
						*ngFor="let grupo of groupedInputs; let i = index"
						class="subgroup-title">
						<div class="modulo-title p-2">
							{{ grupo.moduloNome }} -> {{ grupo.subGrupoNome }}
						</div>
						<p *ngIf="grupo.descricao" class="pl-4 custom-description">
							{{ grupo.descricao }}
						</p>
						<pacto-form-configuracoes-builder
							[items]="grupo.items"
							[customClass]="'pl-2'"></pacto-form-configuracoes-builder>
					</div>
				</div>
				<div *ngIf="!inputs || inputs.length === 0" class="empty-state">
					<img src="assets/images/lupa.svg" />
					<span class="title">
						Não encontramos resultados para a sua busca!
					</span>
					<span class="subtitle">
						Por favor, use outros termos de pesquisa e tente novamente.
					</span>
				</div>
				<ng-container *ngTemplateOutlet="floatingSaveBox"></ng-container>
			</div>
		</div>

		<div *ngIf="!searching" class="config-content">
			<div class="tabs col-2">
				<div class="module" *ngFor="let modulo of modulos">
					<ng-container *ngIf="modulo.enabled">
						<div class="module-name">
							<span>{{ modulo.nome }}</span>
						</div>
						<div
							*ngFor="let subGrupo of filterSubGrupsByModule(modulo)"
							[class]="'tab ' + tabId(subGrupo)"
							[id]="'tab-' + tabId(subGrupo)"
							[class.active]="selectedTab === tabId(subGrupo)"
							[class.invalid]="isInvalidTab(subGrupo)"
							(click)="onClickSubGrupo(subGrupo)">
							{{ subGrupo.nome }}

							<i class="pct pct-alert-triangle"></i>
						</div>
					</ng-container>
				</div>
			</div>
			<div class="content-display col-10" #contentDisplay>
				<div *ngIf="!loading" class="subgroup-title">
					{{ selectedSubGroup?.nome }}
					<p *ngIf="selectedSubGroup?.descricao" class="custom-description">
						{{ selectedSubGroup?.descricao }}
					</p>
					<div class="subtitulo-subgroup">
						{{ selectedSubGroup?.subtitulo }}
					</div>
					<div
						class="subtitulo-empresa-subgroup"
						*ngIf="empresaSelecionada && mostrarUnidade">
						Empresa : {{ empresaSelecionada }}
						<i
							class="pct pct-repeat"
							style="cursor: pointer"
							(click)="mudarDeEmpresa()"></i>
					</div>
				</div>
				<div class="content">
					<pacto-form-configuracoes-builder
						[search]="searching"
						[items]="inputs"></pacto-form-configuracoes-builder>
				</div>
				<ng-container *ngTemplateOutlet="floatingSaveBox"></ng-container>
			</div>
		</div>
	</div>
</pacto-cat-card-plain>

<ng-template #floatingSaveBox>
	<div *ngIf="!loading" class="actions pacto-shadow">
		<div class="error" *ngIf="!valid">
			<i class="pct pct-alert-triangle"></i>
			<span class="cor-act-dft-risk03">
				Existe campos inválidos no módulo {{ invalidModuleNames }}. Revise antes
				de salvar.
			</span>
		</div>
		<pacto-cat-button
			label="Voltar ao topo"
			[size]="'LARGE'"
			type="OUTLINE"
			[disabled]="loading"
			(click)="goTo()"></pacto-cat-button>
		<pacto-cat-button
			label="Salvar"
			[size]="'LARGE'"
			type="PRIMARY"
			[disabled]="!valid || loading"
			(click)="save()"></pacto-cat-button>
	</div>
</ng-template>

<ng-template #colunaValorAnterior let-item="item">
	<span class="column-value" (click)="onClickColumnValue(item)">
		{{ valueTransFromBolleanToString(item?.valorCampoAnterior) }}
	</span>
</ng-template>

<ng-template #colunaValorAlterado let-item="item">
	<span class="column-value" (click)="onClickColumnValue(item)">
		{{ valueTransFromBolleanToString(item?.valorCampoAlterado) }}
	</span>
</ng-template>
