import {
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import moment from "moment";
import { SnotifyService } from "ng-snotify";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import { debounceTime, distinctUntilChanged } from "rxjs/operators";
import { ClientDiscoveryService, SessionService } from "sdk";
import { TreinoApiConfiguracoesTreinoService } from "treino-api";
import {
	GridFilterConfig,
	GridFilterType,
	LoaderService,
	LogComponent,
	ModalLogDefaultComponent,
	PactoDataGridConfig,
	TableData,
} from "ui-kit";
import { ConfigModuloSubGroup } from "../config/model/config-module-subgroup.model";
import { ConfigModulo } from "../config/model/config-modulo.model";
import { ConfigService } from "../config/config.service";
import { SelectEmpresaDialogComponent } from "../dialog/empresa/select-empresa.dialog.component";
import { ConfigItem, ConfigItemBase } from "../inputs/form-configuracoes.model";
import { ConfigIaService } from "../config/config-ia.service";

@Component({
	selector: "adm-v2",
	templateUrl: "./v2.component.html",
	styleUrls: ["./v2.component.scss"],
})
export class V2Component implements OnInit {
	constructor(
		private router: Router,
		private configService: ConfigService,
		private loaderService: LoaderService,
		private discoveryService: ClientDiscoveryService,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private ngbModal: NgbModal,
		private cd: ChangeDetectorRef,
		private navigationService: LayoutNavigationService,
		private treinoConfigService: TreinoApiConfiguracoesTreinoService,
		private configIaService: ConfigIaService
	) {}

	get modulos(): Array<ConfigModulo> {
		return this.configService && this.configService.modulos;
	}

	get subGroups(): Array<ConfigModuloSubGroup> {
		return this.configService && this.configService.subGroups;
	}

	get invalidModules(): ConfigModulo[] {
		return this.configService.invalidModules();
	}

	get invalidModuleNames(): string {
		return this.invalidModules.map((module) => module.nome).join(", ");
	}

	get valid(): boolean {
		return this.configService.valid();
	}

	get dirty(): boolean {
		return this.configService.dirty();
	}

	get logEntities(): Array<string> {
		return [
			"configuracaosistema",
			"configuracaosistemacadastrocliente",
			"cadastrodinamicoitem",
			"emailsrecorrencia",
			"configuracaosistemacrm",
			"configuracaoemailfechamentometa",
			"termospam",
			"configuracaodiasposvenda",
			"configuracaodiasmetas",
			"faixahorarioacessocliente",
			"tiposvinculosfase",
			"configuracaofinanceiro",
		];
	}

	static me: V2Component;
	@ViewChild("translator", { static: true }) translator;
	@ViewChild("colunaValorAnterior", { static: true }) colunaValorAnterior;
	@ViewChild("colunaValorAlterado", { static: true }) colunaValorAlterado;
	@ViewChild("logComponent", { static: true }) logComponent: LogComponent;
	@ViewChild("contentDisplay", { static: true }) contentDisplay: ElementRef;

	selectedTab: string;
	selectedSubGroup: ConfigModuloSubGroup;
	inputs: ConfigItemBase[];
	searchControl = new FormControl("");
	searching: boolean = false;
	loading: boolean = true;
	logDataGridConfig: PactoDataGridConfig;
	fieldNamesCache: Map<string, string> = new Map<string, string>();
	logDataGridFilterConfig: GridFilterConfig;
	recurso = "CONFIGURACOES";
	recursoPadraoEmpresa: boolean = true;
	logTreinoConfig: any[] = [];
	empresaSelecionada: string = "";
	mostrarUnidade: boolean = true;
	possuiTodasAsPermissoesConfig = false;
	groupedInputs = [];

	ngOnInit() {
		this.carregarRecursoPadraoEmpresa();
		this.fecharMenu();
		V2Component.me = this;
		this.logDataGridConfig = this.getLogDataGridConfig();
		this.logDataGridFilterConfig = this.getLogDataGridFilterConfig();
		if (!this.loading) {
			this.searchControl.disable();
			this.loaderService.initForce();
		}
		this.configService.loaded.subscribe((loaded) => {
			this.selectInitTabs();
			this.loaderService.stopForce();
			this.loading = false;
			this.searchControl.enable();
			this.cd.detectChanges();
		});
		this.configService.reloaded.subscribe((loaded) => {
			this.loaderService.stopForce();
			this.selectInitTabsFaseIa();
			this.loading = false;
			this.searchControl.enable();
			this.cd.detectChanges();
		});
		this.cd.detectChanges();
		this.configService.detectChanges.subscribe(() => {
			this.cd.detectChanges();
		});

		this.searchControl.valueChanges
			.pipe(debounceTime(300), distinctUntilChanged())
			.subscribe((newValue) => {
				if (
					!newValue ||
					newValue.trim() === "" ||
					newValue === undefined ||
					newValue === null
				) {
					this.searching = false;
					this.configService.searchText = null;
					this.selectSubGroup(this.selectedSubGroup);
					this.cd.detectChanges();
				} else {
					this.configService.searchText = newValue;
					this.inputs = this.configService.getInputsBySearch(newValue);
					this.searching = true;
					this.groupedInputs = this.groupBySubGrupoComModulo(this.inputs);
					this.cd.detectChanges();
				}
			});
		this._initConfigByEmpresa();
		this.cd.detectChanges();
	}

	groupBySubGrupoComModulo(items: any[]) {
		const grouped = new Map<
			number,
			{
				subGrupoCodigo: number;
				subGrupoNome: string;
				descricao?: string;
				moduloCodigo: number;
				moduloNome: string;
				items: any[];
			}
		>();

		for (const item of items) {
			const key = item.subGrupoCodigo;

			if (!grouped.has(key)) {
				grouped.set(key, {
					subGrupoCodigo: item.subGrupoCodigo,
					subGrupoNome: item.subGrupoNome,
					descricao: item.descriptionMode, // pode ajustar se for outra propriedade
					moduloCodigo: item.moduloCodigo,
					moduloNome: item.moduloNome,
					items: [item],
				});
			} else {
				grouped.get(key)!.items.push(item);
			}
		}

		return Array.from(grouped.values());
	}

	fecharMenu() {
		try {
			const menu = document.getElementById("sidebar-menu-toggle");
			if (menu.classList.contains("opened")) {
				setTimeout(() => {
					menu.click();
				});
			}
		} catch (e) {}
	}

	carregarRecursoPadraoEmpresa() {
		this.admCoreApiNegociacaoService
			.recursoPadraoEmpresa(this.recurso)
			.subscribe(
				(response) => {
					this.recursoPadraoEmpresa = response;
				},
				(httpErrorResponse) => {
					console.log(httpErrorResponse);
					this.recursoPadraoEmpresa = false;
					this.cd.detectChanges();
				}
			);
	}

	getLogDataGridFilterConfig(): GridFilterConfig {
		return {
			filters: [
				{
					name: "operacoes",
					label: "Operação",
					type: GridFilterType.MANY,
					options: [
						{ value: "INCLUSAO", label: "Inclusão" },
						{ value: "ALTERACAO", label: "Alteração" },
						{ value: "EXCLUSAO", label: "Exclusão" },
					],
				},
				{
					name: "dataInicio",
					label: "Data de inicio",
					type: GridFilterType.DATE_POINT,
					transformDateString: true,
				},
				{
					name: "dataFim",
					label: "Data final",
					type: GridFilterType.DATE_POINT,
					transformDateString: true,
				},
			],
		};
	}

	ngAfterViewInit() {
		if (
			(this.selectedTab === null || this.selectedTab === undefined) &&
			this.loading === true
		) {
			this.selectInitTabs();
		}
	}

	selectInitTabs() {
		this.selectSubGroup(ConfigModuloSubGroup.ADM_BASICO);
		this.loading = false;
		this.cd.detectChanges();
	}

	//log da configuração de treino
	carregarLogConfiguracaoTreino(filters: any = {}) {
		this.logDataGridConfig.dataAdapterFn = (serverData) => {
			const treinoContent = this.logTreinoConfig.map((res) => {
				return {
					dataAlteracao: moment(res.dia, "DD/MM/YYYY - HH:mm:ss").toISOString(),
					nomeEntidade: res.chave,
					nomeEntidadeDescricao: res.descricao,
					operacao: res.operacao,
					nomeCampo: res.chave.toLowerCase().trim(),
					responsavelAlteracao: res.usuario,
					alteracoes: res.alteracoes,
					valorCampoAlterado: this.tranformHtmlToText(
						res.alteracoes
							.map((alteracao) => alteracao.valorAlterado)
							.join(", ")
					),
					valorCampoAnterior: this.tranformHtmlToText(
						res.alteracoes
							.map((alteracao) => alteracao.valorAnterior)
							.join(", ")
					),
				};
			});
			const logTreinoPaginado = this.paginarLogConfiguracaoTreino(
				this.logDataGridConfig.state,
				treinoContent
			);
			serverData.content = [...serverData.content, ...logTreinoPaginado];
			serverData.content.sort((a, b) => {
				return (
					new Date(b.dataAlteracao).getTime() -
					new Date(a.dataAlteracao).getTime()
				);
			});
			return serverData;
		};
	}

	private paginarLogConfiguracaoTreino(state, treinoContent) {
		const { paginaNumero, paginaTamanho } = state;
		const inicio = paginaNumero * paginaTamanho;
		const fim = inicio + paginaTamanho;
		return treinoContent.slice(inicio, fim);
	}

	isLoading(): boolean {
		return this.loaderService.forcing || this.loading;
	}

	selectSubGroup(subgrupo: ConfigModuloSubGroup) {
		this.selectedTab = this.tabId(subgrupo);
		this.selectedSubGroup = subgrupo;
		this.mostrarUnidade = this.selectedSubGroup.modulo === ConfigModulo.IA;
		this.configService.selectedTab = this.tabId(subgrupo);
		this.configService.selectedSubGroup = subgrupo;
		if (subgrupo.modulo.nome === ConfigModulo.IA.nome) {
			if (subgrupo.nome === ConfigModuloSubGroup.IA_REDE_DE_EMPRESAS.nome) {
				this.configService.setConfigRedeSelecionada(true);
			} else {
				this.configService.setConfigRedeSelecionada(false);
			}
			if (!this.empresaSelecionada) {
				this.mudarDeEmpresa();
			}
		} else {
			this.configService.setConfigRedeSelecionada(false);
		}

		this.inputs = this.configService.getInputsBySubGroup(this.selectedSubGroup);
	}

	mudarDeEmpresa() {
		const empresaDialog = this.ngbModal.open(SelectEmpresaDialogComponent);
		empresaDialog.result.then((result) => {
			this.configIaService.loadConfiguracaoCrmEmpresa(result);
			this.empresaSelecionada = result ? result.nome : "";
		});
	}

	filterSubGrupsByModule(modulo: ConfigModulo): Array<ConfigModuloSubGroup> {
		this.verifySubGrupoIsAllowed();
		return this.subGroups.filter(
			(subgrupo) =>
				subgrupo.modulo === modulo && this.checkSubGroup(subgrupo.enabled)
		);
	}

	private verifySubGrupoIsAllowed() {
		if (!this.sessionService.temModuloIA()) {
			const modulo = this.modulos.findIndex(
				(grupo) => grupo === ConfigModulo.IA
			);
			if (modulo > -1) {
				this.modulos.splice(modulo, 1);
			}
		} else {
			this.verificaRedeExistente();
		}
	}

	private verificaRedeExistente() {
		if (this.sessionService.loggedUser.username.toLowerCase() !== "pactobr") {
			const indexSub = this.subGroups.findIndex(
				(subgrupo) => subgrupo === ConfigModuloSubGroup.IA_REDE_DE_EMPRESAS
			);
			if (indexSub > -1) {
				this.subGroups.splice(indexSub, 1);
			}
		}
	}

	checkSubGroup(subgrupo: any) {
		let allow: boolean = null;
		if (subgrupo && subgrupo.enabled === false) {
			allow = false;
		}

		if (
			allow === null &&
			[
				ConfigModuloSubGroup.ADM_MANUTENCAO_PRODUTOS_PARCELAS,
				ConfigModuloSubGroup.ADM_MANUTENCAO_GERAIS,
				ConfigModuloSubGroup.ADM_PROCESSOS,
			].includes(subgrupo)
		) {
			if (
				(this.sessionService.loggedUser &&
					this.sessionService.loggedUser.username &&
					this.sessionService.loggedUser.username.toLowerCase().trim() ===
						"admin") ||
				this.sessionService.loggedUser.username.toLowerCase().trim() ===
					"pactobr"
			) {
				allow = true;
			} else {
				allow = false;
			}
		}

		return allow === null ? true : allow;
	}

	goTo(item) {
		if (item) {
			this.selectedTab = "tab-" + item.label + "-" + item.id;
			this.router.navigate(item.label);
			setTimeout(() => {
				document.getElementById(item.id ? item.id : "topo").scrollIntoView();
			}, 200);
		} else {
			document.getElementsByTagName("main")[0].scrollTo(0, 0);
		}
	}

	onClickSubGrupo(subGrupo: ConfigModuloSubGroup) {
		this.selectSubGroup(subGrupo);
		this.mostrarUnidade = subGrupo.modulo.nome === ConfigModulo.IA.nome;

		setTimeout(() => {
			const contentDisplay = document.querySelector(".content-display");
			if (contentDisplay) {
				contentDisplay.scrollTop = 0;
			}
		});
	}

	actionButton() {
		console.log("action");
	}

	isInvalidTab(subGroup: ConfigModuloSubGroup): boolean {
		return !this.configService.validSubGroup(subGroup);
	}

	save() {
		if (!this.valid) {
			this.notificationService.error(
				"Há configurações inválidas. Por favor faça uma revisão!"
			);
			this.cd.detectChanges();
			return;
		}
		this.configService.save(() => {
			this.cd.detectChanges();
		});
	}

	tabId(subGroup: ConfigModuloSubGroup): string {
		return `tab-${subGroup.modulo.codigo}-${subGroup.codigo}`;
	}

	onClickFeedback() {
		window.open("https://forms.gle/prUUPCMHTMCAp64L8", "_blank");
	}

	valueTransFromBolleanToString(value: any) {
		if (value === "true") {
			return "Ativo";
		}
		if (value === "false") {
			return "Inativo";
		}
		return value;
	}

	logParamAdapterFn(params: any) {
		if (
			"quicksearchValue" in params &&
			new RegExp(/(\d+\.\d+\.\d+|\d+\.\d+)/).test(params.quicksearchValue)
		) {
			params["nomecampos"] = V2Component.me.findLogFieldNameByCode(
				params.quicksearchValue
			);
			delete params["quicksearchValue"];
		}
		return params;
	}

	getLogDataGridConfig() {
		const filtroEntidades = this.logEntities
			.map((entidade) => `nomesentidades=${entidade}`)
			.join("&");
		const baseUrl = this.discoveryService.getUrlMap().admMsUrl + "/v1/log";

		return new PactoDataGridConfig({
			endpointUrl: `${baseUrl}?${filtroEntidades}`,
			quickSearch: true,
			exportButton: false,
			endpointParamsType: "query",
			paramAdapterFn: this.logParamAdapterFn,
			onError: (e) => {
				let msg = `Falha ao carregar log da api ${baseUrl}`;
				if (e && e.error && e.error.error) {
					msg = e.error.error;
				}
				this.notificationService.error(msg);
			},
			dataAdapterFn: (data) => {
				return this.prepareLogFieldNames(data);
			},
			columns: [
				{
					nome: "operacao",
					titulo: "Operação",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataAlteracao",
					titulo: "Data",
					valueTransform: (v) => moment(v).format("DD/MM/YYYY HH:mm:ss"),
					visible: true,
					ordenavel: false,
				},
				{
					nome: "responsavelAlteracao",
					titulo: "Usuário",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nomeCampo",
					titulo: "Campo",
					visible: true,
					valueTransform: (value, row) =>
						row.nomeCampoTitulo ||
						row.nomeCampoDescricao ||
						row.nomeCampo ||
						value,
					ordenavel: false,
				},
				{
					nome: "valorCampoAnterior",
					titulo: "Valor anterior",
					valueTransform: this.valueTransFromBolleanToString,
					visible: true,
					ordenavel: false,
					celula: this.colunaValorAnterior,
				},
				{
					nome: "valorCampoAlterado",
					titulo: "Valor alterado",
					valueTransform: this.valueTransFromBolleanToString,
					visible: true,
					ordenavel: false,
					celula: this.colunaValorAlterado,
				},
			],
		});
	}

	onClickColumnValue(item: any) {
		const modal = this.ngbModal.open(ModalLogDefaultComponent, {
			windowClass: "custom-ngb-modal-window",
			backdropClass: "custom-ngb-modal-backdrop",
			centered: true,
		});
		modal.componentInstance.item = item;
	}

	findLogFieldNameByCode(code: string): Array<String> {
		let fieldNames = new Array<String>();
		V2Component.me.configService.inputs.forEach((config) => {
			fieldNames = fieldNames.concat(
				V2Component.me.findInputNamesByCode(code, config.inputs)
			);
		});
		return fieldNames;
	}

	findInputNamesByCode(code: string, inputs: ConfigItem[]): Array<String> {
		let fieldNames = new Array<String>();

		inputs.forEach((input) => {
			if (input.codigo && input.name && input.codigo === code) {
				fieldNames.push(input.name);
			}
			if (input.children) {
				fieldNames = fieldNames.concat(
					V2Component.me.findInputNamesByCode(code, input.children)
				);
			}
		});
		return fieldNames;
	}

	findLogFieldTitle(input: ConfigItem, row: any): string {
		let title: string;

		if (
			!title &&
			input.fieldLogName &&
			input.fieldLogName === row.nomeCampoDescricao
		) {
			title = input.codigo
				? `${input.fieldLogName} - ${input.codigo}`
				: input.fieldLogName;
		}

		if (!title && !input.fieldLogName && row.nomeCampo === input.name) {
			title = input.codigo ? `${input.title} - ${input.codigo}` : input.title;
		}

		if (
			!title &&
			input.entityLogName &&
			input.entityLogName === row.nomeEntidade &&
			(!input.tagLog || input.tagLog.includes(row.tags))
		) {
			if ("dataGridConfig" in input) {
				let columnTitle = null;

				const customFieldTitle = this.findCustomFindLogFieldTitle(row);
				if (customFieldTitle) {
					columnTitle = customFieldTitle;
				}

				if (!columnTitle) {
					input.dataGridConfig.columns.every((column) => {
						if (
							column.nome &&
							row.nomeCampo &&
							column.nome.toLowerCase() === row.nomeCampo.toLowerCase()
						) {
							columnTitle = column.titulo as string;
							return false;
						}
						return true;
					});
				}

				title = input.codigo
					? `${input.title} - ${input.codigo} - Campo: ${columnTitle}`
					: `${input.title} - Campo: ${columnTitle}`;
			} else {
				title = input.codigo ? `${input.title} - ${input.codigo}` : input.title;
			}
		}

		if (!title && input.children) {
			input.children.every((child) => {
				title = this.findLogFieldTitle(child, row);
				if (title) {
					return false;
				} else {
					return true;
				}
			});
		}

		return title;
	}

	findCustomFindLogFieldTitle(row: any): string {
		if (
			row.nomeEntidade &&
			row.nomeEntidade === "configuracaodiasposvenda" &&
			row.nomeCampo === "siglaResponsavelPeloContato"
		) {
			return "Responsável";
		}

		return null;
	}

	prepareLogFieldNames(data: any): TableData<any> {
		if (data.content) {
			data.content = data.content.map((row) => {
				const nomeCampoEmCache = V2Component.me.fieldNamesCache.get(
					`${row.nomeCampo}-${row.nomeEntidade}`
				);
				if (nomeCampoEmCache) {
					row.nomeCampoTitulo = nomeCampoEmCache;
				} else {
					V2Component.me.configService.inputs.forEach((config) => {
						config.inputs.every((input) => {
							const title = V2Component.me.findLogFieldTitle(input, row);
							if (title) {
								row.nomeCampoTitulo = title;
								V2Component.me.fieldNamesCache.set(
									`${row.nomeCampo}-${row.nomeEntidade}-${row.tags}`,
									title
								);
								return false;
							} else {
								return true;
							}
						});
					});
				}
				return row;
			});
		}
		return data;
	}

	private tranformHtmlToText(html: string) {
		const regexForStripHTML = /(<([^>]+)>)/gi;
		return html.replace(regexForStripHTML, "");
	}

	private selectInitTabsFaseIa() {
		this.loading = false;
		this.cd.detectChanges();
	}

	/**
	 * <AUTHOR> da Silva Rezende (Feijão)
	 *
	 * Ao realizar a refatoração dessa nova configuração para carregar as
	 * configurações por demanda, isso deve ser repensado e não deixado.
	 *
	 * Feito devido a necessidade de algumas configurações necessitarem da empresa
	 * e ao pesquisar a empresa não é carregada.
	 *
	 * ISSO NÃO DEVE SER REMOVIDO ATÉ O MOMENTO EM QUE FOR FEITO A REFATORAÇÃO
	 * DA TELA DE CONFIGURAÇÃO.
	 *
	 * ISSO É PARA NÃO IMPACTAR NA PESQUISA DAS CONFIGURAÇÕES QUE NECESSITAM DE
	 * EMPRESA PARA SALVAR (POR MAIS QUE SEJA SOMENTE A IA ATUALMENTE)
	 *
	 */
	private _initConfigByEmpresa() {
		const idEmpresaSelecionada = Number(this.sessionService.empresaId);
		const empresaLogada = this.sessionService.empresas.find(
			(emp) => emp.codigo === idEmpresaSelecionada
		);
		if (empresaLogada) {
			this.empresaSelecionada = empresaLogada.nome;
			this.configIaService.loadConfiguracaoCrmEmpresa({
				id: empresaLogada.codigo,
				nome: empresaLogada.nome,
			});
		}
	}
}
