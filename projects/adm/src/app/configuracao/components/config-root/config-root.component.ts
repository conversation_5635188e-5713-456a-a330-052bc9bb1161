import { NotificarRecursoEmpresaService } from "@adm/services/notificar-recurso-empresa/notificar-recurso-empresa.service";
import { Component, OnDestroy, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
	selector: "adm-config-root",
	templateUrl: "./config-root.component.html",
	styleUrls: ["./config-root.component.scss"],
})
export class ConfigRootComponent implements OnInit, OnDestroy {
	private _destoyed$: Subject<void> = new Subject<void>();
	private _angularNavigation: boolean = false;
	tabs = ["Configurações", "Integrações", "Telas Descontinuadas"];
	recurso = "CONFIGURACOES";
	selectedTabIndex = 0;
	constructor(
		private notificarRecursoEmpresaService: NotificarRecursoEmpresaService,
		private router: Router,
		private activatedRoute: ActivatedRoute
	) {
		this._angularNavigation = this.router.getCurrentNavigation() !== null;
	}
	ngOnInit() {
		this._setTabByRoute();
		this._notifyRecursoEmpresaNavigation();
	}

	ngOnDestroy() {
		this._destoyed$.next();
	}

	private _setTabByRoute() {
		const url = this.router.url;
		if (url.includes("/descontinuadas")) {
			this.selectedTabIndex = 2;
		} else if (url.includes("/integracoes")) {
			this.selectedTabIndex = 1;
		} else {
			this.selectedTabIndex = 0;
		}
	}

	onChangeTab(tabIndex) {
		switch (tabIndex.toString()) {
			case "0":
				this.router.navigate(["/adm", "configuracao", "v2"]);
				break;
			case "1":
				this.router.navigate(["/adm", "configuracao", "v2", "integracoes"]);
				break;
			case "2":
				this.router.navigate(["/adm", "configuracao", "v2", "descontinuadas"]);
				break;
		}
	}

	acessarVersaoAntiga() {
		this.router.navigate(["configuracao"]);
	}

	private _notifyRecursoEmpresaNavigation() {
		if (!this._angularNavigation) {
			return;
		}

		this.notificarRecursoEmpresaService
			.notifyRecursoEmpresaNavigation("NOVA-CONFIGURACAO")
			.pipe(takeUntil(this._destoyed$))
			.subscribe();
	}
}
