import { EventEmitter, Injectable } from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	ValidatorFn,
} from "@angular/forms";
import {
	AdmMsApiConfiguracaoClienteService,
	AdmMsApiConfiguracaoColaboradorService,
	AdmMsApiConfiguracaoService,
	ConfiguracaoCliente,
	ConfiguracaoColaborador,
	ConfiguracaoSistema,
} from "adm-ms-api";
import { ConfiguracaoCrm, CrmApiConfiguracaoService } from "crm-api";
import {
	ConfiguracaoFinanceiro,
	FinanceiroMsApiConfiguracaoService,
} from "financeiro-ms-api";
import { SnotifyService } from "ng-snotify";
import { PermissaoService } from "pacto-layout";
import { concat, forkJoin, Observable, of } from "rxjs";
import { switchMap } from "rxjs/operators";
import { PlataformaModulo, SessionService } from "sdk";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import {
	ConfigTreinoPayload,
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	TreinoApiConfiguracoesTreinoService,
} from "treino-api";
import { LoaderService } from "ui-kit";
import { ConfigItem, ConfigItemBase } from "../inputs/form-configuracoes.model";
import { ConfigAdmService } from "./config-adm.service";
import { ConfigCrmService } from "./config-crm.service";
import { ConfigFinanceiroService } from "./config-financeiro.service";
import { ConfigIaService } from "./config-ia.service";
import { ConfigTreinoService } from "./config-treino.service";
import { ConfigModuloSubGroup } from "./model/config-module-subgroup.model";
import { ConfigModulo, Configuracao } from "./model/config-modulo.model";
import { SubGrupoInputs } from "./model/sub-group-inputs.model";

@Injectable({
	providedIn: "root",
})
export class ConfigService {
	modulos: Array<ConfigModulo>;
	subGroups: Array<ConfigModuloSubGroup>;
	formGroups: Map<number, FormGroup> = new Map<number, FormGroup>();
	subFormGroups: Map<string, FormGroup> = new Map<string, FormGroup>();
	inputs: SubGrupoInputs[];
	currentValues: Map<ConfigModulo | ConfigModuloSubGroup, Configuracao> =
		new Map();
	loaded: EventEmitter<boolean> = new EventEmitter();
	reloaded: EventEmitter<boolean> = new EventEmitter();
	detectChanges: EventEmitter<boolean> = new EventEmitter();
	searchText?: string;
	configRedeSelecionada: boolean = false;
	selectedTab: string;
	selectedSubGroup: ConfigModuloSubGroup;

	constructor(
		private crmConfigApiService: CrmApiConfiguracaoService,
		private admConfigApiService: AdmMsApiConfiguracaoService,
		private finConfigApiService: FinanceiroMsApiConfiguracaoService,
		private admConfigClienteService: AdmMsApiConfiguracaoClienteService,
		private admConfigColaboradorService: AdmMsApiConfiguracaoColaboradorService,
		private treinoApiConfigService: TreinoApiConfiguracoesTreinoService,
		private treinoConfigCacheService: TreinoConfigCacheService,
		private configAdmService: ConfigAdmService,
		private configCrmService: ConfigCrmService,
		private configIaService: ConfigIaService,
		private configFinanceiroService: ConfigFinanceiroService,
		private configTreinoService: ConfigTreinoService,
		private notificationService: SnotifyService,
		private loaderService: LoaderService,
		private sessionService: SessionService,
		private permissaoService: PermissaoService
	) {
		this.configAdmService.loaded.subscribe(() => {
			this.currentValues.set(ConfigModulo.ADM, this.configAdmService.config);
			this.checkLoadedAndPrepare();
		});

		this.configAdmService.detectChanges.subscribe(() => {
			this.detectChanges.emit(true);
		});

		this.configIaService.detectChanges.subscribe(() => {
			this.detectChanges.emit(true);
		});

		this.configCrmService.loaded.subscribe(() => {
			this.currentValues.set(ConfigModulo.CRM, this.configCrmService.config);
			this.checkLoadedAndPrepare();
		});

		this.configIaService.loaded.subscribe(() => {
			this.currentValues.set(ConfigModulo.IA, this.configIaService.config);
			this.checkLoadedAndPrepare();
		});

		this.configIaService.reloaded.subscribe(() => {
			this.inputs = this.prepareInputs();
			this.reloaded.emit(true);
		});

		this.configCrmService.detectChanges.subscribe(() => {
			this.detectChanges.emit(true);
		});

		this.configFinanceiroService.loaded.subscribe(() => {
			this.currentValues.set(
				ConfigModulo.FIN,
				this.configFinanceiroService.config
			);
			this.checkLoadedAndPrepare();
		});

		this.configFinanceiroService.detectChanges.subscribe(() => {
			this.detectChanges.emit(true);
		});

		this.configTreinoService.loaded.subscribe(() => {
			this.currentValues.set(
				ConfigModulo.TRE,
				this.configTreinoService.configTreinoPayload
			);
			this.checkLoadedAndPrepare();
		});

		this.configTreinoService.detectChanges.subscribe(() => {
			this.detectChanges.emit(true);
		});

		this.modulos = Object.keys(ConfigModulo).map((key) => ConfigModulo[key]);
		this.modulos.forEach((modulo) => {
			this.setFormGroup(modulo.codigo, new FormGroup({}));
			this._disableModulo(modulo);
		});
		this.subGroups = Object.keys(ConfigModuloSubGroup).map((key) => {
			const subModule = ConfigModuloSubGroup[key];
			if (
				subModule.modulo.enabled &&
				subModule === ConfigModuloSubGroup.CRM_METAS_STUDIO
			) {
				subModule.enabled = this.sessionService.isModuloHabilitado(
					PlataformaModulo.EST
				);
			}
			return subModule;
		});
		this.subGroups.forEach((subGroup) =>
			this.setSubFormGroup(subGroup, new FormGroup({}))
		);
	}

	setConfigRedeSelecionada(configRedeSelecionada: any) {
		this.configRedeSelecionada = configRedeSelecionada;
	}

	checkLoadedAndPrepare() {
		if (
			this.currentValues.get(ConfigModulo.ADM) &&
			this.currentValues.get(ConfigModulo.CRM) &&
			this.currentValues.get(ConfigModulo.FIN) &&
			this.currentValues.get(ConfigModulo.TRE) &&
			this.currentValues.get(ConfigModulo.IA)
		) {
			this.inputs = this.prepareInputs();
			this.loaded.emit(true);
		}
	}

	validSubGroup(subGroup: ConfigModuloSubGroup): boolean {
		const formGroup = this.getSubFromGroup(subGroup);

		/**
		 * Necessário pois controls desabilitados não entra na validação do form.
		 * Para esses controls tanto valid quanto invalid são false e por isso foi
		 * alterado para utilizar o invalid nessas validações.
		 */
		if (formGroup) {
			let invalid = false;

			for (const controlsKey in formGroup.controls) {
				invalid = formGroup.controls[controlsKey].invalid;
				if (invalid) {
					break;
				}
			}

			return !invalid;
		}

		return true;
	}

	getFormGroups() {
		return [
			this.getFormGroup(ConfigModulo.ADM.codigo),
			this.getFormGroup(ConfigModulo.CRM.codigo),
			this.getFormGroup(ConfigModulo.FIN.codigo),
			this.getFormGroup(ConfigModulo.TRE.codigo),
			this.getFormGroup(ConfigModulo.IA.codigo),
		];
	}

	pristine() {
		const fromGroups = this.getFormGroups();
		fromGroups.forEach((formGroup) => {
			formGroup.markAsPristine();
		});
	}

	dirty(): boolean {
		const fromGroups = this.getFormGroups();
		const controlsDirty = [];
		const controls = Object.keys(
			this.configIaService.formGroupConfiguracaoIA.controls
		).filter(
			(controlName) =>
				this.configIaService.formGroupConfiguracaoIA.controls[controlName].dirty
		);

		if (controls && controls.length > 0) {
			controlsDirty.push(controls);
		}

		fromGroups.forEach((formGroup) => {
			const controls = Object.keys(formGroup.controls).filter(
				(controlName) => formGroup.controls[controlName].dirty
			);
			if (controls && controls.length > 0) {
				controlsDirty.push(controls);
			}
		});

		const individualControls = [
			this.configIaService.formControlLoginPactoConversas,
			this.configIaService.formControlSenhaPactoConversas,
			this.configIaService.formControlTokenPactoConversas,
			this.configIaService.formControlInstanciaPactoConversas,
			this.configIaService.formControlWhatsAppBusiness,
			this.configIaService.formEmailResponsavelConversasAI,
			this.configIaService.formTelefoneResponsavelConversasAI,
			this.configIaService.fromControlCampanhas,
			this.configIaService.formControlInputFile,
		];

		individualControls.forEach((control) => {
			if (control.dirty || control.touched) {
				controlsDirty.push(control);
			}
			control.valueChanges.subscribe((value) => {
				if (value === null || value === undefined || value === "") {
					controlsDirty.push(control);
				}
			});
		});

		return controlsDirty.length > 0;
	}

	valid(): boolean {
		const fromGroups = [
			this.getFormGroup(ConfigModulo.ADM.codigo),
			this.getFormGroup(ConfigModulo.CRM.codigo),
			this.getFormGroup(ConfigModulo.FIN.codigo),
			this.getFormGroup(ConfigModulo.TRE.codigo),
			this.getFormGroup(ConfigModulo.IA.codigo),
		];
		this.addCrmControls();

		/**
		 * Necessário pois controls desabilitados não entra na validação do form.
		 * Para esses controls tanto valid quanto invalid são false e por isso foi
		 * alterado para utilizar o invalid nessas validações.
		 */
		const valid = fromGroups.every((formGroup) => {
			formGroup.updateValueAndValidity();
			return !formGroup.invalid;
		});
		return valid || false;
	}

	addCrmControls() {
		this.getFormGroup(ConfigModulo.IA.codigo).addControl(
			"instanceId",
			this.configIaService.formControlInstanciaPactoConversas
		);
		this.getFormGroup(ConfigModulo.IA.codigo).addControl(
			"token",
			this.configIaService.formControlTokenPactoConversas
		);
	}

	invalidModules(): ConfigModulo[] {
		return Object.keys(ConfigModulo)
			.filter((modulo) => {
				const formGroup = this.getFormGroup(ConfigModulo[modulo].codigo);
				// TODO: Verificar porque o valid do formGroup não está funcionando
				if (formGroup) {
					return Object.keys(formGroup.controls).some(
						(controlName) => formGroup.controls[controlName].invalid
					);
				}
			})
			.map((modulo) => ConfigModulo[modulo]);
	}

	save(onCompletion: Function) {
		this.loaderService.initForce();
		this.saveAllConfigs(onCompletion);
	}

	saveAllConfigs(onCompletion: Function) {
		this.loaderService.initForce();
		forkJoin(
			this.saveAdm(),
			this.saveCampoCadastroVisitante(),
			this.saveCampoCadastroCliente(),
			this.saveCampoColaborador(),
			this.saveCrm(),
			this.saveFin(),
			this.saveTreino(),
			this.saveIa()
		).subscribe(
			([
				configAdm,
				configCadastroVisitante,
				configCadastroCliente,
				configCampoColaborador,
				configCrm,
				configFinanceiro,
				configApp,
				configIA,
			]) => {
				this.currentValues.set(ConfigModulo.ADM, configAdm);
				this.currentValues.set(ConfigModulo.CRM, configCrm);
				this.currentValues.set(ConfigModulo.FIN, configFinanceiro);
				this.currentValues.set(ConfigModulo.TRE, configApp);
				this.currentValues.set(ConfigModulo.IA, configIA);
			},
			(error) => {
				this.notificationService.error(
					"Não foi possivel salvar suas configurações!"
				);
				console.error("Erro ao salvar configurações", error);
				this.loaderService.stopForce();
				onCompletion(false);
			},
			() => {
				this.notificationService.success(
					"Todas as configurações foram salvas com sucesso!"
				);
				this.pristine();
				this.loaderService.stopForce();
				onCompletion(true);
			}
		);
	}

	saveCampoColaborador(): Observable<ConfiguracaoColaborador[]> {
		const formGroup = this.configAdmService.formGroupCamposColaborador;

		if (!this.dirtyControls(formGroup)) {
			return of(null);
		}
		const possuiPermissao = this._possuiPermissaoAlterarConfigAdm();
		if (!possuiPermissao) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 2.08 - Configuração do sistema para alterar as configurações do móduloADM, procure seu administrador!"
			);
			return of(this.configAdmService.currentValuesCamposColaborador);
		}
		formGroup.updateValueAndValidity();
		const formValues = formGroup.value;
		const payload = Object.keys(formValues).map((campo) => {
			const campoColaborador =
				this.configAdmService.currentValuesCamposColaborador.find(
					(campoColaborador) => campoColaborador.nomecampo === campo
				);
			return {
				codigo: campoColaborador.codigo,
				nomecampo: campoColaborador.nomecampo,
				labelcampo: campoColaborador.labelcampo,
				campoobrigatorio: formValues[campo] === "campoobrigatorio",
				mostrarcampo: formValues[campo] === "mostrarcampo",
			};
		});

		return this.admConfigColaboradorService.alterar(payload);
	}

	private prepareConfiguracaoCliente(
		formValues,
		visitante: boolean
	): ConfiguracaoCliente[] {
		return Object.keys(formValues).map((campo) => {
			return {
				nome: campo,
				obrigatorio: formValues[campo] === "obrigatorio",
				pendente: formValues[campo] === "pendente",
				mostrar: formValues[campo] === "mostrar",
				visitante,
			};
		});
	}

	private saveCampoCadastroCliente(): Observable<ConfiguracaoCliente[]> {
		const formGroup = this.configAdmService.formGroupCamposCliente;

		if (!this.dirtyControls(formGroup)) {
			return of(null);
		}
		const possuiPermissao = this._possuiPermissaoAlterarConfigAdm();
		if (!possuiPermissao) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 2.08 - Configuração do sistema para alterar as configurações do móduloADM, procure seu administrador!"
			);
			return of(this.configAdmService.currentValuesCamposCliente);
		}
		formGroup.updateValueAndValidity();
		const payload = this.prepareConfiguracaoCliente(formGroup.value, false);
		return this.admConfigClienteService.alterar(payload);
	}

	private saveCampoCadastroVisitante(): Observable<ConfiguracaoCliente[]> {
		const formGroup = this.configAdmService.formGroupCamposVisitante;

		if (!this.dirtyControls(formGroup)) {
			return of(null);
		}
		const possuiPermissao = this._possuiPermissaoAlterarConfigAdm();
		if (!possuiPermissao) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 2.08 - Configuração do sistema para alterar as configurações do móduloADM, procure seu administrador!"
			);
			return of(this.configAdmService.currentValuesCamposVisitante);
		}
		formGroup.updateValueAndValidity();
		const payload = this.prepareConfiguracaoCliente(formGroup.value, true);
		return this.admConfigClienteService.alterar(payload);
	}

	private _possuiPermissaoAlterarConfigCrm() {
		const recursoConfig = this.permissaoService.recursoAdm("7.06");
		return recursoConfig.tipoPermissoes.some((tp) => {
			return (
				tp === PerfilRecursoPermissoTipo.EDITAR ||
				tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
				tp === PerfilRecursoPermissoTipo.TOTAL ||
				tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		});
	}

	saveCrm(): Observable<ConfiguracaoCrm> {
		const formGroup = this.getFormGroup(ConfigModulo.CRM.codigo);

		if (
			!this.dirtyControls(formGroup) &&
			!this.dirtyControls(this.configCrmService.resonsaveisPorFasesFormGroup)
		) {
			return of(null);
		}
		const possuiPermissao = this._possuiPermissaoAlterarConfigCrm();
		if (!possuiPermissao) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 7.06 - Configuração de sistema do CRM para alterar as configurações do módulo CRM, procure seu administrador!"
			);
			return of(this.configCrmService.config);
		}
		formGroup.updateValueAndValidity();
		const payload = formGroup.value;

		if (payload["remetentepadrao"] && payload["remetentepadrao"].nome) {
			payload["remetentepadrao"] = payload["remetentepadrao"].nome;
		}

		this.prepareObjectId(payload);
		this.configCrmService.saveResponsaveisPorFases(formGroup);
		return this.crmConfigApiService.alterar(payload);
	}

	saveIa(): Observable<any> {
		const formGroup = this.getFormGroup(ConfigModulo.IA.codigo);
		if (!this.dirtyControls(formGroup)) {
			return of(null);
		}
		formGroup.updateValueAndValidity();
		const payload = formGroup.value;
		this.prepareObjectId(payload);
		// NOVO
		if (this.configRedeSelecionada) {
			this.configIaService.saveConfiguracaoRedeIA();
		} else {
			this.configIaService.saveConfiguracaoFasesIA();
			this.configIaService.saveConfiguracaoIA();
		}
		return of(null);
	}

	private prepareObjectId(payload) {
		Object.keys(payload).forEach((key) => {
			if (
				typeof payload[key] === "object" &&
				payload[key] !== undefined &&
				payload[key] !== null &&
				payload[key].codigo
			) {
				payload[key] = payload[key].codigo;
			}
		});
	}

	private _possuiPermissaoAlterarConfigAdm() {
		const recursoConfig = this.permissaoService.recursoAdm("2.08");
		return recursoConfig.tipoPermissoes.some((tp) => {
			return (
				tp === PerfilRecursoPermissoTipo.EDITAR ||
				tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
				tp === PerfilRecursoPermissoTipo.TOTAL ||
				tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		});
	}

	saveAdm(): Observable<ConfiguracaoSistema> {
		const formGroup = this.getFormGroup(ConfigModulo.ADM.codigo);
		if (!this.dirtyControls(formGroup)) {
			return of(null);
		}
		const possuiPermissao = this._possuiPermissaoAlterarConfigAdm();
		if (!possuiPermissao) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 2.08 - Configuração do sistema para alterar as configurações do módulo ADM, procure seu administrador!"
			);
			return of(this.configAdmService.config);
		}
		formGroup.updateValueAndValidity();
		const payload = formGroup.value;
		this.prepareObjectId(payload);
		return this.admConfigApiService.alterar(payload);
	}

	private _possuiPermissaoAlterarConfigFin() {
		return this.permissaoService.temPermissaoAdm("9.08");
	}

	saveFin(): Observable<ConfiguracaoFinanceiro> {
		const formGroup = this.getFormGroup(ConfigModulo.FIN.codigo);
		if (!this.dirtyControls(formGroup)) {
			return of(null);
		}
		const temPermissaoConfigFin = this._possuiPermissaoAlterarConfigFin();
		if (!temPermissaoConfigFin) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 9.08 - Configurações do financeiro para alterar as configurações do módulo FINANCEIRO, procure seu administrador!"
			);
			return of(this.configFinanceiroService.config);
		}
		this.getFormGroup(ConfigModulo.FIN.codigo).updateValueAndValidity();
		const payload = this.getFormGroup(ConfigModulo.FIN.codigo).value;
		this.prepareObjectId(payload);
		return this.finConfigApiService.alterar(payload);
	}

	private _possuiPermissaoAlterarConfigTreino() {
		const permissaoEmpresa = new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.CONFIGURACOES_EMPRESA,
			[
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		);
		return this.permissaoService.temPermissaoTreino(permissaoEmpresa);
	}

	saveTreino(): Observable<any> {
		const formGroup = this.getFormGroup(ConfigModulo.TRE.codigo);
		if (!this.dirtyControls(formGroup)) {
			return of(null);
		}
		const temPermissaoConfigTreino = this._possuiPermissaoAlterarConfigTreino();
		if (!temPermissaoConfigTreino) {
			this.notificationService.error(
				"Seu usuário não possui a permissão Configurações Empresa para alterar as configurações do treino, procure seu administrador!"
			);
			return of(this.configTreinoService.configTreinoPayload);
		}
		const saveConfig = (
			updateService: (payload: any) => Observable<any>,
			payloadMapper: (payload: any) => any
		) => {
			const controls = Object.keys(formGroup.controls).filter(
				(controlName) => formGroup.controls[controlName].dirty
			);
			if (controls.length === 0) {
				return of(null);
			}

			formGroup.updateValueAndValidity();
			const payload = formGroup.value;
			const dataPayload = payloadMapper(payload);
			this.prepareObjectId(dataPayload);
			const save$ = updateService.call(
				this.treinoApiConfigService,
				dataPayload
			);
			return this.updateCache(save$);
		};

		return concat<any>(
			saveConfig(
				this.treinoApiConfigService.updateConfiguracoesAula,
				ConfigTreinoPayload.getConfiguracoesAulas
			),

			saveConfig((payload) => {
				this.configTreinoService.setModuloAula(payload);
				return this.treinoApiConfigService.updateConfiguracoesAplicativo(
					payload
				);
			}, ConfigTreinoPayload.getConfiguracoesAplicativos),

			saveConfig((payload) => {
				this.configTreinoService.setTipoRmlAvaliacao(payload);
				if (this.configTreinoService.orderDobras) {
					payload.ordens_dobras = JSON.stringify(
						this.configTreinoService.orderDobras
					);
				}
				return this.treinoApiConfigService.updateConfiguracoesAvaliacao(
					payload
				);
			}, ConfigTreinoPayload.getConfiguracoesAvaliacao),

			saveConfig(
				this.treinoApiConfigService.updateConfiguracoesGerais,
				ConfigTreinoPayload.getConfiguracoesGerais
			),

			saveConfig(
				this.treinoApiConfigService.updateConfiguracoesGestao,
				ConfigTreinoPayload.getConfigGestao
			),

			saveConfig(
				this.treinoApiConfigService.updateConfiguracoesManutencao,
				ConfigTreinoPayload.getConfigManutencao
			),

			saveConfig(
				this.treinoApiConfigService.updateConfiguracoesNotificacao,
				ConfigTreinoPayload.getConfigNotificacao
			),

			saveConfig(
				this.treinoApiConfigService.updateConfiguracoesTreino,
				ConfigTreinoPayload.getConfigTreino
			),

			saveConfig(
				this.treinoApiConfigService.updateConfiguracoesIa,
				ConfigTreinoPayload.getConfigIa
			)
		);
	}

	private updateCache(save$: Observable<any>) {
		const update$ = this.treinoConfigCacheService.loadTreinoConfigCache();
		save$.pipe(switchMap(() => update$)).subscribe(() => {
			this.treinoApiConfigService.atualizarInfo();
		});
		return save$;
	}

	private checkInputsContainingText(regex: RegExp, input: ConfigItem) {
		let description: string;
		if (input.description && input.description instanceof Function) {
			description = input.description();
		} else if (input.description) {
			description = input.description as string;
		}

		const found =
			regex.test(input.name) ||
			regex.test(description) ||
			regex.test(input.title) ||
			(input.codigo === false ? false : regex.test(input.codigo as string));

		if (!found && input.children) {
			return input.children.find((children) =>
				this.checkInputsContainingText(regex, children)
			);
		}

		return found;
	}

	getInputsBySearch(search: string): ConfigItemBase[] {
		const inputs = [];
		const regex = RegExp(search, "i");
		this.inputs.forEach((subGroupInputs) => {
			if (!subGroupInputs.subgrupo.modulo.enabled) {
				return;
			}
			subGroupInputs.inputs.forEach((input) => {
				const found = this.checkInputsContainingText(regex, input);
				if (found) {
					inputs.push(input);
				}
			});
		});

		return inputs;
	}

	getInputsBySubGroup = (subgrupo: ConfigModuloSubGroup): ConfigItemBase[] => {
		if (this.inputs == undefined) {
			return [];
		}
		const subGupInputs = this.inputs.find((item) => item.subgrupo === subgrupo);
		if (!subGupInputs) {
			throw new Error(`Subgrupo ${subgrupo.nome} não encontrado`);
		}
		return subGupInputs.inputs;
	};

	setSubFormGroup(
		moduleSubGroup: ConfigModuloSubGroup,
		formGroup: FormGroup
	): void {
		this.subFormGroups.set(
			`${moduleSubGroup.modulo.codigo}-${moduleSubGroup.codigo}`,
			formGroup
		);
	}

	getSubFromGroup(moduleSubGroup: ConfigModuloSubGroup) {
		return this.subFormGroups.get(
			`${moduleSubGroup.modulo.codigo}-${moduleSubGroup.codigo}`
		);
	}

	setFormGroup(codigoModulo: number, formGroup: FormGroup): void {
		this.formGroups.set(codigoModulo, formGroup);
	}

	getFormGroup(codigoModulo: number) {
		return this.formGroups.get(codigoModulo);
	}

	private prepareInputFormControl(
		input: ConfigItem,
		subGrupoInputs: SubGrupoInputs,
		currentValues: Configuracao
	) {
		const formGroupModulo = this.getFormGroup(
			subGrupoInputs.subgrupo.modulo.codigo
		);
		const formSubGrupo = this.getSubFromGroup(subGrupoInputs.subgrupo);
		if (input.formControl) {
			if (input.autoControlCurrentValue) {
				input.formControl.setValue(this.configValue(currentValues, input.name));
			}

			if (input.validators) {
				input.formControl.setValidators(
					input.validators as ValidatorFn | ValidatorFn[] | null
				);
			}

			if (!formGroupModulo.contains(input.name)) {
				formGroupModulo.addControl(input.name, input.formControl);
			}

			if (!formSubGrupo.contains(input.name)) {
				formSubGrupo.addControl(input.name, input.formControl);
			}

			this._configInputFormGroup(input, subGrupoInputs, formGroupModulo);
			this._disableInputsIfNecessary(input, subGrupoInputs);

			/**
			 * Isso aqui é necessário pois formControl passado para os children dos
			 * inputs através de formGroup.get('controlName') faz com que ocorra erros
			 * de control não encontrado ou undefined no componente de form.
			 *
			 * Ex. de config com formControl com formGroup: Habilitar Configuração Gymbot
			 */
			if (input.children) {
				input.children.forEach((children) => {
					if (children.formControl && !children.formControl.parent) {
						this.prepareInputFormControl(
							children,
							subGrupoInputs,
							currentValues
						);
					}
				});
			}
			return input;
		}
		if (currentValues && input.name && !(input.name in currentValues)) {
			throw new Error(
				`O valor atual da configuração ${input.name} não foi encontrada no retorno da API`
			);
		}
		const formControl = new FormControl(
			this.configValue(currentValues, input.name),
			input.validators
		);
		input.formControl = formControl;

		if (!formSubGrupo.contains(input.name)) {
			formSubGrupo.addControl(input.name, input.formControl);
		}

		if (!formGroupModulo.contains(input.name)) {
			formGroupModulo.addControl(input.name, input.formControl);
		}

		this._configInputFormGroup(input, subGrupoInputs, formGroupModulo);
		this._disableInputsIfNecessary(input, subGrupoInputs);

		if (input.children) {
			input.children.forEach((children) => {
				this.prepareInputFormControl(children, subGrupoInputs, currentValues);
			});
		}
	}

	private _configInputFormGroup(
		input: ConfigItem,
		subGrupoInputs: SubGrupoInputs,
		formGroupModulo: FormGroup
	) {
		if (subGrupoInputs.subgrupo === ConfigModuloSubGroup.ADM_CAMPOS_CLIENTE) {
			input.formGroup = this.configAdmService.formGroupCamposCliente;
		} else if (
			subGrupoInputs.subgrupo === ConfigModuloSubGroup.ADM_CAMPOS_VISITANTE
		) {
			input.formGroup = this.configAdmService.formGroupCamposVisitante;
		} else if (
			subGrupoInputs.subgrupo === ConfigModuloSubGroup.ADM_CAMPOS_COLABORADOR
		) {
			input.formGroup = this.configAdmService.formGroupCamposColaborador;
		} else {
			input.formGroup = formGroupModulo;
		}
	}

	private _disableInputsIfNecessary(input, subGrupoInputs) {
		if (subGrupoInputs.subgrupo.modulo === ConfigModulo.ADM) {
			if (!this._possuiPermissaoAlterarConfigAdm()) {
				input.formControl.disable({ onlySelf: true, emitEvent: false });
				input.formControl.updateValueAndValidity();
				input.disabled = true;
			}
		}

		if (subGrupoInputs.subgrupo.modulo === ConfigModulo.TRE) {
			if (!this._possuiPermissaoAlterarConfigTreino()) {
				input.formControl.disable({ onlySelf: true, emitEvent: false });
				input.formControl.updateValueAndValidity();
				input.disabled = true;
			}
		}

		if ([ConfigModulo.CRM].includes(subGrupoInputs.subgrupo.modulo)) {
			if (!this._possuiPermissaoAlterarConfigCrm()) {
				input.formControl.disable({ onlySelf: true, emitEvent: false });
				input.formControl.updateValueAndValidity();
				input.disabled = true;
			}
		}

		if (subGrupoInputs.subgrupo.modulo === ConfigModulo.FIN) {
			if (!this._possuiPermissaoAlterarConfigFin()) {
				input.formControl.disable({ onlySelf: true, emitEvent: false });
				input.formControl.updateValueAndValidity();
				input.disabled = true;
			}
		}
	}

	private prepareInputs(): SubGrupoInputs[] {
		return this.getConfigs().map((subGrupoInputs) => {
			const currentValues = this.getCurrentValues(
				subGrupoInputs.subgrupo.modulo
			);
			subGrupoInputs.inputs = subGrupoInputs.inputs.map((input, index) => {
				this.prepareInputFormControl(input, subGrupoInputs, currentValues);
				this.prepareInputCodes(subGrupoInputs, input, index);
				return {
					...input,
					subGrupoCodigo: subGrupoInputs.subgrupo.codigo,
					subGrupoNome: subGrupoInputs.subgrupo.nome,
					moduloCodigo: subGrupoInputs.subgrupo.modulo.codigo,
					moduloNome: subGrupoInputs.subgrupo.modulo.nome,
				};
			});
			return subGrupoInputs;
		});
	}

	prepareInputCodes(
		subGrupoInputs: SubGrupoInputs,
		input: ConfigItemBase,
		index: number,
		parentCode: string | boolean = null
	) {
		if (input.codigo === false) {
			return;
		}
		if (parentCode) {
			input.codigo = `${parentCode}.${index + 1}`;
		} else {
			input.codigo = `${subGrupoInputs.subgrupo.modulo.codigo}.${
				subGrupoInputs.subgrupo.codigo
			}.${index + 1}`;
		}
		if (input.children) {
			input.children.forEach((configChildren, index) => {
				this.prepareInputCodes(
					subGrupoInputs,
					configChildren,
					index,
					input.codigo
				);
			});
		}
	}

	private configValue(currentValues: Configuracao, name: string): any {
		if (currentValues && (currentValues[name] === 0 || currentValues[name])) {
			return currentValues[name];
		} else {
			return null;
		}
	}

	getCurrentValues(modulo: ConfigModulo): Configuracao {
		const currentValues = this.currentValues.get(modulo);

		if (currentValues === undefined || currentValues === null) {
			throw new Error(
				`Configurações atuais do modulo ${modulo.nome} não foram encontradas`
			);
		}

		return currentValues;
	}

	public getConfigs(): SubGrupoInputs[] {
		return [
			...this.configAdmService.getInputs(),
			...this.configCrmService.getInputs(),
			...this.configFinanceiroService.getInputs(),
			...this.configTreinoService.getInputs(),
			...this.configIaService.getInputs(),
		];
	}

	public possuiTodasAsPermissoes() {
		return (
			this._possuiPermissaoAlterarConfigAdm() &&
			this._possuiPermissaoAlterarConfigCrm() &&
			this._possuiPermissaoAlterarConfigCrm() &&
			this._possuiPermissaoAlterarConfigFin()
		);
	}

	dirtyControls(formGroup: FormGroup) {
		const controlsDirty = Object.keys(formGroup.controls).filter(
			(controlName) => formGroup.controls[controlName].dirty
		);
		return controlsDirty.length > 0;
	}

	private _disableModulo(modulo: ConfigModulo) {
		switch (modulo) {
			case ConfigModulo.ADM:
				modulo.enabled =
					this.sessionService.isModuloHabilitado(PlataformaModulo.NZW) ||
					this.sessionService.isModuloHabilitado(PlataformaModulo.ZW);
				break;
			case ConfigModulo.CRM:
				modulo.enabled =
					this.sessionService.isModuloHabilitado(PlataformaModulo.NCRM) ||
					this.sessionService.isModuloHabilitado(PlataformaModulo.CRM);
				break;
			case ConfigModulo.FIN:
				modulo.enabled = this.sessionService.isModuloHabilitado(
					PlataformaModulo.FIN
				);
				break;
			case ConfigModulo.IA:
				modulo.enabled = this.sessionService.isModuloHabilitado(
					PlataformaModulo.IA
				);
				break;
			case ConfigModulo.TRE:
				modulo.enabled =
					this.sessionService.isModuloHabilitado(PlataformaModulo.NTR) ||
					this.sessionService.isModuloHabilitado(PlataformaModulo.TR);
				break;
		}
	}
}
