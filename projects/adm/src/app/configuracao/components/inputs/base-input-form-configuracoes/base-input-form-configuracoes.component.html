<div
	[class]="
		size === 'large' ? 'form-group' : 'config-input-container form-group'
	"
	[ngClass]="{ 'has-danger': showError }">
	<pacto-cat-switch
		*ngIf="type === 'checkbox'"
		[id]="name"
		[control]="control"
		(valueChange)="onValueChange($event)"></pacto-cat-switch>

	<pacto-cat-form-input
		*ngIf="type === 'number' || type === 'text' || type === 'password'"
		[id]="id"
		[label]="label"
		[type]="type"
		[checkTouched]="false"
		[control]="control"
		[min]="min"
		[max]="max"
		[placeholder]="placeholder ? placeholder : ''"
		[maxlength]="maxlength"
		[errorMsg]="errorMsg"
		[textMask]="textMask ? { mask: textMask, guide: false } : { mask: false }"
		(valueChange)="onValueChange($event)"
		[placeholder]="placeholder ? placeholder : ''"></pacto-cat-form-input>

	<pacto-cat-form-select
		*ngIf="type === 'select'"
		[control]="control"
		[items]="options"
		[disabled]="disabled"
		(valueChange)="onValueChange($event)"></pacto-cat-form-select>

	<pacto-cat-radio-group
		*ngIf="type === 'radio'"
		[formControl]="control"
		(change)="onValueChange($event)"
		[disabled]="disabled">
		<pacto-cat-radio *ngFor="let option of options" [value]="option.id">
			<label style="margin-bottom: 0 !important">{{ option.label }}</label>
		</pacto-cat-radio>
	</pacto-cat-radio-group>

	<pacto-cat-select-filter
		*ngIf="type === 'selectfilter'"
		[id]="name"
		[endpointUrl]="endpointUrl"
		[options]="options"
		[initOption]="initOption"
		[control]="control"
		[placeholder]="placeholder"
		[resposeParser]="responseParser"
		[paramBuilder]="paramBuilder"
		[idKey]="idKey"
		[labelKey]="labelKey"
		[ngClass]="size ? 'large' : ''"
		(valueChange)="onValueChange($event)"></pacto-cat-select-filter>

	<pacto-cat-form-multi-select-filter
		*ngIf="type === 'multiselectfilter'"
		[id]="name"
		[endpointUrl]="endpointUrl"
		[options]="options"
		[control]="control"
		[placeholder]="placeholder"
		[resposeParser]="responseParser"
		[paramBuilder]="paramBuilder"
		[idKey]="idKey"
		[labelKey]="labelKey"
		(valueChange)="onValueChange($event)"></pacto-cat-form-multi-select-filter>

	<pacto-cat-datepicker
		*ngIf="type === 'date'"
		[label]="label"
		[formControl]="control"
		[dateFilter]="dateFilter"
		(valueChange)="onValueChange($event)"></pacto-cat-datepicker>

	<pacto-cat-form-textarea
		*ngIf="type === 'textarea'"
		[ngClass]="size ? 'large' : ''"
		[rows]="rows"
		[control]="control"
		[placeholder]="placeholder ? placeholder : ''"
		[id]="id"
		[disabled]="disabled"
		(valueChange)="onValueChange($event)"></pacto-cat-form-textarea>

	<pacto-cat-button
		*ngIf="type === 'button'"
		[id]="name"
		[label]="typeDescription"
		[full]="true"
		[size]="'NORMAL'"
		[icon]="icon"
		[type]="typeButton ? typeButton : 'OUTLINE'"
		[disabled]="disabled"
		(click)="click($event)"></pacto-cat-button>

	<pacto-cat-file-input
		*ngIf="type === 'inputfile'"
		#fileInputComponent
		[urlImage]="urlImage"
		[imageHeight]="150"
		[imageWidth]="150"
		[imgAlt]="'Imagem do Usuário'"
		[id]="name"
		[control]="control"
		[nomeControl]="control"
		[formatos]="formatos"
		[formatosValidos]="formatosValidos"
		(valueChange)="onValueChange($event)"
		(click)="click($event)"></pacto-cat-file-input>
</div>
