@import "projects/ui/assets/import";
@import "projects/ui/assets/ui-kit.scss";

:host {
	display: block;
	height: calc(100vh - 265px);
}

.descontinuadas-card-plain {
	height: max-content;
	max-height: fit-content;
	min-height: 100vh;

	#menus-modulos {
		width: 70%;
	}

	.card-funcionalidades {
		.funcionalidades-header {
			@extend .pct-title4;
			color: var(--color-typography-default-title);
			padding-top: 16px;
		}

		ol {
			margin: 0px;

			.card-funcionalidade {
				cursor: pointer;
				height: 24px;
				@extend .pct-overline1;
				color: var(--color-typography-default-text);
				background-color: var(--color-background-plane-2);
				p {
					margin: 0px;
					text-decoration: underline;
				}
				&:hover {
					@extend .pct-title5;
				}
			}

			.empty {
				@extend .pct-overline2;
				padding-left: 20px;
				color: var(--color-support-grey3);
			}
		}
	}
}

::ng-deep pacto-cat-tabs-transparent {
	.pacto-tabs-wrapper {
		.tabs {
			.tab {
				width: 100% !important;
			}
		}
	}
}

.row {
	justify-content: space-between;
}
