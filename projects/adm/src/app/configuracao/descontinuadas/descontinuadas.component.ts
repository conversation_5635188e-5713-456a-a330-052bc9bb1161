import { Component, OnInit } from "@angular/core";
import { DescontinuadasRoutesConfigService } from "./descontinuadas-routes-config.service";
import { Observable } from "rxjs";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import { PlataformaConfigService } from "ui-kit/public-api";

enum Modulo {
	ADM = 0,
	TREINO = 1,
	//adicionar mais modulos aqui
}

@Component({
	selector: "pacto-descontinuadas",
	templateUrl: "./descontinuadas.component.html",
	styleUrls: ["./descontinuadas.component.scss"],
})
export class DescontinuadasComponent implements OnInit {
	moduloAtual: Modulo = Modulo.ADM;
	categorias;
	tabIndex: number = 0;
	constructor(
		private descontinuadasRoutesConfigService: DescontinuadasRoutesConfigService,
		private layoutNavigationService: LayoutNavigationService
	) {}

	ngOnInit() {
		this.tabClickHandler({ index: 0 });
	}

	tabClickHandler(event: {
		index: number;
		previous?: string;
		next?: string;
		previousIndex?: number;
	}) {
		this.tabIndex = Number(event.index);
		this.moduloAtual = this.tabIndex;
		this.loadEntriesByModulo(this.moduloAtual);
	}

	loadEntriesByModulo(moduloAtual) {
		let dados;
		switch (moduloAtual) {
			case Modulo.ADM:
				dados = this.descontinuadasRoutesConfigService.AdmDescontinuadas();
				break;
			case Modulo.TREINO:
				dados = this.descontinuadasRoutesConfigService.TreinoDescontinuadas();
				break;
			default:
				dados = this.descontinuadasRoutesConfigService.AdmDescontinuadas();
				break;
		}
		this.categorias = [
			{ titulo: "Cadastro", itens: dados.cadastros },
			{ titulo: "Relatório", itens: dados.relatorios },
			{ titulo: "Operações", itens: dados.operacoes },
			{ titulo: "Outros", itens: dados.outros },
		];
	}

	redirectTo(item) {
		const externalLink = this.layoutNavigationService.redirectToModule(
			item.module,
			item
		);
		this.layoutNavigationService.makeRedirect(externalLink as Observable<any>);
	}
}
