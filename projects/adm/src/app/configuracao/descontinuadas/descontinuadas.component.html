<pacto-cat-card-plain class="descontinuadas-card-plain">
	<pacto-cat-tabs-transparent
		#tabsMenus
		(activateTab)="tabClickHandler($event)"
		[tabIndex]="this.tabIndex"
		id="menus-modulos">
		<ng-template
			label="ADM"
			pactoTabTransparent="adm"
			tabImage="./assets/images/logo-adm-whitebg.svg"></ng-template>
		<ng-template
			label="TREINO"
			pactoTabTransparent="treino"
			tabImage="./assets/images/logo-treino-whitebg.svg"></ng-template>
		<!-- caso nescessario criar partir dos exemplos acima e colocar no enum Modulos-->
	</pacto-cat-tabs-transparent>
	<div class="card-funcionalidades">
		<ng-container *ngFor="let categoria of categorias">
			<p class="funcionalidades-header">{{ categoria.titulo }}</p>
			<ol *ngIf="categoria.itens.length; else emptyItens">
				<ng-container *ngFor="let item of categoria.itens">
					<li
						*ngIf="item.permitido"
						class="card-funcionalidade"
						(click)="redirectTo(item)">
						<div class="row">
							<div class="col-md-9 mr-auto">
								<p>{{ "menu.nav.all." + item.id + ".name" | translate }}</p>
							</div>
						</div>
					</li>
				</ng-container>
			</ol>
			<ng-template #emptyItens>
				<p class="empty">Nenhum item</p>
			</ng-template>
		</ng-container>
	</div>
</pacto-cat-card-plain>

<!-- 
<ng-container
	*ngIf="menu.permitido && !menu?.inativo && !menu?.route?.internalLink">
	<a
		*ngIf="menu?.route?.externalLink && isString(menu.route.externalLink)"
		[attr.data-cy]="menu.favoriteIdentifier"
		[href]="asString(menu.route.externalLink)"
		[target]="menu?.route?.openInNewTab ? '_blank' : '_self'"
		class="pacto-item-menu a2menu">
		<div class="pacto-item-menu-content">
			{{"menu.nav.all." + item.id + ".name" | translate}}
		</div>
	</a>
	<div
		(click)="redirectTo(asObservable(menu.route.externalLink))"
		*ngIf="
			menu?.route?.externalLink &&
			!isString(menu?.route?.externalLink) &&
			menu.permitido &&
			!menu?.inativo
		"
		class="pacto-item-menu a3menu">
		<div class="pacto-item-menu-content">
			{{"menu.nav.all." + item.id + ".name" | translate}}
		</div>
	</div>
</ng-container> -->
