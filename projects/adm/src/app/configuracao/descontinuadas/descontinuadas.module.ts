import { ConfiguracoesGuard } from "@base-core/guards/configuracoes.guard";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RouterModule, Routes } from "@angular/router";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DescontinuadasComponent } from "./descontinuadas.component";
import { TranslateModule } from "@ngx-translate/core";

const routes: Routes = [
	{
		path: "",
		children: [
			{
				path: "",
				pathMatch: "full",
				component: DescontinuadasComponent,
			},
		],
	},
];

@NgModule({
	declarations: [DescontinuadasComponent],
	imports: [
		CommonModule,
		TranslateModule,
		RouterModule.forChild(routes),
		BaseSharedModule,
	],
})
export class DescontinuadasModule {}
