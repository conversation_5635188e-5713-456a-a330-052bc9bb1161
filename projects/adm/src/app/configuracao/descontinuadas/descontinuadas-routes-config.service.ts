import { Component, Injectable } from "@angular/core";
import {
	PermissaoService,
	PlataformModuleConfig,
	PlatformLink,
	PlatformMenuItem,
} from "pacto-layout";

@Injectable({
	providedIn: "root",
})
export class DescontinuadasRoutesConfigService {
	constructor(private permissaoService: PermissaoService) {}

	public AdmDescontinuadas() {
		return {
			cadastros: [
				this.antigoPlano,
				this.antigoTurmaLista,
				this.antigoPinpadMenuItem,
				this.cadastroMovimentoProdutoMenuItem,
				this.antigoProdutoMenuItem,
				this.antigoFormaPagamentoMenuItem,
				this.antigoAmbienteMenuItem,
				this.antigoTipoRemessa,
				this.antigoTipoModalidade,
				this.antigoTipoRetorno,
				this.antigoNivelTurmaMenuItem,
				this.antigoTaxaComissaoMenuItem,
				this.antigoCampanhaCupomDesconto,
				this.antigoCampanhaClubeVantagensMenuItem,
				this.antigoLancamentoProdutoColetivoMenuItem,
				this.antigoCompraEstoqueMenuItem,
			],
			relatorios: [
				this.antigoFechamentoCaixaMenuItem,
				this.antigoFechamentoAcessosMenuItem,
				this.antigoPosicaoEsqtoqueMenuItem,
				this.antigoRelatorioDescontoOcupacaoMenuItem,
				this.antigoPedidosPinpadMenuItem,
				this.antigoComissaoConsultorMenuItem,
				this.relatorioClienteComCobrancaAutomaticaBloqueadaMenuItem,
			],
			operacoes: [
				this.antigoGestaoTurma,
				this.antigoModeloOrcamento,
				this.diariaMenuItem,
			],
			outros: [],
		};
	}

	public TreinoDescontinuadas() {
		return {
			cadastros: [],
			relatorios: [],
			operacoes: [],
			outros: [],
		};
	}

	get diariaMenuItem(): PlatformMenuItem {
		return {
			id: "diaria_descontinuadas",
			permissaoAdm: "4.01 - Aula Avulsa",
			module: PlataformModuleConfig.ADM_LEGADO,
			permitido: this.permissaoService.temRecursoAdm("4.01"),
			route: {
				queryParams: {
					funcionalidadeNome: "DIARIA",
					jspPage: "diariaForm.jsp",
				},
			},
		};
	}

	get antigoPlano(): PlatformMenuItem {
		return {
			id: "plano_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permitido: true,
			route: {
				queryParams: {
					urlRedirect: "uriPlano",
					jspPage: "planoCons.jsp",
					openAsPopup: true,
					windowWidth: 1070,
					windowHeight: 600,
				},
			},
		};
	}

	get antigoFechamentoCaixaMenuItem(): PlatformMenuItem {
		return {
			id: "fechamento_caixa_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "6.01 - Fechamento de Caixa Por Operador",
			permitido: this.permissaoService.temPermissaoAdm("6.01"),
			route: {
				queryParams: {
					jspPage: "relatorio/caixaPorOperador.jsp",
					funcionalidadeNome: "FECHAMENTO_CAIXA_OPERADOR",
					openAsPopup: true,
					windowTitle: "Fechamento de Caixa por Operador",
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get antigoTurmaLista(): PlatformMenuItem {
		return {
			id: "turma_descontinuado",
			permitido: true,
			module: PlataformModuleConfig.ADM_LEGADO,
			route: {
				queryParams: {
					urlRedirect: "uriTurma",
					jspPage: "turmaCons.jsp",
					openAsPopup: true,
					windowWidth: 1070,
					windowHeight: 600,
				},
			},
		};
	}

	get cadastroMovimentoProdutoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroMovimentoProduto_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "3.05 - Movimentação do Produto",
			permitido: this.permissaoService.temRecursoAdm("3.05"),
			favoriteIdentifier: "MOVIMENTO_PRODUTO",
			route: {
				queryParams: {
					funcionalidadeNome: "MOVIMENTO_PRODUTO",
					windowTitle: "Movimento do Produto",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoProdutoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroProduto_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "5.10 - Produto",
			permitido: this.permissaoService.temRecursoAdm("5.10"),
			favoriteIdentifier: "PRODUTO_DESCONTINUADO",
			route: {
				queryParams: {
					funcionalidadeNome: "PRODUTO",
					windowTitle: "Produto (Descontinuado)",
					openAsPopup: true,
					windowWidth: 850,
					windowHeight: 595,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoPinpadMenuItem(): PlatformMenuItem {
		return {
			id: "pinpad_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "4.06 - Forma de Pagamento",
			permitido: this.permissaoService.temRecursoAdm("4.06"),
			favoriteIdentifier: "PINPAD_DESCONTINUADO",
			route: {
				queryParams: {
					funcionalidadeNome: "PINPAD",
					windowTitle: "Pinpad (Descontinuado)",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoAmbienteMenuItem(): PlatformMenuItem {
		return {
			id: "ambiente_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "5.01 - Ambiente",
			permitido: this.permissaoService.temRecursoAdm("5.01"),
			favoriteIdentifier: "AMBIENTE_DESCONTINUADO",
			route: {
				queryParams: {
					funcionalidadeNome: "AMBIENTE",
					windowTitle: "Ambiente (Descontinuado)",
					openAsPopup: true,
					windowWidth: 850,
					windowHeight: 595,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoGestaoTurma(): PlatformMenuItem {
		return {
			id: "gestaoTurma_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "9.54 - Permitir acesso ao Gestão de Turma",
			permitido: this.permissaoService.temPermissaoAdm("9.54"),
			favoriteIdentifier: "GESTAO_DE_TURMA",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_DE_TURMA",
					jspPage: "gestaoTurma.jsp",
					// valor url enum = gestaoTurma
				},
			},
		};
	}

	get antigoTipoModalidade(): PlatformMenuItem {
		return {
			id: "cadastroTipoModalidade_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "5.16 - Tipo de Modalidade",
			permitido: this.permissaoService.temRecursoAdm("5.16"),
			favoriteIdentifier: "TIPO_MODALIDADE",
			route: {
				queryParams: {
					funcionalidadeNome: "TIPO_MODALIDADE",
					windowTitle: "Tipo Modalidade",
					openAsPopup: true,
					windowWidth: 1024,
					windowHeight: 700,
				},
			},
		};
	}

	get antigoTipoRetorno(): PlatformMenuItem {
		return {
			id: "cadastroTipoRetorno_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "4.10 - Tipo de Retorno",
			permitido: this.permissaoService.temRecursoAdm("4.10"),
			favoriteIdentifier: "TIPO_RETORNO",
			route: {
				queryParams: {
					funcionalidadeNome: "TIPO_RETORNO",
					windowTitle: "Tipo de Retorno",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get antigoTipoRemessa(): PlatformMenuItem {
		return {
			id: "cadastroTipoRemessa_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "4.09 - Tipo de Remessa",
			permitido: this.permissaoService.temRecursoAdm("4.09"),
			favoriteIdentifier: "TIPO_REMESSA",
			route: {
				queryParams: {
					funcionalidadeNome: "TIPO_REMESSA",
					windowTitle: "Tipo de Remessa",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get antigoModeloOrcamento(): PlatformMenuItem {
		return {
			id: "cadastroModeloOrcamento_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "5.18 - Orçamento Turmas",
			permitido: this.permissaoService.temRecursoAdm("5.18"),
			favoriteIdentifier: "MODELO_ORCAMENTO",
			route: {
				queryParams: {
					funcionalidadeNome: "MODELO_ORCAMENTO",
					windowTitle: "Modelo de Orçamento",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get antigoNivelTurmaMenuItem(): PlatformMenuItem {
		return {
			id: "nivelTurma_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permitido: true,
			favoriteIdentifier: "NIVEL_TURMA_DESCONTINUADO",
			route: {
				queryParams: {
					funcionalidadeNome: "NIVEL_TURMA",
					windowTitle: "Nível de Turma (Descontinuado)",
					openAsPopup: true,
					windowWidth: 800,
					windowHeight: 595,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoFormaPagamentoMenuItem(): PlatformMenuItem {
		return {
			id: "formaPagamento_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "4.06 - Formas de pagamento",
			permitido: this.permissaoService.temRecursoAdm("4.06"),
			favoriteIdentifier: "FORMA_PAGAMENTO_DESCONTINUADO",
			route: {
				queryParams: {
					funcionalidadeNome: "FORMA_PAGAMENTO",
					windowTitle: "Formas de Pagamento (Descontinuado)",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoTaxaComissaoMenuItem(): PlatformMenuItem {
		return {
			id: "taxaComissao_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "4.26 - Taxas de Comissão para Consultor",
			permitido: this.permissaoService.temPermissaoAdm("4.26"),
			favoriteIdentifier: "TAXA_COMISSAO_DESCONTINUADO",
			route: {
				queryParams: {
					funcionalidadeNome: "TAXA_COMISSAO",
					windowTitle: "Taxas de Comissão (Descontinuado)",
					openAsPopup: true,
					windowWidth: 1120,
					windowHeight: 650,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoCampanhaCupomDesconto(): PlatformMenuItem {
		return {
			id: "campanhaCupomDesconto_descontinuado",
			favoriteIdentifier: "CAMPANHA_CUPOM_DESCONTO",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "5.62 - Campanha Cupom Desconto",
			permitido: this.permissaoService.temRecursoAdm("5.62"),
			route: {
				queryParams: {
					funcionalidadeNome: "CAMPANHA_CUPOM_DESCONTO",
					windowTitle: "Campanha Cupom de Desconto",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 750,
				},
			},
		};
	}

	get antigoCampanhaClubeVantagensMenuItem(): PlatformMenuItem {
		return {
			id: "campanhaClubeVantagens_descontinuado",
			permissaoAdm: "5.62 - Campanha Cupom Desconto",
			permitido: this.permissaoService.temRecursoAdm("5.62"),
			favoriteIdentifier: "CLUBE_VANTAGENS_CAMPANHA",
			route: {
				queryParams: {
					funcionalidadeNome: "CLUBE_VANTAGENS_CAMPANHA",
					jspPage: "clubeVantagens.jsp",
				},
			},
		};
	}

	get antigoRelatorioDescontoOcupacaoMenuItem(): PlatformMenuItem {
		return {
			id: "descontoOcupacao_descontinuado",
			permissaoAdm: "9.78 - Relatório de Desconto por Ocupação na Turma",
			permitido: this.permissaoService.temPermissaoAdm("9.78"),
			favoriteIdentifier: "DESCONTO_OCUPACAO_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "DESCONTO_OCUPACAO_TURMAS",
					openAsPopup: true,
					windowTitle: "Desconto por Ocupação na Turmas",
					windowWidth: 1000,
					windowHeight: 760,
				},
			},
		};
	}

	get antigoLancamentoProdutoColetivoMenuItem(): PlatformMenuItem {
		return {
			id: "lancamentoProdutoColetivo_descontinuado",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "2.75 - Permitir acesso ao recurso de Operações Coletivo",
			permitido: this.permissaoService.temPermissaoAdm("2.75"),
			favoriteIdentifier: "LANCAMENTO_PRODUTO_COLETIVO_DESCONTINUADO",
			route: {
				queryParams: {
					funcionalidadeNome: "LANCAMENTO_PRODUTO_COLETIVO",
					windowTitle: "Operações Coletivas (Descontinuado)",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
			hideInSearch: true,
		};
	}

	get antigoFechamentoAcessosMenuItem(): PlatformMenuItem {
		return {
			id: "fechamentoAcessos_descontinuado",
			permissaoAdm: "9.79 - Relatório de Fechamento de Acesso",
			permitido: this.permissaoService.temPermissaoAdm("9.79"),
			favoriteIdentifier: "FECHAMENTO_ACESSOS_ANTIGO",
			module: PlataformModuleConfig.ADM_LEGADO,
			route: {
				queryParams: {
					funcionalidadeNome: "FECHAMENTO_ACESSOS",
					popupFullscreen: true,
					windowTitle: "Fechamento Acessos (Antigo)",
				},
			},
		};
	}

	get antigoPedidosPinpadMenuItem(): PlatformMenuItem {
		return {
			id: "pedidosPinpad_descontinuado",
			permissaoAdm: "9.83 - Relatório de Pedidos Pinpad",
			permitido: this.permissaoService.temPermissaoAdm("9.83"),
			favoriteIdentifier: "PEDIDOS_PINPAD",
			module: PlataformModuleConfig.ADM_LEGADO,
			route: {
				queryParams: {
					funcionalidadeNome: "PEDIDOS_PINPAD",
					popupFullscreen: true,
					windowTitle: "Pedidos Pinpad",
				},
			},
		};
	}

	get antigoCompraEstoqueMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCompra_descontinuado",
			permissaoAdm: "12.01 - Cadastrar Compra",
			permitido: this.permissaoService.temPermissaoAdm("12.01"),
			module: PlataformModuleConfig.ADM_LEGADO,
			favoriteIdentifier: "COMPRA",
			route: {
				queryParams: {
					funcionalidadeNome: "COMPRA",
					windowTitle: "Compra",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get antigoComissaoConsultorMenuItem(): PlatformMenuItem {
		return {
			id: "comissaoConsultor_descontinuado",
			permissaoAdm: "4.27 - Comissão para Consultor",
			permitido: this.permissaoService.temPermissaoAdm("4.27"),
			favoriteIdentifier: "COMISSAO_VARIAVEL",
			module: PlataformModuleConfig.ADM_LEGADO,
			route: {
				queryParams: {
					funcionalidadeNome: "COMISSAO_VARIAVEL",
					openAsPopup: true,
					windowTitle: "Comissão para Consultor",
					windowWidth: 1000,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioClienteComCobrancaAutomaticaBloqueadaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClienteComCobrancaAutomaticaBloqueada_descontinuado",
			permissaoAdm:
				"9.65 - Permitir visualizar o Relatório de clientes com cobrança automática bloqueada",
			permitido: this.permissaoService.temPermissaoAdm("9.65"),
			favoriteIdentifier: "RELATORIO_CLIENTES_COBRANCA_BLOQUEADA",
			module: PlataformModuleConfig.ADM_LEGADO,
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_CLIENTES_COBRANCA_BLOQUEADA",
					openAsPopup: true,
					windowTitle: "Clientes com cobrança automática bloqueada",
					windowWidth: 1024,
					windowHeight: 700,
				},
			},
		};
	}

	get antigoPosicaoEsqtoqueMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroPosicaoEstoque_descontinuado",
			permissaoAdm: "12.08 - Visualizar Posição do Estoque",
			permitido: this.permissaoService.temPermissaoAdm("12.08"),
			module: PlataformModuleConfig.ADM_LEGADO,
			route: {
				queryParams: {
					jspPage: "relatorioEstoqueProduto.jsp",
					funcionalidadeNome: "POSICAO_DO_ESTOQUE",
					windowTitle: "Posição do Estoque",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}
}
