import { DialogConfigModule } from "@adm/configuracao/components/dialog/dialog-config.module";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AjudaComponent } from "./components/ajuda/ajuda.component";
import { RouterModule, Routes } from "@angular/router";
import { V2Component } from "./components/v2/v2.component";
import { UiModule } from "ui-kit";
import { LayoutModule } from "../layout/layout.module";
import { FormConfiguracoesBuilderComponent } from "./components/inputs/form-configuracoes-builder/form-configuracoes-builder.component";
import { FormConfiguracoesInputComponent } from "./components/inputs/form-configuracoes-input/form-configuracoes-input.component";
import { BaseInputFormConfiguracoesComponent } from "./components/inputs/base-input-form-configuracoes/base-input-form-configuracoes.component";
import { TextMaskModule } from "angular2-text-mask";
import { ReactiveFormsModule } from "@angular/forms";
import { FormConfiguracaoesInputModalComponent } from "./components/inputs/form-configuracoes-input/form-configuracaoes-input-modal/form-configuracaoes-input-modal.component";
import { SelectEmpresaDialogComponent } from "./components/dialog/empresa/select-empresa.dialog.component";
import { ConfigRootComponent } from "./components/config-root/config-root.component";
import { NgbModalModule } from "@ng-bootstrap/ng-bootstrap";

const routes: Routes = [
	{
		path: "",
		component: AjudaComponent,
	},
	{
		path: "v2",
		component: ConfigRootComponent,
		children: [
			{
				path: "",
				pathMatch: "full",
				component: V2Component,
			},
			{
				path: "integracoes",
				loadChildren: () =>
					import("./integracoes-v2/integracoes-v2.module").then(
						(m) => m.IntegracoesV2Module
					),
			},
			{
				path: "descontinuadas",
				loadChildren: () =>
					import("./descontinuadas/descontinuadas.module").then(
						(m) => m.DescontinuadasModule
					),
			},
		],
	},
];

@NgModule({
	declarations: [
		FormConfiguracoesBuilderComponent,
		FormConfiguracoesInputComponent,
		BaseInputFormConfiguracoesComponent,
		AjudaComponent,
		V2Component,
		FormConfiguracaoesInputModalComponent,
		SelectEmpresaDialogComponent,
		ConfigRootComponent,
	],
	entryComponents: [
		FormConfiguracaoesInputModalComponent,
		SelectEmpresaDialogComponent,
	],
	exports: [
		FormConfiguracaoesInputModalComponent,
		SelectEmpresaDialogComponent,
	],
	imports: [
		NgbModalModule,
		CommonModule,
		RouterModule.forChild(routes),
		UiModule,
		DialogConfigModule,
		TextMaskModule,
		LayoutModule,
		ReactiveFormsModule,
	],
})
export class ConfiguracaoModule {}
