import { AdmRestService } from "@adm/adm-rest.service";
import { DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ZwBootLancamentoProdutoColetivoService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { PermissaoService } from "pacto-layout";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

@Component({
	selector: "adm-lancamento-produto-coletivo",
	templateUrl: "./lancamento-produto-coletivo.component.html",
	styleUrls: ["./lancamento-produto-coletivo.component.scss"],
})
export class LancamentoProdutoColetivoComponent
	implements OnInit, AfterViewInit, OnDestroy
{
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnProduto", { static: true }) columnProduto: TemplateRef<any>;
	@ViewChild("columnUsuario", { static: true }) columnUsuario: TemplateRef<any>;
	@ViewChild("columnDataLancamento", { static: true })
	columnDataLancamento: TemplateRef<any>;
	@ViewChild("tableLancamentoProdutoColetivo", { static: false })
	tableLancamentoProdutoColetivo: RelatorioComponent;

	tableConfig: PactoDataGridConfig;

	destroyed$: Subject<void> = new Subject<void>();
	filterConfig: GridFilterConfig = { filters: [] };

	constructor(
		private admRest: AdmRestService,
		private datePipe: DatePipe,
		private router: Router,
		private notificationService: SnotifyService,
		private lancamentoProdutoColetivoService: ZwBootLancamentoProdutoColetivoService,
		private ngbModal: NgbModal,
		private permissaoService: PermissaoService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		if (!this.permissaoService.temPermissaoAdm("2.47")) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 2.47 - Realizar venda de produto para grupo específico necessária para acessar esta funcionalidade."
			);
			this.router.navigate(["/adm", "/home"]);
			return;
		}
	}

	ngAfterViewInit() {
		this._initTable();
		this._initFilter();
		this.cd.detectChanges();
	}

	ngOnDestroy() {
		this.destroyed$.next();
		this.destroyed$.complete();
	}

	private _initTable() {
		this.tableConfig = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlZwBack(
				"lancamento-produto-coletivo"
			),
			quickSearch: true,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: true,
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "produto",
					titulo: this.columnProduto,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => v.descricao,
				},
				{
					nome: "usuario",
					titulo: this.columnUsuario,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => v.nome,
				},
				{
					nome: "dataLancamento",
					titulo: this.columnDataLancamento,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyyy"),
				},
			],
			actions: [
				{
					nome: "editLancamentoProdutoColetivo",
					iconClass: "pct pct-edit cor-action-default-able04",
					tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
					actionFn: (row) => this.edit(row.row),
				},
				{
					nome: "deleteLancamentoProdutoColetivo",
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
					actionFn: (row) => this.openModalConfirmacaoExclusao(row),
				},
			],
		});
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editLancamentoProdutoColetivo") {
			this.edit(event.row);
		} else if (event.iconName === "deleteLancamentoProdutoColetivo") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este lançamento?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.delete(event.row);
				}
			})
			.catch((error) => {});
	}

	private _initFilter() {
		this.filterConfig = {
			filters: [
				{
					name: "dataLancamento",
					label: this.traducao.getLabel("filtro-data-lancamento"),
					type: GridFilterType.DS3_DATE,
				},
			],
		};
	}

	edit(lancamentoProdutoColetivo) {
		this.router.navigate([
			"adm",
			"lancamento-produto-coletivo",
			lancamentoProdutoColetivo.codigo,
		]);
	}

	delete(row: any) {
		this.lancamentoProdutoColetivoService
			.delete(row.codigo)
			.pipe(takeUntil(this.destroyed$))
			.subscribe(
				(response) => {
					this.notificationService.success(this.traducao.getLabel("DELETED"));
					if (this.tableLancamentoProdutoColetivo.data.content) {
						this.tableLancamentoProdutoColetivo.data.content =
							this.tableLancamentoProdutoColetivo.data.content.filter(
								(tipoModalidade) => tipoModalidade.codigo !== row.codigo
							);
					}
					this.tableLancamentoProdutoColetivo.reloadData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novo() {
		this.router.navigate(["adm", "lancamento-produto-coletivo", "novo"]);
	}
}
