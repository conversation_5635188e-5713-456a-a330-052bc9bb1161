import { AdmRestService } from "@adm/adm-rest.service";
import { AmostraClientesComponent } from "@adm/lancamento-produto-coletivo/amostra-clientes/amostra-clientes.component";
import { DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmLegadoAutorizarAcessoService,
	ApiResponseList,
	Empresa,
	LancamentoProdutoColetivo,
	Modalidade,
	Plano,
	Produto,
	tipoLancamentoOptions,
	TipoLancamentoProdutoColetivo,
	TipoLancamentoProdutoColetivoEnum,
	ZwBootLancamentoProdutoColetivoService,
	ZwBootModalidadeService,
	ZwBootPlanoService,
	ZwBootProdutoService,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { PermissaoService } from "pacto-layout";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	DialogAutorizacaoAcessoComponent,
	LoaderService,
	TraducoesXinglingComponent,
} from "ui-kit";

@Component({
	selector: "adm-lancamento-produto-coletivo-form",
	templateUrl: "./lancamento-produto-coletivo-form.component.html",
	styleUrls: ["./lancamento-produto-coletivo-form.component.scss"],
})
export class LancamentoProdutoColetivoFormComponent
	implements OnInit, AfterViewInit, OnDestroy
{
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;

	private _destroyed$: Subject<void> = new Subject<void>();

	form: FormGroup;
	lancamentoProdutoColetivo: LancamentoProdutoColetivo =
		new LancamentoProdutoColetivo();
	id: string;
	isEdicao = false;
	urlLog: string;
	recurso: PerfilAcessoRecurso;
	tituloFormulario = "Novo lançamento de produto coletivo";
	codigoControl: FormControl = new FormControl();
	produtos: Array<Produto> = new Array<Produto>();
	planos: Array<Plano> = new Array<Plano>();
	modalidades: Array<Modalidade> = new Array<Modalidade>();
	tiposLancamento: Array<TipoLancamentoProdutoColetivo> =
		new Array<TipoLancamentoProdutoColetivo>();
	tipoLancamentoColetivoEnum = TipoLancamentoProdutoColetivoEnum;
	parcelas: Array<{ value: number; label: string }> = new Array<{
		value: number;
		label: string;
	}>();
	meses: Array<{ value: number; label: string }> = new Array<{
		value: number;
		label: string;
	}>();
	mensagemLancamento: string;

	constructor(
		private lancamentoProdutoColetivoService: ZwBootLancamentoProdutoColetivoService,
		private planoService: ZwBootPlanoService,
		private modalidadeService: ZwBootModalidadeService,
		private produtoService: ZwBootProdutoService,
		private fb: FormBuilder,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private admRest: AdmRestService,
		private datePipe: DatePipe,
		private dialog: MatDialog,
		private permissaoService: PermissaoService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private ngbModal: NgbModal,
		private loaderService: LoaderService
	) {}

	ngOnInit() {
		if (!this.permissaoService.temPermissaoAdm("2.47")) {
			this.notificationService.error(
				"Seu usuário não possui a permissão 2.47 - Realizar venda de produto para grupo específico necessária para acessar esta funcionalidade."
			);
			this.router.navigate(["/adm", "/home"]);
			return;
		}
		this._initParcelas();
		this._initForm();
		this._initId();
		this._initEdit();
		this._listenFormChanges();
	}

	ngAfterViewInit() {
		this._initTipoLancamento();
		this._initMeses();
	}

	ngOnDestroy() {
		this._destroyed$.next();
		this._destroyed$.complete();
	}

	private _initId() {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();
		this.codigoControl.setValue(this.id);
		this.isEdicao = !!this.id && this.id !== "novo";
	}

	private _initForm() {
		this.form = this.fb.group({
			descricao: ["", [Validators.required]],
			produto: [null, [Validators.required]],
			valor: [null, [Validators.required]],
			tipoLancamento: [
				TipoLancamentoProdutoColetivoEnum.DATA_ESPECIFICA,
				[Validators.required],
			],
			dataEspecifica: [null],
			mes: [null],
			parcela: [this.parcelas[0]],
			plano: [null],
			vigenciaContratoInicio: [null],
			vigenciaContratoFim: [null],
			modalidade: [null],
			nrVezesParcelar: [this.parcelas[0]],
			dataFim: [null],
			matriculas: [""],
			lancarAoGravar: [false],
			ignorarJaLancados: [false],
		});
	}

	private _initEdit() {
		if (this.isEdicao) {
			this.tituloFormulario = "Editar lançamento de produto coletivo";
			this._loadById();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`log/LANCAMENTOPRODUTOCOLETIVO/${this.id}`
			);
		} else {
			this.lancamentoProdutoColetivo.empresa = {
				codigo: this.sessionService.currentEmpresa.codigo,
				nome: this.sessionService.currentEmpresa.nome,
			} as Empresa;

			this.loadProdutos();
			this.loadPlanos();
			this.loadModalidades();
		}
	}

	private _loadById() {
		this.lancamentoProdutoColetivoService
			.find(this.id)
			.pipe(takeUntil(this._destroyed$))
			.subscribe((response) => {
				this.lancamentoProdutoColetivo = response.content;
				this._patchForm();

				this.loadProdutos();
				this.loadPlanos();
				this.loadModalidades();
				this._buildMensagemLancamento(this.form.value);
				this.cd.detectChanges();
			});
	}

	private _patchForm() {
		this.form.patchValue(this.lancamentoProdutoColetivo, { emitEvent: false });
	}

	loadProdutos(searchValue?: string) {
		this.produtoService
			.consultarMinimal({
				filters: {
					tiposDesconsiderar: ["TR"],
					quicksearchValue: searchValue,
				},
				page: 0,
				size: 50,
			})
			.pipe(takeUntil(this._destroyed$))
			.subscribe((response: ApiResponseList<Produto>) => {
				this.produtos = response.content;
				this.cd.detectChanges();
			});
	}

	loadPlanos(searchValue?: string) {
		this.planoService
			.consultarMinimal({
				filters: {
					quicksearchValue: searchValue,
					codigoEmpresa: this.lancamentoProdutoColetivo.empresa.codigo,
				},
				page: 0,
				size: 50,
			})
			.pipe(takeUntil(this._destroyed$))
			.subscribe((response: ApiResponseList<Plano>) => {
				this.planos = response.content;
				this.cd.detectChanges();
			});
	}

	loadModalidades(searchValue?: string) {
		this.modalidadeService
			.consultarMinimal({
				filters: {
					quicksearchValue: searchValue,
					codigoEmpresa: this.lancamentoProdutoColetivo.empresa.codigo,
				},
				page: 0,
				size: 50,
			})
			.pipe(takeUntil(this._destroyed$))
			.subscribe((response: ApiResponseList<Modalidade>) => {
				this.modalidades = response.content;
				this.cd.detectChanges();
			});
	}

	private _initTipoLancamento() {
		this.tiposLancamento = [...tipoLancamentoOptions()];
		this.tiposLancamento.forEach(
			(tipoLancamento: TipoLancamentoProdutoColetivo) => {
				tipoLancamento.label = this.traducao.getLabel(
					`tipo-lancamento-${tipoLancamento.labelId}`
				);
			}
		);
	}

	private _initParcelas() {
		for (let i = 1; i <= 12; i++) {
			this.parcelas.push({
				value: i,
				label: `${i}`,
			});
		}
	}

	private _initMeses() {
		for (let i = 1; i <= 12; i++) {
			this.meses.push({
				value: i,
				label: this.traducao.getLabel(`mes-${this.getMothLabelByIndex(i)}`),
			});
		}
	}

	private getMothLabelByIndex(index: number) {
		const date = new Date().setMonth(index - 1);
		return this.datePipe
			.transform(date, "MMMM")
			.normalize("NFD")
			.toLowerCase()
			.replace(/[\u0300-\u036f]/g, "");
	}

	private _listenFormChanges() {
		this.form.valueChanges
			.pipe(takeUntil(this._destroyed$))
			.subscribe((value) => {
				this._buildMensagemLancamento(value);
			});

		this.form
			.get("tipoLancamento")
			.valueChanges.pipe(takeUntil(this._destroyed$))
			.subscribe((value) => {
				this.mensagemLancamento = "";
			});

		this.form
			.get("produto")
			.valueChanges.pipe(takeUntil(this._destroyed$))
			.subscribe((value) => {
				if (value) {
					this.form.get("valor").setValue(value.valorFinal);
				}
			});
	}

	private _buildMensagemLancamento(value) {
		if (value.produto) {
			this.mensagemLancamento = "Lançar o produto";
			if (value.produto && value.produto.codigo !== 0) {
				this.mensagemLancamento += ` ${value.produto.descricao}`;
			}
			if (value.valor) {
				this.mensagemLancamento += ` no valor de R$ ${value.valor}`;
			}
			this.mensagemLancamento += " para todos os alunos ativos";
			if (value.plano && value.plano.codigo !== 0) {
				this.mensagemLancamento += `  do plano ${value.plano.descricao}`;
			}

			if (value.vigenciaContratoInicio && value.vigenciaContratoFim) {
				this.mensagemLancamento += ` com início em ${this.datePipe.transform(
					value.vigenciaContratoInicio,
					"dd/MM/yyyy"
				)} e fim em ${this.datePipe.transform(
					value.vigenciaContratoFim,
					"dd/MM/yyyy"
				)}`;
			}

			if (value.modalidade && value.modalidade.codigo !== 0) {
				this.mensagemLancamento += ` , que tem a modalidade ${value.modalidade.nome}`;
			}

			if (
				value.tipoLancamento ===
					TipoLancamentoProdutoColetivoEnum.DATA_ESPECIFICA &&
				value.dataEspecifica
			) {
				this.mensagemLancamento += `, pro dia ${this.datePipe.transform(
					value.dataEspecifica,
					"dd/MM/yyyy"
				)}.`;
			}

			if (
				value.tipoLancamento === TipoLancamentoProdutoColetivoEnum.MES &&
				value.mes
			) {
				this.mensagemLancamento += `, a cada mês de ${value.mes.label}.`;
			}

			if (
				value.tipoLancamento ===
					TipoLancamentoProdutoColetivoEnum.POR_PARCELA &&
				value.parcela
			) {
				const parcela =
					typeof value.parcela === "object"
						? value.parcela.label
						: value.parcela;
				this.mensagemLancamento += `, a cada parcela de número ${parcela}.`;
			}

			if (value.dataFim) {
				this.mensagemLancamento += ` Válido até ${this.datePipe.transform(
					value.dataFim,
					"dd/MM/yyyy"
				)}.`;
			}

			if (value.lancarAoGravar && value.ignorarJaLancados) {
				this.mensagemLancamento +=
					" Ignorar alunos que já tem produto gerado por este lançamento.";
			}

			if (value.matriculas && value.matriculas.length > 0) {
				this.mensagemLancamento += " Somente para as matrículas informadas.";
			}
		}
	}

	openAmostraClientes(isAmostraCliente: boolean = true) {
		this.dialog.open(AmostraClientesComponent, {
			width: "800px",
			maxWidth: "100%",
			panelClass: "mdl-amostra-cliente",
			data: {
				formValue: this.form.value,
				isAmostraCliente,
				lancamentoProdutoColetivo: this.lancamentoProdutoColetivo,
			},
		});
	}

	openClientesJaLancados() {
		this.openAmostraClientes(false);
	}

	saveUpdate() {
		if (this.form.invalid) {
			this.notificationService.error("Preencha todos os campos obrigatórios.");
			return;
		}
		if (this.form.get("lancarAoGravar").value) {
			const modalConfirmacao: any = this.dialog.open(
				DialogAutorizacaoAcessoComponent,
				{
					disableClose: true,
					id: "autorizacao-acesso",
					autoFocus: false,
				}
			);
			modalConfirmacao.componentInstance.form
				.get("usuario")
				.setValue(this.sessionService.loggedUser.username);
			modalConfirmacao.componentInstance.confirm.subscribe((result) => {
				this.autorizarAcessoService
					.validarPermissao(
						this.sessionService.chave,
						result.data.usuario,
						result.data.senha,
						"LancamentoProdutoColetivo",
						"2.47 - Realizar venda de produto para grupo específico",
						this.sessionService.empresaId
					)
					.pipe(takeUntil(this._destroyed$))
					.subscribe(
						(response: any) => {
							this.lancamentoProdutoColetivo.codigoUsuarioAprovou =
								response.content;
							this._effectiveSaveUpdate();
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta) {
								if (err.meta.messageValue) {
									this.notificationService.error(err.meta.messageValue);
								} else if (err.meta.message) {
									this.notificationService.error(err.meta.message);
								}
							} else {
								this.notificationService.error("Ocorreu um erro desconhecido");
							}
						}
					);
			});
		} else {
			this._effectiveSaveUpdate();
		}
	}

	private _effectiveSaveUpdate() {
		this.loaderService.initForce();
		try {
			const body = {
				...this.lancamentoProdutoColetivo,
				...this.form.value,
			};
			if (this.isEdicao) {
				body.codigo = this.codigoControl.value;
			}
			if (body.parcela && typeof body.parcela === "object") {
				body.parcela = body.parcela.value;
			}
			if (body.nrVezesParcelar && typeof body.nrVezesParcelar === "object") {
				body.nrVezesParcelar = body.nrVezesParcelar.value;
			}

			if (this.isEdicao) {
				this._update(body);
			} else {
				this._save(body);
			}
		} catch (e) {
			throw e;
			this.loaderService.stopForce();
		}
	}

	private _save(body) {
		this.lancamentoProdutoColetivoService
			.save(body)
			.pipe(takeUntil(this._destroyed$))
			.subscribe(
				(response) => {
					this.loaderService.stopForce();
					this.notificationService.success("Lançamento salvo com sucesso");
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					this.loaderService.stopForce();
					const err = httpErrorResponse.error;
					if (err.meta) {
						if (err.meta.messageValue) {
							this.notificationService.error(err.meta.messageValue);
						} else if (err.meta.message) {
							this.notificationService.error(err.meta.message);
						}
					} else {
						this.notificationService.error("Ocorreu um erro desconhecido");
					}
				}
			);
	}

	private _update(body) {
		this.lancamentoProdutoColetivoService
			.update(body)
			.pipe(takeUntil(this._destroyed$))
			.subscribe(
				(response) => {
					this.loaderService.stopForce();
					this.notificationService.success("Lançamento salvo com sucesso");
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					this.loaderService.stopForce();
					const err = httpErrorResponse.error;
					if (err.meta) {
						if (err.meta.messageValue) {
							this.notificationService.error(err.meta.messageValue);
						} else if (err.meta.message) {
							this.notificationService.error(err.meta.message);
						}
					} else {
						this.notificationService.error("Ocorreu um erro desconhecido");
					}
				}
			);
	}

	estornarProdutosEmAberto() {
		if (!this.isEdicao) {
			return;
		}
		const modalConfirmacao: any = this.dialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"EstornoProdutoColetivo",
					"2.48 - Estorno de produtos em aberto deste produto coletivo",
					this.sessionService.empresaId
				)
				.pipe(takeUntil(this._destroyed$))
				.subscribe(
					(response: any) => {
						this.lancamentoProdutoColetivo.codigoUsuarioAprovou =
							response.content;
						this._effectiveEstornarProdutosEmAberto();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta) {
							if (err.meta.messageValue) {
								this.notificationService.error(err.meta.messageValue);
							} else if (err.meta.message) {
								this.notificationService.error(err.meta.message);
							}
						} else {
							this.notificationService.error("Ocorreu um erro desconhecido");
						}
					}
				);
		});
	}

	private _effectiveEstornarProdutosEmAberto() {
		this.lancamentoProdutoColetivoService
			.estornarProdutosLancados(
				this.lancamentoProdutoColetivo.codigo,
				this.lancamentoProdutoColetivo
			)
			.pipe(takeUntil(this._destroyed$))
			.subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("produtos-em-aberto-estornados")
					);
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	openModalConfirmacaoExclusao() {
		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este lançamento?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.delete();
				}
			})
			.catch((error) => {});
	}

	delete() {
		if (!this.isEdicao) {
			return;
		}

		this.lancamentoProdutoColetivoService
			.delete(+this.id)
			.pipe(takeUntil(this._destroyed$))
			.subscribe(
				(response) => {
					this.notificationService.success(this.traducao.getLabel("DELETED"));
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	voltarListagem() {
		this.router.navigate(["adm", "lancamento-produto-coletivo"]);
	}
}
