.compact-form {
	.form-row-centered {
		display: flex;
		align-items: center;
		margin-bottom: 16px;

		.label-column {
			width: 150px;
			text-align: right;
			padding-right: 16px;

			label {
				font-weight: 500;
				margin-bottom: 0;
			}
		}
	}
}

.button-actions-centered {
	display: flex;
	gap: 8px;
	padding-top: unset;
	margin-top: 16px;
}

::ng-deep.mdl-amostra-cliente {
	.mat-dialog-container {
		padding: 0;
	}
}

.lpc-msg-lancamento {
	padding: 9px 16px;
	background-color: var(--color-background-plane-3, hsla(0, 0%, 96%, 1));
	border-radius: 5px;
	margin-top: 16px;
	color: var(
		--color-background-action-default-disable-2,
		hsla(222, 5%, 50%, 1)
	);
	display: flex;
	align-items: center;

	i.pct-alert-circle {
		margin-right: 8px;
	}
}
