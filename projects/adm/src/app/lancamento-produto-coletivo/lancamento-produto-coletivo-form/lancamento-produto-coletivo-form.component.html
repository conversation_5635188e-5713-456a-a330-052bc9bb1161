<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@lancamento-produto-coletivo:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							id="input-lancamento-produto-coletivo-codigo"
							ds3Input
							[formControl]="codigoControl"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>* Descrição:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							id="input-lancamento-produto-coletivo-nome"
							ds3Input
							formControlName="descricao" />
					</ds3-form-field>
				</div>
			</div>

			<!--
				O if aqui é devido ao carregamento dos dados.
				É uma solução temporária para carregar o item selecionado
				O mesmo vale para os outros selects que usam array de api
			-->
			<div class="form-row-centered" *ngIf="produtos && produtos.length > 0">
				<div class="label-column">
					<label>* Produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							id="input-lancamento-produto-coletivo-produto"
							[options]="produtos"
							(searchEvent)="loadProdutos($event.term)"
							placeholder="Selecione o produto"
							valueKey="codigo"
							nameKey="descricao"
							[useFullOption]="true"
							[formControl]="form.get('produto')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Valor:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							id="input-lancamento-produto-coletivo-valor"
							ds3Input
							formControlName="valor"
							currencyMask
							[options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label></label>
				</div>
				<div class="input-column input-column-width-auto">
					<ds3-form-field>
						<ds3-radio-group formControlName="tipoLancamento">
							<ds3-radio
								*ngFor="let tipoLancamento of tiposLancamento"
								[value]="tipoLancamento?.value">
								{{ tipoLancamento?.label }}
							</ds3-radio>
						</ds3-radio-group>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="
					form.get('tipoLancamento').value ===
					tipoLancamentoColetivoEnum.DATA_ESPECIFICA
				">
				<div class="label-column">
					<label>Data de vencimento do produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-input-date
							ds3Input
							[control]="form.get('dataEspecifica')"
							dateType="datepicker"></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="
					form.get('tipoLancamento').value === tipoLancamentoColetivoEnum.MES
				">
				<div class="label-column">
					<label>Mês de vencimento do produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							id="input-lancamento-produto-coletivo-mes-vencimento-produto"
							[options]="meses"
							placeholder="Selecione o mês"
							valueKey="value"
							nameKey="label"
							[useFullOption]="true"
							[formControl]="form.get('mes')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="
					form.get('tipoLancamento').value ===
					tipoLancamentoColetivoEnum.POR_PARCELA
				">
				<div class="label-column">
					<label>Parcela:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							id="input-lancamento-produto-coletivo-parcela"
							[options]="parcelas"
							placeholder="Selecione a parcela"
							valueKey="value"
							nameKey="label"
							[useFullOption]="true"
							[formControl]="form.get('parcela')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered" *ngIf="planos && planos.length > 0">
				<div class="label-column">
					<label>Plano:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							id="input-lancamento-produto-coletivo-plano"
							(searchEvent)="loadPlanos($event.term)"
							[options]="planos"
							placeholder="Selecione o plano"
							valueKey="codigo"
							nameKey="descricao"
							[useFullOption]="true"
							[formControl]="form.get('plano')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Contrato vigência:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-input-date
							ds3Input
							[controlStart]="form.get('vigenciaContratoInicio')"
							[controlEnd]="form.get('vigenciaContratoFim')"
							dateType="dateranger"></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="modalidades && modalidades.length > 0">
				<div class="label-column">
					<label>Modalidade:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							id="input-lancamento-produto-coletivo-modalidade"
							(searchEvent)="loadPlanos($event.term)"
							[options]="modalidades"
							placeholder="Selecione a modalidade"
							valueKey="codigo"
							nameKey="nome"
							[useFullOption]="true"
							[formControl]="form.get('modalidade')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Parcelar em:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							id="input-lancamento-produto-coletivo-parcelar-em"
							[options]="parcelas"
							placeholder="Selecione a quantidade de parcelas"
							valueKey="value"
							nameKey="label"
							[useFullOption]="true"
							[formControl]="form.get('nrVezesParcelar')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="
					form.get('tipoLancamento').value === tipoLancamentoColetivoEnum.MES ||
					form.get('tipoLancamento').value ===
						tipoLancamentoColetivoEnum.POR_PARCELA ||
					form.get('tipoLancamento').value ===
						tipoLancamentoColetivoEnum.CONTRATO_SEM_PRODUTO
				">
				<div class="label-column">
					<label>Válido até:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-input-date
							ds3Input
							[control]="form.get('dataFim')"
							dateType="datepicker"></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Matrículas específicas:</label>
				</div>
				<div
					class="input-column"
					ds3Tooltip="Ao informar uma ou mais matrículas (separadas por vírgula) o sistema vai manter os outros filtros, porém se limitando a estes alunos">
					<ds3-form-field>
						<input
							type="text"
							id="input-lancamento-produto-coletivo-matriculas"
							ds3Input
							formControlName="matriculas"
							placeholder="Digite as matrículas separadas por vírgula" />
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Lançar produtos ao gravar:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-checkbox
							ds3Input
							[formControl]="form.get('lancarAoGravar')"></ds3-checkbox>
					</ds3-form-field>
				</div>
			</div>

			<div class="form-row-centered" *ngIf="form.get('lancarAoGravar').value">
				<div class="label-column">
					<label>Ignorar já lançados:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-checkbox
							ds3Input
							[formControl]="form.get('ignorarJaLancados')"></ds3-checkbox>
					</ds3-form-field>
				</div>
			</div>
		</form>

		<ds3-diviser></ds3-diviser>

		<div class="lpc-msg-lancamento" *ngIf="mensagemLancamento">
			<i class="pct pct-alert-circle"></i>
			<span>{{ mensagemLancamento }}</span>
		</div>

		<div class="button-actions-centered">
			<button
				id="btn-lancamento-produto-coletivo-save"
				type="button"
				ds3-flat-button
				color="secondary"
				*ngIf="isEdicao"
				(click)="openModalConfirmacaoExclusao()">
				Excluir
			</button>
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				[titulo]="'Lançamento produto coletivo'"
				[url]="urlLog"
				class="mr-2"></pacto-log>
			<button
				id="btn-lancamento-produto-coletivo-ver-clientes-lancados"
				type="button"
				*ngIf="lancamentoProdutoColetivo?.jaFoiLancado"
				ds3-outlined-button
				(click)="openClientesJaLancados()">
				Ver clientes já lançados
			</button>
			<button
				id="btn-lancamento-produto-coletivo-amostra-cliente"
				type="button"
				ds3-outlined-button
				(click)="openAmostraClientes()">
				Ver amostra de clientes
			</button>
			<button
				id="btn-lancamento-produto-coletivo-estornar-produtos-aberto"
				type="button"
				ds3-outlined-button
				*ngIf="isEdicao"
				(click)="estornarProdutosEmAberto()">
				Estornar produtos em aberto
			</button>
			<button
				id="btn-lancamento-produto-coletivo-cancel"
				type="button"
				ds3-outlined-button
				(click)="voltarListagem()">
				Cancelar
			</button>
			<button
				id="btn-lancamento-produto-coletivo-save"
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="saveUpdate()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@deleted" xingling="DELETED">Excluído com sucesso!</span>
	<span
		i18n="@@lancamento-produto-coletivo:tipo-lancamento:data-especifica"
		xingling="tipo-lancamento-data-especifica">
		Data específica
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:tipo-lancamento:mes"
		xingling="tipo-lancamento-mes">
		Mês
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:tipo-lancamento:por-parcela"
		xingling="tipo-lancamento-por-parcela">
		Por parcela
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:tipo-lancamento:contrato-sem-produto"
		xingling="tipo-lancamento-contrato-sem-produto">
		Contrato sem produto
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-janeiro" xingling="mes-janeiro">
		Janeiro
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:mes-fevereiro"
		xingling="mes-fevereiro">
		Fevereiro
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-marco" xingling="mes-marco">
		Março
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-abril" xingling="mes-abril">
		Abril
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-maio" xingling="mes-maio">
		Maio
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-junho" xingling="mes-junho">
		Junho
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-julho" xingling="mes-julho">
		Julho
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-agosto" xingling="mes-agosto">
		Agosto
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:mes-setembro"
		xingling="mes-setembro">
		Setembro
	</span>
	<span i18n="@@lancamento-produto-coletivo:mes-outubro" xingling="mes-outubro">
		Outubro
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:mes-novembro"
		xingling="mes-novembro">
		Novembro
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:mes-dezembro"
		xingling="mes-dezembro">
		Dezembro
	</span>
	<span
		i18n="@@lancamento-produto-coletivo:produtos-em-aberto-estornados"
		xingling="produtos-em-aberto-estornados">
		Produtos em aberto foram estornados. Confira o log para saber quem foram os
		estornados.
	</span>
</pacto-traducoes-xingling>
