@import "dist/ui-kit/assets/ds3/colors.var.scss";

.modal-title {
	.modal-title-content {
		display: flex;
		align-items: center;
		justify-content: center;

		h2 {
			margin: 16px;
			color: $supportBlack04;
		}

		button {
			margin-left: auto;
			margin-right: 16px;
		}
	}

	ds3-diviser {
		width: 100%;
	}
}

.modal-content {
	display: flex;

	.ds3-table-footer {
		margin-top: 16px;
		margin-bottom: 16px;
	}

	.ds3-table-head {
		gap: 16px;

		.ds3-table-search {
			i.pct {
				font-size: 1rem;
			}
		}
	}

	.modal-middle-content {
		overflow: auto;
	}
}

@media (max-width: 700px) {
	.modal-content {
		.ds3-table-head {
			flex-wrap: wrap;
			.ds3-table-search {
				width: 100%;
			}
		}
	}
}

.mdl-amostra-cliente-loader {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	width: 100%;
	padding: 16px 0 16px 0;

	img {
		width: 100px;
	}
}
