import {
	AfterViewInit,
	Component,
	ElementRef,
	Inject,
	<PERSON><PERSON><PERSON><PERSON>,
	OnInit,
	ViewChild,
} from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import {
	AmostraCliente,
	ApiResponseList,
	ZwBootLancamentoProdutoColetivoService,
} from "adm-legado-api";
import { ToastrService } from "ngx-toastr";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import { Observable, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataTableStateManager } from "ui-kit";

@Component({
	selector: "adm-amostra-clientes",
	templateUrl: "./amostra-clientes.component.html",
	styleUrls: ["./amostra-clientes.component.scss"],
})
export class AmostraClientesComponent
	implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, AfterViewInit
{
	@ViewChild("middleContent", { static: false })
	middleContent: ElementRef;

	private _destroyed$: Subject<void> = new Subject<void>();
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	amostras: Array<AmostraCliente> = new Array<AmostraCliente>();
	pageSizeOptions = [50, 100, 150];
	totalItems: number = 0;
	isAmostraCliente: boolean = false;

	constructor(
		private lancamentoProdutoColetivoService: ZwBootLancamentoProdutoColetivoService,
		public dialog: MatDialogRef<AmostraClientesComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any,
		private toastrService: ToastrService,
		private navigationService: LayoutNavigationService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService
	) {}

	ngOnInit() {
		this.isAmostraCliente = this.data.isAmostraCliente;
		this._loadData();
	}

	ngAfterViewInit() {
		this._calculateMiddleContentHeight();
	}

	ngOnDestroy() {
		this._destroyed$.next();
		this._destroyed$.complete();
	}

	private _loadData() {
		if (this.data.isAmostraCliente) {
			this._loadAmostraClientes();
			return;
		}
		this._loadClientesJaLancados();
	}

	private _loadAmostraClientes() {
		this.tableState.patchState({ loading: true });

		const body = {
			...this.data.lancamentoProdutoColetivo,
			...this.data.formValue,
		};
		if (body.parcela && typeof body.parcela === "object") {
			body.parcela = body.parcela.value;
		}
		if (body.nrVezesParcelar && typeof body.nrVezesParcelar === "object") {
			body.nrVezesParcelar = body.nrVezesParcelar.value;
		}

		this.lancamentoProdutoColetivoService
			.amostraClientes(body, {
				ignorarJalancados: this.data.ignorarJaLancados,
				page: this.page,
				size: this.size,
			})
			.pipe(takeUntil(this._destroyed$))
			.subscribe({
				next: (response: ApiResponseList<AmostraCliente>) => {
					this.totalItems = response.totalElements;

					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _loadClientesJaLancados() {
		this.tableState.patchState({ loading: true });

		this.lancamentoProdutoColetivoService
			.clientesJaAlcancados(this.data.lancamentoProdutoColetivo.codigo, {
				page: this.page,
				size: this.size,
			})
			.pipe(takeUntil(this._destroyed$))
			.subscribe({
				next: (response: ApiResponseList<AmostraCliente>) => {
					this.totalItems = response.totalElements;

					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	openCliente(matricula) {
		if (!matricula) {
			this.toastrService.error("Matrícula do cliente não encontrada!");
			return;
		}
		this.admCoreApiNegociacaoService.recursoHabilitado("TELA_ALUNO").subscribe({
			next: async (response) => {
				let urlCliente: string;

				if (response) {
					urlCliente = this.navigationService.createNovaPlataformaUrl(
						PlataformModuleConfig.TREINO.idUppercase,
						"/pessoas/perfil-v2/" + matricula
					);
				} else {
					urlCliente = await this.urlLegado(matricula);
				}
				window.open(urlCliente, "_blank");
			},
			error: (err) => {
				console.log(err);
			},
		});
	}

	private async urlLegado(matricula: string) {
		const urlInfo = await (
			this.navigationService.redirectToModule(
				PlataformModuleConfig.ADM_LEGADO,
				{
					id: "admCliente",
					permitido: true,
					route: {
						queryParams: {
							urlRedirect: "uriCliente",
							matriculaCliente: matricula,
							openInNewTab: false,
						},
					},
				}
			) as Observable<{ url?: string; popupInfo?: any }>
		).toPromise();
		return urlInfo.url;
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadAmostraClientes();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadAmostraClientes();
	}

	/**
	 * Esse cálculo deve ser movido para um componente global que funcionará como componente pai para todos os modais
	 * e responsável por responsividade.
	 */
	private _calculateMiddleContentHeight() {
		if (this.middleContent) {
			const hostHeight = window.innerHeight;
			this.middleContent.nativeElement.style.maxHeight = `${
				hostHeight - 260
			}px`;
		}
	}
}
