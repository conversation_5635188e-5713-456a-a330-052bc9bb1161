<div class="modal-title">
	<div class="modal-title-content">
		<h2 class="pct-title4">
			{{ isAmostraCliente ? "Amostra de clientes" : "Clientes já alcançados" }}
		</h2>
		<button ds3-icon-button (click)="dialog.close()">
			<i class="pct pct-x"></i>
		</button>
	</div>
	<ds3-diviser></ds3-diviser>
</div>
<div class="modal-content">
	<div class="modal-middle-content" #middleContent>
		<ds3-table>
			<table ds3DataTable [stateManager]="tableState">
				<ng-container ds3TableColumn="pessoa">
					<th *ds3TableHeaderCell>Nome</th>
					<td class="nomeEntidade" *ds3TableCell="let item">
						<button ds3-text-button (click)="openCliente(item?.matricula)">
							{{ item?.nome | titlecase }}
						</button>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="matricula">
					<th *ds3TableHeaderCell class="align-center">Matrícula</th>
					<td class="matricula align-center" *ds3TableCell="let item">
						{{ item?.matricula }}
					</td>
				</ng-container>

				<tr *ds3TableRow></tr>

				<tr *ds3TableEmptyRow class="ds3-table-empty">
					<td>
						<h2>Nenhum item encontrado</h2>
						<p>
							Nenhum item encontrado no período, tente realizar uma nova busca.
						</p>
					</td>
				</tr>

				<tbody *ds3TableLoading>
					<tr>
						<td>
							<div class="mdl-amostra-cliente-loader" role="status">
								<img
									alt="Loading pacto"
									src="pacto-ui/images/gif/loading-pacto.gif" />
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</ds3-table>
	</div>
	<div class="ds3-table-footer">
		<ds3-pagination
			[length]="totalItems"
			[pageSize]="size"
			[pageIndex]="page"
			[pageSizeOptions]="pageSizeOptions"
			(pageSizeChange)="onPageSizeChange($event)"
			(pageChange)="onPageChange($event)"></ds3-pagination>
	</div>
</div>
