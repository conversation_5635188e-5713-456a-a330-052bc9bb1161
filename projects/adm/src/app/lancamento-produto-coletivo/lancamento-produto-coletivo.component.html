<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@lancamento-produto-coletivo:modulo"
	i18n-pageTitle="@@lancamento-produto-coletivo:title"
	modulo="Configurações"
	pageTitle="Lançamento de produto coletivo">
	<div class="table-wrapper" *ngIf="tableConfig">
		<pacto-relatorio
			#tableLancamentoProdutoColetivo
			idSuffix="lancamento-produto-coletivo"
			(btnAddClick)="novo()"
			(rowClick)="edit($event)"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="tableConfig"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="lancamento-produto-coletivo"></pacto-relatorio>
	</div>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@tooltip-edit" xingling="TOOLTIP_EDIT">Editar</span>
	<span i18n="@@tooltip-delete" xingling="TOOLTIP_DELETE">Excluir</span>
	<span i18n="@@deleted" xingling="DELETED">Excluído com sucesso!</span>
	<span
		i18n="@@lancamento-produto-coletivo:filtro-data-lancamento"
		xingling="filtro-data-lancamento">
		Data de lançamento
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@lancamento-produto-coletivo:codigo">Código</span>
</ng-template>

<ng-template #columnDescricao>
	<span i18n="@@lancamento-produto-coletivo:descricao">Descrição</span>
</ng-template>

<ng-template #columnProduto>
	<span i18n="@@lancamento-produto-coletivo:produto">Produto</span>
</ng-template>

<ng-template #columnUsuario>
	<span i18n="@@lancamento-produto-coletivo:usuario">Usuário</span>
</ng-template>

<ng-template #columnDataLancamento>
	<span i18n="@@lancamento-produto-coletivo:data-lancamento">Lançamento</span>
</ng-template>
