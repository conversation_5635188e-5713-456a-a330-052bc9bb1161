import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { LancamentoProdutoColetivoComponent } from "./lancamento-produto-coletivo.component";
import { LancamentoProdutoColetivoFormComponent } from "./lancamento-produto-coletivo-form/lancamento-produto-coletivo-form.component";

const routes: Routes = [
	{
		path: "",
		component: LancamentoProdutoColetivoComponent,
	},
	{
		path: "novo",
		component: LancamentoProdutoColetivoFormComponent,
	},
	{
		path: ":id",
		component: LancamentoProdutoColetivoFormComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class LancamentoProdutoColetivoRoutingModule {}
