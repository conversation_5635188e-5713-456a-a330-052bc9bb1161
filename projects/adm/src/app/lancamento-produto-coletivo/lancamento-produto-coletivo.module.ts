import { LayoutModule } from "@adm/layout/layout.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ComponentsModule, Ds3RadioGroupModule, UiModule } from "ui-kit";
import { AmostraClientesComponent } from "./amostra-clientes/amostra-clientes.component";
import { LancamentoProdutoColetivoFormComponent } from "./lancamento-produto-coletivo-form/lancamento-produto-coletivo-form.component";

import { LancamentoProdutoColetivoRoutingModule } from "./lancamento-produto-coletivo-routing.module";
import { LancamentoProdutoColetivoComponent } from "./lancamento-produto-coletivo.component";

@NgModule({
	declarations: [
		LancamentoProdutoColetivoComponent,
		LancamentoProdutoColetivoFormComponent,
		AmostraClientesComponent,
	],
	imports: [
		CommonModule,
		ReactiveFormsModule,
		TranslateModule,
		LancamentoProdutoColetivoRoutingModule,
		LayoutModule,
		ComponentsModule,
		UiModule,
		Ds3RadioGroupModule,
	],
	entryComponents: [AmostraClientesComponent],
})
export class LancamentoProdutoColetivoModule {}
