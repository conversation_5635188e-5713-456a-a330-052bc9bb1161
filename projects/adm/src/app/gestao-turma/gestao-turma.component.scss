@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ui-kit.scss";

#gt {
	margin: 16px 24px;

	::ng-deep ds3-breadcrumbs {
		margin-bottom: 24px;
	}

	#gt-content {
		margin-top: 16px;
		padding: 0px;
	}

	.gt-content-filters {
		margin: 16px 12px;

		.col {
			padding: 0px 8px;

			&.check {
				display: flex;
				width: 100%;
				align-items: flex-end;
				flex-direction: row;
				padding-bottom: 8px;
			}
		}

		&-buttons {
			display: flex;
			justify-content: flex-end;
			margin-top: 16px;
		}
	}
}

ds3-diviser {
	margin-top: 32px;
}

div[matDialogTitle] {
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	padding: 12px 16px 8px 16px;
	flex-direction: row;
	flex-wrap: wrap;

	span {
		@extend .pct-title4;
		height: 18px;
	}

	ds3-diviser {
		width: calc(100% + 32px);
		margin: 3px -16px 0px -16px;
	}
}

div[matDialogContent] {
	padding: 24px 16px;
}

div[matDialogActions] {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	column-gap: 16px;
	padding: 0px 16px 16px 0px;
}
