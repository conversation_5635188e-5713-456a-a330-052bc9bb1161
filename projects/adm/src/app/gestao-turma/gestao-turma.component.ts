import {
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnInit,
	ViewChild,
	ViewChildren,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatDialog, MatDialogRef } from "@angular/material";
import {
	ds3RouteData,
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
	TableData,
} from "ui-kit";
import { ZwBootGestaoTurmaService } from "adm-legado-api";
@Component({
	selector: "adm-gestao-turma",
	templateUrl: "./gestao-turma.component.html",
	styleUrls: ["./gestao-turma.component.scss"],
})
export class GestaoTurmaComponent implements OnInit {
	constructor(
		private cd: ChangeDetectorRef,
		private dialogService: MatDialog,
		private gestaoTurmaService: ZwBootGestaoTurmaService
	) {}

	routeData: ds3RouteData[] = [
		{ path: ["adm"], title: "Administrativo", isDisabled: true },
		{ path: ["adm", "operacoes"], title: "Operações", isDisabled: true },
		{
			path: ["adm", "operacoes", "gestao-turma"],
			title: "Gestão de turma",
			isDisabled: true,
		},
	];

	tabs = ["Transferir alunos", "Transferir turma"];
	selectedTab;
	loadedData = false;

	@ViewChild("alunosRelatorioRef", { static: false })
	alunosRelatorioRef: RelatorioComponent;
	alunosRelatorioData: PactoDataGridConfig;

	@ViewChild("turmasRelatorioRef", { static: false })
	turmasRelatorioRef: RelatorioComponent;
	turmasRelatorioData: PactoDataGridConfig;

	modalidadeFiltroOptions: Array<any> = [{ label: "-", codigo: 0 }];
	horarioFiltroOptions: Array<any> = [{ label: "-", codigo: 0 }];
	professorFiltroOptions: Array<any> = [{ label: "-", codigo: 0 }];
	turmaFiltroOptions: Array<any> = [{ label: "-", codigo: 0 }];
	turmaTransferenciaOptions: Array<any> = [{ label: "-", codigo: 0 }];
	horarioTransferenciaOptions: Array<any> = [{ label: "-", codigo: 0 }];
	professorTransferenciaOptions: Array<any> = [{ label: "-", codigo: 0 }];

	formFiltros: FormGroup;
	formTransferencia: FormGroup;

	dialogRef: MatDialogRef<any>;
	@ViewChild("dialogTransferir", { static: false })
	dialogTransferir;
	@ViewChild("dialogConfirmacao", { static: false })
	dialogConfirmacao;
	alunos;

	ngOnInit() {
		this.loadModalidades();
		this.changeTab(0);
	}

	changeTab(evt) {
		if (evt === 0) {
			this.formFiltros = new FormGroup({
				turma: new FormControl(),
				horario: new FormControl(),
				modalidade: new FormControl(),
			});
			this.formTransferencia = new FormGroup(
				{
					turma: new FormControl(Validators.required),
					horario: new FormControl(Validators.required),
				},
				Validators.required
			);
			this.formFiltros.get("modalidade").valueChanges.subscribe((v) => {
				this.onModalidadeChange(v);
			});
		}
		if (evt === 1) {
			this.formFiltros = new FormGroup({
				modalidade: new FormControl(),
				professor: new FormControl(),
				turma: new FormControl(),
				naoVigentes: new FormControl(false),
			});
			this.formTransferencia = new FormGroup(
				{
					professor: new FormControl(Validators.required),
				},
				Validators.required
			);
		}
		this.refazerConsulta();
		this.selectedTab = evt;
		this.cd.detectChanges();
	}

	consultar() {
		this.loadedData = true;
		if (this.selectedTab === 0) {
			const filters = this.formFiltros.getRawValue();
			this.gestaoTurmaService
				.listaAlunos(filters, {})
				.toPromise()
				.then((response) => {
					this.alunos = response.content || [];
					this.initTableAluno();
				});
		}
		if (this.selectedTab === 1) {
			this.initTableTurma();
		}
	}

	refazerConsulta() {
		this.formFiltros.reset();
		this.loadedData = false;
	}

	transferir() {
		this.dialogRef = this.dialogService.open(this.dialogTransferir, {
			autoFocus: false,
			width: "600px",
		});
		this.dialogRef.beforeClosed().subscribe((seguir) => {
			if (!!seguir) {
				this.confirmacaoTransferir();
			}
		});
	}

	private initTableAluno() {
		setTimeout(() => {
			this.alunosRelatorioData = new PactoDataGridConfig({
				valueRowCheck: "nome",
				dataAdapterFn: (serverData) => {
					return this.alunos;
				},
				exportButton: false,
				quickSearch: false,
				ghostLoad: true,
				ghostAmount: 5,
				pagination: false,
				showFilters: false,
				columns: [
					{
						nome: "nome",
						titulo: "Nome",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "matricula",
						titulo: "Matrícula",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "reposicao",
						titulo: "Reposição",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "inicioMatri",
						titulo: "Início Matrí.",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "fimMatri",
						titulo: "Fim Matrí.",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "ambiente",
						titulo: "Ambiente",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "professor",
						titulo: "Professor",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "dia",
						titulo: "Dia",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "horario",
						titulo: "Horário",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	private initTableTurma() {
		setTimeout(() => {
			this.turmasRelatorioData = new PactoDataGridConfig({
				exportButton: false,
				quickSearch: false,
				ghostLoad: true,
				ghostAmount: 5,
				pagination: false,
				showFilters: false,
				columns: [
					{
						nome: "turma",
						titulo: "Turma",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "professor",
						titulo: "Professor",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "dia",
						titulo: "Dia",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
					{
						nome: "horario",
						titulo: "Horário",
						ordenavel: true,
						mostrarTitulo: true,
						visible: true,
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	get allowToTransfer() {
		return !(
			(this.alunosRelatorioRef &&
				this.alunosRelatorioRef.selectedItems &&
				this.alunosRelatorioRef.selectedItems.length) ||
			(this.turmasRelatorioRef &&
				this.turmasRelatorioRef.selectedItems &&
				this.turmasRelatorioRef.selectedItems.length)
		);
	}

	confirmacaoTransferir() {
		this.dialogRef = this.dialogService.open(this.dialogConfirmacao, {
			autoFocus: false,
		});
		this.dialogRef.afterClosed().subscribe((v) => {
			if (!!v) {
				const selecionados =
					this.alunosRelatorioRef.selectedItems ||
					this.turmasRelatorioRef.selectedItems;
				const destino = this.formTransferencia.getRawValue();
				const body = { selecionados, destino };

				if (this.selectedTab === 0) {
					// Transferir alunos
					this.gestaoTurmaService.transferirAlunos(body).subscribe(
						(response) => {
							console.log("Alunos transferidos com sucesso", response);
							this.consultar(); // Recarregar dados
						},
						(error) => console.error("Erro ao transferir alunos", error)
					);
				} else {
					// Transferir turmas
					this.gestaoTurmaService.transferirTurmas(body).subscribe(
						(response) => {
							console.log("Turmas transferidas com sucesso", response);
							this.consultar(); // Recarregar dados
						},
						(error) => console.error("Erro ao transferir turmas", error)
					);
				}
			}
		});
	}

	/**
	 * Carrega modalidades do service
	 */
	private loadModalidades() {
		this.gestaoTurmaService.modalidades().subscribe(
			(response) => {
				this.modalidadeFiltroOptions = [
					{ label: "-", codigo: 0 },
					...(response.content || []),
				];
			},
			(error) => console.error("Erro ao carregar modalidades", error)
		);
	}

	/**
	 * Carrega turmas por modalidade
	 */
	onModalidadeChange(codModalidade: number) {
		if (codModalidade && codModalidade !== 0) {
			this.gestaoTurmaService.turmasByModalidade(codModalidade).subscribe(
				(response) => {
					this.turmaFiltroOptions = [
						{ label: "-", codigo: 0 },
						...(response.content || []),
					];
					this.turmaTransferenciaOptions = [
						{ label: "-", codigo: 0 },
						...(response.content || []),
					];
				},
				(error) => console.error("Erro ao carregar turmas", error)
			);

			this.gestaoTurmaService.professoresByModalidade(codModalidade).subscribe(
				(response) => {
					this.professorFiltroOptions = [
						{ label: "-", codigo: 0 },
						...(response.content || []),
					];
					this.professorTransferenciaOptions = [
						{ label: "-", codigo: 0 },
						...(response.content || []),
					];
				},
				(error) => console.error("Erro ao carregar professores", error)
			);
		} else {
			this.turmaFiltroOptions = [{ label: "-", codigo: 0 }];
			this.turmaTransferenciaOptions = [{ label: "-", codigo: 0 }];
			this.professorFiltroOptions = [{ label: "-", codigo: 0 }];
			this.professorTransferenciaOptions = [{ label: "-", codigo: 0 }];
		}
	}

	/**
	 * Carrega horários por turma
	 */
	onTurmaChange(codTurma: number) {
		if (codTurma && codTurma !== 0) {
			this.gestaoTurmaService.horariosByTurma(codTurma).subscribe(
				(response) => {
					this.horarioFiltroOptions = [
						{ label: "-", codigo: 0 },
						...(response.content || []),
					];
					this.horarioTransferenciaOptions = [
						{ label: "-", codigo: 0 },
						...(response.content || []),
					];
				},
				(error) => console.error("Erro ao carregar horários", error)
			);
		} else {
			this.horarioFiltroOptions = [{ label: "-", codigo: 0 }];
			this.horarioTransferenciaOptions = [{ label: "-", codigo: 0 }];
		}
	}
}
