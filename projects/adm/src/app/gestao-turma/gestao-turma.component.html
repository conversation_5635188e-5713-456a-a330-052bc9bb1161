<div id="gt">
	<ds3-breadcrumbs
		[routeData]="routeData"
		[displayArrowReturn]="false"
		[displayArrowSection]="false"></ds3-breadcrumbs>

	<ds3-tabs
		[tabs]="['Transferir alunos', 'Transferir turma']"
		id="gt-tabs"
		(selectedTabEvent)="changeTab($event)"></ds3-tabs>

	<pacto-cat-card-plain id="gt-content">
		<div id="gt-content-transferir">
			<div class="row gt-content-filters">
				<div class="col col-3" *ngIf="formFiltros.controls['modalidade']">
					<ds3-form-field>
						<ds3-field-label>Modalidade</ds3-field-label>
						<ds3-select
							ds3Input
							[options]="modalidadeFiltroOptions"
							[formControl]="formFiltros.controls['modalidade']"
							valueKey="codigo"
							nameKey="label"
							[disabled]="loadedData"></ds3-select>
					</ds3-form-field>
				</div>
				<div class="col col-3" *ngIf="formFiltros.controls['professor']">
					<ds3-form-field>
						<ds3-field-label>Professor</ds3-field-label>
						<ds3-select
							ds3Input
							[options]="professorFiltroOptions"
							[formControl]="formFiltros.controls['professor']"
							valueKey="codigo"
							nameKey="label"
							[disabled]="loadedData"></ds3-select>
					</ds3-form-field>
				</div>
				<div class="col col-3" *ngIf="formFiltros.controls['turma']">
					<ds3-form-field>
						<ds3-field-label>Turma</ds3-field-label>
						<ds3-select
							ds3Input
							[options]="turmaFiltroOptions"
							[formControl]="formFiltros.controls['turma']"
							valueKey="codigo"
							nameKey="label"
							[disabled]="loadedData"></ds3-select>
					</ds3-form-field>
				</div>
				<div class="col col-6" *ngIf="formFiltros.controls['horario']">
					<ds3-form-field>
						<ds3-field-label>Horário</ds3-field-label>
						<ds3-select
							ds3Input
							[options]="horarioFiltroOptions"
							[formControl]="formFiltros.controls['horario']"
							valueKey="codigo"
							nameKey="label"
							[disabled]="loadedData"></ds3-select>
					</ds3-form-field>
				</div>
				<div
					class="col check col-3"
					*ngIf="formFiltros.controls['naoVigentes']">
					<ds3-form-field>
						<ds3-form-field></ds3-form-field>
						<ds3-checkbox
							[formControl]="formFiltros.controls['naoVigentes']"
							ds3Input
							[disabled]="loadedData">
							Somente turmas não vigentes
						</ds3-checkbox>
					</ds3-form-field>
				</div>
				<div class="col col-12 gt-content-filters-buttons">
					<button ds3-outlined-button *ngIf="!loadedData" (click)="consultar()">
						Consultar
					</button>
					<button
						*ngIf="loadedData"
						ds3-outlined-button
						(click)="refazerConsulta()">
						Refazer Consulta
					</button>
				</div>
			</div>
			<div class="transferir" *ngIf="!!loadedData">
				<ds3-diviser></ds3-diviser>

				<pacto-relatorio
					*ngIf="selectedTab == 0"
					#alunosRelatorioRef
					[table]="alunosRelatorioData"
					[enableDs3]="true"
					[showShare]="false"
					[persistirFiltros]="false"
					[customActionsRight]="transferirButtonRef"></pacto-relatorio>
				<pacto-relatorio
					*ngIf="selectedTab == 1"
					#turmasRelatorioRef
					[table]="turmasRelatorioData"
					[enableDs3]="true"
					[showShare]="false"
					[persistirFiltros]="false"
					[customActionsRight]="transferirButtonRef"></pacto-relatorio>
			</div>
		</div>
	</pacto-cat-card-plain>
</div>
<ng-template #transferirButtonRef>
	<button
		ds3-flat-button
		id="gt-content-table-transferir"
		(click)="transferir()"
		[disabled]="allowToTransfer">
		Transferir
	</button>
</ng-template>

<ng-template #dialogTransferir>
	<div matDialogTitle>
		<span>Transferir {{ selectedTab === 0 ? "alunos" : "turmas" }}</span>
		<button ds3-icon-button size="sm" (click)="dialogRef.close(null)">
			<i class="pct pct-x"></i>
		</button>
		<ds3-diviser></ds3-diviser>
	</div>
	<div matDialogContent>
		<ds3-form-field *ngIf="formTransferencia.controls['turma']">
			<ds3-field-label>Turma</ds3-field-label>
			<ds3-select
				ds3Input
				valueKey="codigo"
				nameKey="label"
				[options]="turmaTransferenciaOptions"
				[formControl]="formTransferencia.controls['turma']"></ds3-select>
		</ds3-form-field>
		<ds3-form-field *ngIf="formTransferencia.controls['professor']">
			<ds3-field-label>
				Selecione o professor que irá receber os horários
			</ds3-field-label>
			<ds3-select
				ds3Input
				valueKey="codigo"
				nameKey="label"
				[options]="professorTransferenciaOptions"
				[formControl]="formTransferencia.controls['professor']"></ds3-select>
		</ds3-form-field>
		<ds3-form-field *ngIf="formTransferencia.controls['horario']">
			<ds3-field-label>Horário</ds3-field-label>
			<ds3-select
				ds3Input
				valueKey="codigo"
				nameKey="label"
				[options]="horarioTransferenciaOptions"
				[formControl]="formTransferencia.controls['horario']"></ds3-select>
		</ds3-form-field>
	</div>

	<div matDialogActions>
		<button
			ds3-flat-button
			(click)="dialogRef.close(true)"
			[disabled]="!formTransferencia.valid">
			Transferir
		</button>
	</div>
</ng-template>
<ng-template #dialogConfirmacao>
	<div matDialogTitle>
		<span>
			Confirmar transferência de {{ selectedTab === 0 ? "alunos" : "horários" }}
		</span>
		<button ds3-icon-button size="sm" (click)="dialogRef.close(null)">
			<i class="pct pct-x"></i>
		</button>
		<ds3-diviser></ds3-diviser>
	</div>
	<div matDialogContent>
		<span *ngIf="selectedTab === 0">
			Tem certeza que deseja transferir os alunos
			<ng-container *ngFor="let check of alunosRelatorioRef.selectedItems">
				<br />
				<span>
					{{ check?.nome }}
				</span>
				<br />
			</ng-container>
			<br />
			para {{ formTransferencia.controls["turma"].value }} |
			{{ formTransferencia.controls["horario"].value }}
		</span>
		<span *ngIf="selectedTab === 1">
			Tem certeza que deseja transferir os horários
			<ng-container *ngFor="let check of turmasRelatorioRef.selectedItems">
				<br />
				<span>
					{{ check | json }}
				</span>
				<br />
			</ng-container>
			<br />
			para {{ formTransferencia.controls["professor"].value }}
		</span>
	</div>

	<div matDialogActions>
		<button
			ds3-flat-button
			(click)="dialogRef.close(null)"
			[disabled]="!formTransferencia.valid">
			Cancelar
		</button>
		<button
			ds3-flat-button
			(click)="dialogRef.close(true)"
			[disabled]="!formTransferencia.valid">
			Confirmar transferência
		</button>
	</div>
</ng-template>
