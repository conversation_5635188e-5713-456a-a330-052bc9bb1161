import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { UiModule } from "ui-kit";
import { LayoutModule } from "../layout/layout.module";
import { GestaoTurmaRoutingModule } from "./gestao-turma-routing.module";
import { GestaoTurmaComponent } from "./gestao-turma.component";
import { GestaoTurmaDialogComponent } from "./components/gestao-turma-dialog/gestao-turma-dialog.component";

@NgModule({
	declarations: [
		GestaoTurmaComponent,
		GestaoTurmaDialogComponent,
		GestaoTurmaDialogComponent,
	],
	imports: [
		CommonModule,
		GestaoTurmaRoutingModule,
		BaseSharedModule,
		LayoutModule,
		UiModule,
	],
	entryComponents: [GestaoTurmaDialogComponent],
})
export class GestaoTurmaModule {}
