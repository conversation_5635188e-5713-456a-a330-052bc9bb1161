import { Injectable } from "@angular/core";
import {
	ActivatedRouteSnapshot,
	CanActivate,
	Router,
	RouterStateSnapshot,
} from "@angular/router";
import { Observable } from "rxjs";

import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoRecurso,
	SessionService,
} from "sdk";
import { SnotifyService } from "ng-snotify";

@Injectable({
	providedIn: "root",
})
export class PerfilAcessoGuard implements CanActivate {
	public constructor(
		private session: SessionService,
		private snotifyService: SnotifyService,
		private router: Router
	) {}

	/**
	 * Get the configurations from de server and set at a local instance of each service.
	 */
	canActivate(
		route: ActivatedRouteSnapshot,
		state: RouterStateSnapshot
	): Observable<boolean> | Promise<boolean> | boolean {
		const recursoNecessario: PerfilAcessoRecurso = route.data.recurso;
		const funcionalidadeNecessario: PerfilAcessoFuncionalidade =
			route.data.funcionalidade;

		const perfilAcesso = this.session.perfilUsuario;
		const possuiRecurso = perfilAcesso.possuiRecurso(recursoNecessario);
		const possuiFuncionalidade = perfilAcesso.possuiFuncionalidade(
			funcionalidadeNecessario
		);

		if (!possuiRecurso && recursoNecessario) {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
			return this.router.navigate(["/adm"]);
		} else if (!possuiFuncionalidade && funcionalidadeNecessario) {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
			return this.router.navigate(["/adm"]);
		} else {
			return true;
		}
	}
}
