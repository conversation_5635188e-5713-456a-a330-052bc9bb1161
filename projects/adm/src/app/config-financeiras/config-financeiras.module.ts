import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { ConfigFinanceirasRoutingModule } from "./config-financeiras-routing.module";
import { ContaCorrenteComponent } from "./components/conta-corrente/conta-corrente.component";
import { UiModule } from "ui-kit";
import { ContaCorrenteFormComponent } from "./components/conta-corrente-form/conta-corrente-form.component";
import { BancoComponent } from "../financeiro/components/banco/banco.component";
import { BancoFormComponent } from "../financeiro/components/banco-form/banco-form.component";
import { MetaFinanceiraComponent } from "./components/metas-financeiro/meta-financeira.component";
import { MetaFinanceiraFormComponent } from "./components/metas-financeiro-form/meta-financeira-form.component";
import { MetaFinanceiraConsultaComponent } from "./components/meta-financeira-consulta/meta-financeira-consulta.component";
import { LayoutModule } from "../layout/layout.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { MetaFinanceiraConfirmarExclusaoComponent } from "./components/meta-financeira-confirmar-exclusao/meta-financeira-confirmar-exclusao.component";
import { MetaFinanceiraConfirmarExclusaoListagemComponent } from "./components/meta-financeira-confirmar-exclusao-listagem/meta-financeira-confirmar-exclusao-listagem.component";
import { AdquirenteComponent } from "./components/adquirente/adquirente/adquirente.component";
import { AdquirenteFormComponent } from "./components/adquirente/adquirente-form/adquirente-form.component";
import { OperadoraCartaoFormComponent } from "./components/operadora-cartao-form/operadora-cartao-form.component";
import { OperadoraCartaoListaComponent } from "./components/operadora-cartao-lista/operadora-cartao-lista.component";
import { FormasPagamentoComponent } from "./components/formas-pagamento/formas-pagamento.component";
import { FormasPagamentoFormComponent } from "./components/formas-pagamento-form/formas-pagamento-form.component";
import { ImpostoProdutoComponent } from "./components/imposto-produto/imposto-produto/imposto-produto.component";
import { ImpostoProdutoFormComponent } from "./components/imposto-produto/imposto-produto-form/imposto-produto-form.component";
import { ReactiveFormsModule } from "@angular/forms";
import { ExclusaoImpostoModalComponent } from "./components/imposto-produto/exclusao-imposto-modal/exclusao-imposto-modal.component";
import { AtualizarProdutoModalComponent } from "./components/imposto-produto/atualiza-produto-modal/atualizar-produtomodal.component";
import { PinpadComponent } from "./components/pinpad/pinpad/pinpad.component";
import { PinpadFormComponent } from "./components/pinpad/pinpad-form/pinpad-form.component";
import { TipoRetornoComponent } from "./components/tipo-retorno/tipo-retorno/tipo-retorno.component";
import { TipoRetornoFormComponent } from "./components/tipo-retorno/tipo-retorno-form/tipo-retorno-form.component";
import { TipoRemessaComponent } from "./components/tipo-remessa/tipo-remessa/tipo-remessa.component";
import { TipoRemessaFormComponent } from "./components/tipo-remessa/tipo-remessa-form/tipo-remessa-form.component";
import { ModeloOrcamentoComponent } from "./components/modelo-orcamento/modelo-orcamento/modelo-orcamento.component";
import { ModeloOrcamentoFormComponent } from "./components/modelo-orcamento/modelo-orcamento-form/modelo-orcamento-form.component";
import { QuillModule } from "ngx-quill";

@NgModule({
	declarations: [
		ContaCorrenteComponent,
		ContaCorrenteFormComponent,
		BancoComponent,
		BancoFormComponent,
		MetaFinanceiraComponent,
		MetaFinanceiraFormComponent,
		MetaFinanceiraConsultaComponent,
		MetaFinanceiraConfirmarExclusaoComponent,
		MetaFinanceiraConfirmarExclusaoListagemComponent,
		AdquirenteComponent,
		AdquirenteFormComponent,
		OperadoraCartaoFormComponent,
		OperadoraCartaoListaComponent,
		FormasPagamentoComponent,
		FormasPagamentoFormComponent,
		ImpostoProdutoComponent,
		ImpostoProdutoFormComponent,
		ExclusaoImpostoModalComponent,
		AtualizarProdutoModalComponent,
		PinpadComponent,
		PinpadFormComponent,
		TipoRetornoComponent,
		TipoRetornoFormComponent,
		TipoRemessaComponent,
		TipoRemessaFormComponent,
		ModeloOrcamentoComponent,
		ModeloOrcamentoFormComponent,
	],
	imports: [
		CommonModule,
		ConfigFinanceirasRoutingModule,
		NgbModule,
		LayoutModule,
		UiModule,
		ReactiveFormsModule,
		QuillModule,
	],
	entryComponents: [
		MetaFinanceiraConsultaComponent,
		MetaFinanceiraConfirmarExclusaoComponent,
		MetaFinanceiraConfirmarExclusaoListagemComponent,
		ExclusaoImpostoModalComponent,
		AtualizarProdutoModalComponent,
	],
})
export class ConfigFinanceirasModule {}
