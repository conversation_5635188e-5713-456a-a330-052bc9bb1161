import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { ContaCorrenteComponent } from "./components/conta-corrente/conta-corrente.component";
import { ContaCorrenteFormComponent } from "./components/conta-corrente-form/conta-corrente-form.component";
import { BancoComponent } from "../financeiro/components/banco/banco.component";
import { BancoFormComponent } from "../financeiro/components/banco-form/banco-form.component";
import { MetaFinanceiraComponent } from "./components/metas-financeiro/meta-financeira.component";
import { MetaFinanceiraFormComponent } from "./components/metas-financeiro-form/meta-financeira-form.component";
import { AdquirenteComponent } from "./components/adquirente/adquirente/adquirente.component";
import { AdquirenteFormComponent } from "./components/adquirente/adquirente-form/adquirente-form.component";
import { OperadoraCartaoFormComponent } from "./components/operadora-cartao-form/operadora-cartao-form.component";
import { OperadoraCartaoListaComponent } from "./components/operadora-cartao-lista/operadora-cartao-lista.component";
import { PerfilAcessoRecurso } from "sdk";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "../perfil-acesso/perfil-acesso-recurso.model";
import { FormasPagamentoComponent } from "./components/formas-pagamento/formas-pagamento.component";
import { FormasPagamentoFormComponent } from "./components/formas-pagamento-form/formas-pagamento-form.component";
import { ImpostoProdutoComponent } from "./components/imposto-produto/imposto-produto/imposto-produto.component";
import { ImpostoProdutoFormComponent } from "./components/imposto-produto/imposto-produto-form/imposto-produto-form.component";
import { PinpadComponent } from "./components/pinpad/pinpad/pinpad.component";
import { PinpadFormComponent } from "./components/pinpad/pinpad-form/pinpad-form.component";
import { TipoRetornoComponent } from "./components/tipo-retorno/tipo-retorno/tipo-retorno.component";
import { TipoRetornoFormComponent } from "./components/tipo-retorno/tipo-retorno-form/tipo-retorno-form.component";
import { TipoRemessaComponent } from "./components/tipo-remessa/tipo-remessa/tipo-remessa.component";
import { TipoRemessaFormComponent } from "./components/tipo-remessa/tipo-remessa-form/tipo-remessa-form.component";
import { ModeloOrcamentoComponent } from "./components/modelo-orcamento/modelo-orcamento/modelo-orcamento.component";
import { ModeloOrcamentoFormComponent } from "./components/modelo-orcamento/modelo-orcamento-form/modelo-orcamento-form.component";

const routes: Routes = [
	{
		path: "conta-corrente",
		component: ContaCorrenteComponent,
	},
	{
		path: "nova-conta-corrente",
		component: ContaCorrenteFormComponent,
	},
	{
		path: "conta-corrente/:id",
		component: ContaCorrenteFormComponent,
	},
	{
		path: "banco",
		component: BancoComponent,
	},
	{
		path: "novo-banco",
		component: BancoFormComponent,
	},
	{
		path: "banco/:id",
		component: BancoFormComponent,
	},
	{
		path: "meta-financeira",
		component: MetaFinanceiraComponent,
	},
	{
		path: "nova-meta-financeira",
		component: MetaFinanceiraFormComponent,
	},
	{
		path: "meta-financeira/:id",
		component: MetaFinanceiraFormComponent,
	},
	{
		path: "adquirente",
		component: AdquirenteComponent,
	},
	{
		path: "novo-adquirente",
		component: AdquirenteFormComponent,
	},
	{
		path: "adquirente/:id",
		component: AdquirenteFormComponent,
	},
	{
		path: "imposto-produto",
		component: ImpostoProdutoComponent,
	},
	{
		path: "novo-imposto-produto",
		component: ImpostoProdutoFormComponent,
	},
	{
		path: "imposto-produto/:id",
		component: ImpostoProdutoFormComponent,
	},
	{
		path: "pinpad",
		component: PinpadComponent,
	},
	{
		path: "novo-pinpad",
		component: PinpadFormComponent,
	},
	{
		path: "pinpad/:id",
		component: PinpadFormComponent,
	},
	{
		path: "tipo-retorno",
		component: TipoRetornoComponent,
	},
	{
		path: "tipo-retorno/novo",
		component: TipoRetornoFormComponent,
	},
	{
		path: "tipo-retorno/:id",
		component: TipoRetornoFormComponent,
	},
	{
		path: "tipo-remessa",
		component: TipoRemessaComponent,
	},
	{
		path: "tipo-remessa/novo",
		component: TipoRemessaFormComponent,
	},
	{
		path: "tipo-remessa/:id",
		component: TipoRemessaFormComponent,
	},
	{
		path: "nova-operadora-cartao",
		component: OperadoraCartaoFormComponent,
	},
	{
		path: "operadora-cartao/:id",
		component: OperadoraCartaoFormComponent,
	},
	{
		path: "formas-pagamento",
		component: FormasPagamentoComponent,
	},
	{
		path: "formas-pagamento/nova",
		component: FormasPagamentoFormComponent,
	},
	{
		path: "formas-pagamento/editar/:id",
		component: FormasPagamentoFormComponent,
	},
	{
		path: "operadora-cartao",
		component: OperadoraCartaoListaComponent,
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.OPERADORA_CARTAO,
				[
					PerfilRecursoPermissoTipo.CONSULTAR,
					PerfilRecursoPermissoTipo.EDITAR,
					PerfilRecursoPermissoTipo.INCLUIR,
					PerfilRecursoPermissoTipo.EXCLUIR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
					PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
				]
			),
		},
	},
	{
		path: "modelo-orcamento",
		component: ModeloOrcamentoComponent,
	},
	{
		path: "modelo-orcamento/novo",
		component: ModeloOrcamentoFormComponent,
	},
	{
		path: "modelo-orcamento/:id",
		component: ModeloOrcamentoFormComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class ConfigFinanceirasRoutingModule {}
