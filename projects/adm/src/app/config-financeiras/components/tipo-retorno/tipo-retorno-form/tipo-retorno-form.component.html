<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@tipo-retorno:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricao" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Arquivo de Layout do Retorno -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Arquivo de Layout do Retorno: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							formControlName="arquivoLayoutRetorno" />
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				titulo="Log tipo de retorno"
				[url]="urlLog"
				class="mr-2"></pacto-log>

			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Cancelar
			</button>
			<button
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
