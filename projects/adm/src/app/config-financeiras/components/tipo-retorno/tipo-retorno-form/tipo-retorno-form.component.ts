import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	CadastroAuxApiTipoRetornoService,
	TipoRetorno,
} from "cadastro-aux-api";
import { AdmRestService } from "@adm/adm-rest.service";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-tipo-retorno-form",
	templateUrl: "./tipo-retorno-form.component.html",
	styleUrls: ["./tipo-retorno-form.component.scss"],
})
export class TipoRetornoFormComponent implements OnInit {
	form: FormGroup;
	tipoRetorno: TipoRetorno = new TipoRetorno();
	id: string;
	isEdicao = false;
	urlLog: string;
	recurso: PerfilAcessoRecurso;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private sessionService: SessionService,
		private admRest: AdmRestService,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private tipoRetornoService: CadastroAuxApiTipoRetornoService,
		private cd: ChangeDetectorRef
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.TIPO_RETORNO,
		);
	}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id && this.id !== "novo";

		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			descricao: ["", [Validators.required]],
			arquivoLayoutRetorno: ["", [Validators.required]],
		});

		if (
			this.isEdicao &&
			(this.recurso.consultar || this.recurso.incluirConsultar) &&
			!this.recurso.editar &&
			!this.recurso.incluir
		) {
			this.form.disable();
		}

		if (this.isEdicao) {
			this.carregarTipoRetorno();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`log/TIPORETORNO/${this.id}`
			);
		}
	}

	carregarTipoRetorno() {
		this.tipoRetornoService.find(this.id).subscribe(
			(response) => {
				this.tipoRetorno = response.content;
				this.form.patchValue(this.tipoRetorno);
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar tipo de retorno");
				this.voltarListagem();
			}
		);
	}

	voltarListagem() {
		this.router.navigate(["adm", "config-financeiras", "tipo-retorno"]);
	}

	salvar(): void {
		if (
			!this.recurso ||
			!(
				this.recurso.editar ||
				this.recurso.incluir ||
				!(
					this.isEdicao &&
					(this.recurso.consultar || this.recurso.incluirConsultar)
				)
			)
		) {
			let resource = "INCLUIR";
			if (this.isEdicao) {
				resource = "EDITAR";
			}
			this.notificationService.error(
				`Você não possui permissão para esta operação, "${resource} 4.10 - TIPOS DE RETORNOS"`,
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}

		if (this.form.invalid) {
			this.notificationService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const dadosFormulario = this.form.getRawValue();
		Object.assign(this.tipoRetorno, dadosFormulario);

		this.tipoRetornoService.save(this.tipoRetorno).subscribe(
			(response) => {
				this.notificationService.success(
					this.isEdicao
						? "Tipo de Retorno atualizado com sucesso!"
						: "Tipo de Retorno cadastrado com sucesso!"
				);
				this.voltarListagem();
			},
			(error) => {
				const errorMessage =
					error && error.error && error.error.meta && error.error.meta.message
						? error.error.meta.message
						: "Erro ao salvar tipo de retorno.";
				this.notificationService.error(errorMessage);
			}
		);
	}

	novo() {
		this.form.reset();
		this.tipoRetorno = new TipoRetorno();
		this.isEdicao = false;
		this.cd.detectChanges();
	}

	cancelar() {
		this.voltarListagem();
	}

	get tituloFormulario(): string {
		return this.isEdicao ? "Editar Tipo de Retorno" : "Novo Tipo de Retorno";
	}
}
