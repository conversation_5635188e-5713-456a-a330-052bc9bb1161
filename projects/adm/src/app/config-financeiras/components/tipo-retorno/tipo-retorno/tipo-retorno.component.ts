import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiTipoRetornoService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../../adm-rest.service";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-tipo-retorno",
	templateUrl: "./tipo-retorno.component.html",
	styleUrls: ["./tipo-retorno.component.scss"],
})
export class TipoRetornoComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnArquivoLayout", { static: true })
	columnArquivoLayout: TemplateRef<any>;
	@ViewChild("tableTipoRetorno", { static: false })
	tableTipoRetorno: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private tipoRetornoService: CadastroAuxApiTipoRetornoService,
		private ngbModal: NgbModal
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.TIPO_RETORNO,
		);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoTipoRetorno() {
		if (!this.recurso || (!this.recurso.incluir && !this.recurso.incluirConsultar)) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "INCLUIR 4.10 - TIPOS DE RETORNOS"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		this.router.navigate(["adm", "config-financeiras", "tipo-retorno", "novo"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editTipoRetorno") {
			this.editTipoRetorno(event.row);
		} else if (event.iconName === "deleteTipoRetorno") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		if (!this.recurso || (!this.recurso.excluir)) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "EXCLUIR 4.10 - TIPOS DE RETORNOS"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}

		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este tipo de retorno?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteTipoRetorno(event.row);
				}
			})
			.catch((error) => {});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initFilter();
	}

	editTipoRetorno(tipoRetorno) {
		if (!this.recurso || (!this.recurso.editar && !this.recurso.incluirConsultar && !this.recurso.consultar)) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "CONSULTAR 4.10 - TIPOS DE RETORNOS"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		this.router.navigate([
			"adm",
			"config-financeiras",
			"tipo-retorno",
			tipoRetorno.codigo,
		]);
	}

	deleteTipoRetorno(row: any) {
		this.tipoRetornoService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				if (this.tableTipoRetorno.data.content) {
					this.tableTipoRetorno.data.content =
						this.tableTipoRetorno.data.content.filter(
							(obj) => obj.codigo !== row.codigo
						);
				}
				this.tableTipoRetorno.reloadData();
			},
			(error) => {
				const errorMessage =
					error && error.error && error.error.meta && error.error.meta.message
						? error.error.meta.message
						: "Erro ao excluir tipo de retorno.";
				this.notificationService.error(errorMessage);
			}
		);
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "situacao",
						label: this.traducao.getLabel("filtro-situacao"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "true", label: this.traducao.getLabel("ativo") },
							{ value: "false", label: this.traducao.getLabel("inativo") },
						],
						initialValue: ["true"],
					},
				],
			};
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/tipo-retorno",
					false,
					Api.MSCADAUX
				),
				logUrl: this.admRest.buildFullUrlCadAux("log/TIPORETORNO"),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "arquivoLayoutRetorno",
						titulo: this.columnArquivoLayout,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: "editTipoRetorno",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
					},
					{
						nome: "deleteTipoRetorno",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
