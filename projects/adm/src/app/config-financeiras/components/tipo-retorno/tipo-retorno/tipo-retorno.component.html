<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@tipo-retorno:modulo"
	i18n-pageTitle="@@tipo-retorno:title"
	modulo="Configurações"
	pageTitle="Tipo de Retorno">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableTipoRetorno
			(btnAddClick)="novoTipoRetorno()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editTipoRetorno($event)"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="tipoRetorno"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@tipo-retorno:filtro-situacao" xingling="filtro-situacao">
		Situação
	</span>
	<span i18n="@@tipo-retorno:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@tipo-retorno:desativado" xingling="inativo">Inativo</span>

	<span i18n="@@tipo-retorno:tipoRetornoDeleted" xingling="DELETED">
		Tipo de Retorno excluído com sucesso
	</span>
	<span i18n="@@tipo-retorno:editAction" xingling="ACTION_EDIT">Editar</span>
	<span i18n="@@tipo-retorno:editTooltip" xingling="TOOLTIP_EDIT">
		Editar Tipo de Retorno
	</span>
	<span i18n="@@tipo-retorno:deleteAction" xingling="ACTION_DELETE">
		Excluir
	</span>
	<span i18n="@@tipo-retorno:deleteTooltip" xingling="TOOLTIP_DELETE">
		Excluir Tipo de Retorno
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@tipo-retorno:columnCodigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@tipo-retorno:columnDescricao">Descrição</span>
</ng-template>
<ng-template #columnArquivoLayout>
	<span i18n="@@tipo-retorno:columnArquivoLayout">
		Arquivo de Layout do Retorno
	</span>
</ng-template>
