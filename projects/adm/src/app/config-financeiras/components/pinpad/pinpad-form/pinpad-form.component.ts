import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import {
	FormBuilder,
	FormGroup,
	Validators,
	AbstractControl,
	ValidationErrors,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { LoaderService } from "ui-kit";
import { CadastroAuxApiPinpadService, Pinpad } from "cadastro-aux-api";
import {
	AdmCoreApiEmpresaService,
	AdmCoreApiConvenioCobrancaService,
} from "adm-core-api";
import { AdmRestService } from "@adm/adm-rest.service";

@Component({
	selector: "adm-pinpad-form",
	templateUrl: "./pinpad-form.component.html",
	styleUrls: ["./pinpad-form.component.scss"],
})
export class PinpadFormComponent implements OnInit {
	form: FormGroup;
	pinpad: Pinpad = new Pinpad();
	id: string;
	empresas = [];
	convenios = [];
	tipos = [
		{ codigo: 0, nome: "-" },
		{ codigo: 3, nome: "Stone Connect" },
	];
	isEdicao = false;
	urlLog: string;

	// Custom validator to prevent empty or whitespace-only descriptions
	static noWhitespaceValidator(
		control: AbstractControl
	): ValidationErrors | null {
		if (!control.value) {
			return { required: true };
		}
		const isWhitespace = (control.value || "").trim().length === 0;
		return isWhitespace ? { whitespace: true } : null;
	}

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private admRest: AdmRestService,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private loaderService: LoaderService,
		private pinpadService: CadastroAuxApiPinpadService,
		private empresaService: AdmCoreApiEmpresaService,
		private convenioCobrancaService: AdmCoreApiConvenioCobrancaService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id;

		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			descricao: [
				"",
				[Validators.required, PinpadFormComponent.noWhitespaceValidator],
			],
			pinpad: [null],
			pdvpinpad: [""],
			nrmaxparcelas: [null],
			empresa: [null],
			conveniocobranca: [null],
		});

		this.carregarEmpresas();
		this.setupFormSubscriptions();

		if (this.isEdicao) {
			this.carregarPinpad();
			this.urlLog = this.admRest.buildFullUrlCadAux(`log/PINPAD/${this.id}`);
		}
	}

	setupFormSubscriptions() {
		// Observa mudanças no tipo para mostrar/ocultar campos
		this.form.get("pinpad").valueChanges.subscribe((tipo) => {
			this.cd.detectChanges();
		});

		// Observa mudanças na empresa para carregar convênios
		this.form.get("empresa").valueChanges.subscribe((empresa) => {
			if (empresa) {
				this.carregarConveniosPorEmpresa(empresa);
			} else {
				this.convenios = [];
				this.form.get("convenio").setValue(null);
			}
		});
	}

	carregarEmpresas() {
		this.empresaService.findAll().subscribe(
			(response) => {
				this.empresas = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar empresas");
			}
		);
	}

	carregarConveniosPorEmpresa(empresaId: number) {
		this.convenioCobrancaService.findAllByEmpresa(empresaId).subscribe(
			(response) => {
				this.convenios = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar convênios");
				this.convenios = [];
			}
		);
	}

	carregarPinpad() {
		this.loaderService.initForce();
		this.pinpadService.find(this.id).subscribe(
			(response) => {
				this.pinpad = response.content;
				this.form.patchValue(this.pinpad);
				this.loaderService.stopForce();
				this.cd.detectChanges();
			},
			(error) => {
				this.loaderService.stopForce();
				this.notificationService.error("Erro ao carregar pinpad");
				this.voltarListagem();
			}
		);
	}

	voltarListagem() {
		this.router.navigate(["adm", "config-financeiras", "pinpad"]);
	}

	salvar(): void {
		if (this.form.invalid) {
			// Check for specific validation errors
			const descricaoControl = this.form.get("descricao");
			if (descricaoControl && descricaoControl.errors) {
				if (descricaoControl.errors["required"]) {
					this.notificationService.error("O campo Descrição é obrigatório.");
					return;
				}
				if (descricaoControl.errors["whitespace"]) {
					this.notificationService.error(
						"A Descrição não pode estar vazia ou conter apenas espaços."
					);
					return;
				}
			}
			this.notificationService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const dadosFormulario = this.form.getRawValue();

		// Extrai apenas o código da empresa e convênio se forem objetos
		if (
			dadosFormulario.empresa &&
			typeof dadosFormulario.empresa === "object"
		) {
			dadosFormulario.empresa = dadosFormulario.empresa.codigo;
		}
		if (
			dadosFormulario.convenio &&
			typeof dadosFormulario.convenio === "object"
		) {
			dadosFormulario.convenio = dadosFormulario.convenio.codigo;
		}
		if (dadosFormulario.tipo && typeof dadosFormulario.tipo === "object") {
			dadosFormulario.tipo = dadosFormulario.tipo.codigo;
		}

		Object.assign(this.pinpad, dadosFormulario);

		this.loaderService.initForce();
		this.pinpadService.save(this.pinpad).subscribe(
			(response) => {
				this.loaderService.stopForce();
				this.notificationService.success(
					this.isEdicao
						? "Pinpad atualizado com sucesso!"
						: "Pinpad cadastrado com sucesso!"
				);
				this.voltarListagem();
			},
			(error) => {
				this.loaderService.stopForce();
				const errorMessage =
					error &&
					error.error &&
					error.error.meta &&
					error.error.meta.messageValue
						? error.error.meta.messageValue
						: "Erro ao salvar pinpad.";
				this.notificationService.error(errorMessage);
			}
		);
	}

	novo() {
		this.form.reset();
		this.pinpad = new Pinpad();
		this.isEdicao = false;
		this.convenios = [];
		this.cd.detectChanges();
	}

	excluir() {
		if (!this.id) {
			this.notificationService.warning(
				"Nenhum registro selecionado para exclusão."
			);
			return;
		}

		if (confirm("Tem certeza que deseja excluir este pinpad?")) {
			this.loaderService.initForce();
			this.pinpadService.delete(Number(this.id)).subscribe(
				(response) => {
					this.loaderService.stopForce();
					this.notificationService.success("Pinpad excluído com sucesso!");
					this.voltarListagem();
				},
				(error) => {
					this.loaderService.stopForce();
					const errorMessage =
						error &&
						error.error &&
						error.error.meta &&
						error.error.meta.messageValue
							? error.error.meta.messageValue
							: "Erro ao excluir pinpad.";
					this.notificationService.error(errorMessage);
				}
			);
		}
	}

	cancelar() {
		this.voltarListagem();
	}

	get tituloFormulario(): string {
		return this.isEdicao ? "Editar Pinpad" : "Novo Pinpad";
	}

	get isPinpadType(): boolean {
		const tipo = this.form.get("pinpad").value;
		return tipo && tipo === 3;
	}
}
