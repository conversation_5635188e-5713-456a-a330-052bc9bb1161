<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@pinpad:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição*:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricao" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Tipo -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Tipo:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="tipos"
							[useFullOption]="false"
							[placeholder]="'Selecione o tipo'"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							[formControl]="form.get('pinpad')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Serial Number -->
			<div class="form-row-centered" *ngIf="isPinpadType">
				<div class="label-column">
					<label>Serial Number:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input [formControl]="form.get('pdvpinpad')" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Número máximo de parcelas -->
			<div class="form-row-centered" *ngIf="isPinpadType">
				<div class="label-column">
					<label>Número máximo de parcelas:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="number"
							ds3Input
							[formControl]="form.get('nrmaxparcelas')" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Empresa -->
			<div class="form-row-centered" *ngIf="isPinpadType">
				<div class="label-column">
					<label>Empresa:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="empresas"
							[useFullOption]="false"
							[placeholder]="'Selecione a empresa'"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							[formControl]="form.get('empresa')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Convênio de Cobrança -->
			<div class="form-row-centered" *ngIf="isPinpadType">
				<div class="label-column">
					<label>Convênio Cobrança:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="convenios"
							[useFullOption]="false"
							[placeholder]="'Selecione o convênio'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							[formControl]="form.get('conveniocobranca')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				titulo="Log modalidade"
				[url]="urlLog"
				class="mr-2"></pacto-log>

			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Cancelar
			</button>
			<button
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
