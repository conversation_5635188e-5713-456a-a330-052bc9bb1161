import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiPinpadService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-pinpad",
	templateUrl: "./pinpad.component.html",
	styleUrls: ["./pinpad.component.scss"],
})
export class PinpadComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnEmpresa", { static: true }) columnEmpresa: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnConvenio", { static: true })
	columnConvenio: TemplateRef<any>;
	@ViewChild("tablePinpad", { static: false })
	tablePinpad: RelatorioComponent;
	table: PactoDataGridConfig;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private pinpadService: CadastroAuxApiPinpadService,
		private ngbModal: NgbModal
	) {}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoPinpad() {
		this.router.navigate(["adm", "config-financeiras", "novo-pinpad"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editPinpad") {
			this.editPinpad(event.row);
		} else if (event.iconName === "deletePinpad") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este pinpad?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deletePinpad(event.row);
				}
			})
			.catch((error) => {});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {}

	editPinpad(pinpad) {
		this.router.navigate([
			"adm",
			"config-financeiras",
			"pinpad",
			pinpad.codigo,
		]);
	}

	deletePinpad(row: any) {
		this.pinpadService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				this.tablePinpad.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl("/pinpad", false, Api.MSCADAUX),
				quickSearch: true,
				logUrl: this.admRest.buildFullUrlCadAux(`log/PINPAD`),
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nomeEmpresa",
						titulo: this.columnEmpresa,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nomeConvenio",
						titulo: this.columnConvenio,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: "editPinpad",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
						actionFn: (row) => this.editPinpad(row),
					},
					{
						nome: "deletePinpad",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
						actionFn: (row) => this.deletePinpad(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
