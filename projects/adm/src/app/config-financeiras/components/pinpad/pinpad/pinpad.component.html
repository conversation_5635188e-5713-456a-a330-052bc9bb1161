<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@pinpad:modulo"
	i18n-pageTitle="@@pinpad:title"
	modulo="Configurações"
	pageTitle="Pinpad">
	<div class="table-wrapper">
		<pacto-relatorio
			#tablePinpad
			(btnAddClick)="novoPinpad()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editPinpad($event)"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="pinpad"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@pinpad:filtro-situacao" xingling="filtro-situacao">
		Situação
	</span>
	<span i18n="@@pinpad:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@pinpad:desativado" xingling="inativo">Inativo</span>

	<span i18n="@@pinpad:pinpadDeleted" xingling="DELETED">
		Pinpad excluído com sucesso
	</span>
	<span i18n="@@pinpad:editAction" xingling="ACTION_EDIT">Editar</span>
	<span i18n="@@pinpad:editTooltip" xingling="TOOLTIP_EDIT">Editar Pinpad</span>
	<span i18n="@@pinpad:deleteAction" xingling="ACTION_DELETE">Excluir</span>
	<span i18n="@@pinpad:deleteTooltip" xingling="TOOLTIP_DELETE">
		Excluir Pinpad
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@pinpad:columnCodigo">Código</span>
</ng-template>
<ng-template #columnEmpresa>
	<span i18n="@@pinpad:columnNome">Empresa</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@pinpad:columnDescricao">Descrição</span>
</ng-template>
<ng-template #columnConvenio>
	<span i18n="@@pinpad:columnSituacao">Convênio de cobrança</span>
</ng-template>
