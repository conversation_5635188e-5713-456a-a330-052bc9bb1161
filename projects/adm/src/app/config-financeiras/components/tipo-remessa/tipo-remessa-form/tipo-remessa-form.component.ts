import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	CadastroAuxApiTipoRemessaService,
	TipoRemessa,
	ARQUIVO_LAYOUT_REMESSA_OPTIONS,
	CadastroAuxApiTipoRetornoService,
} from "cadastro-aux-api";
import { AdmRestService } from "@adm/adm-rest.service";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-tipo-remessa-form",
	templateUrl: "./tipo-remessa-form.component.html",
	styleUrls: ["./tipo-remessa-form.component.scss"],
})
export class TipoRemessaFormComponent implements OnInit {
	form: FormGroup;
	tipoRemessa: TipoRemessa = new TipoRemessa();
	id: string;
	tiposRetorno = [];
	arquivosLayoutRemessa = ARQUIVO_LAYOUT_REMESSA_OPTIONS;
	isEdicao = false;
	urlLog: string;
	recurso: PerfilAcessoRecurso;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private sessionService: SessionService,
		private admRest: AdmRestService,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private tipoRemessaService: CadastroAuxApiTipoRemessaService,
		private tipoRetornoService: CadastroAuxApiTipoRetornoService,
		private cd: ChangeDetectorRef,
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.TIPO_REMESSA,
		);
	}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id && this.id !== "novo";

		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			descricao: ["", [Validators.required]],
			tipoRetorno: [null, [Validators.required]],
			arquivoLayoutRemessa: ["", [Validators.required]],
			tipoRemessa: ["", [Validators.required]],
		});

		if (
			this.isEdicao &&
			(this.recurso.consultar || this.recurso.incluirConsultar) &&
			!this.recurso.editar &&
			!this.recurso.incluir
		) {
			this.form.disable();
		}

		this.carregarTiposRetorno();

		if (this.isEdicao) {
			this.carregarTipoRemessa();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`log/TIPOREMESSA/${this.id}`,
			);
		}
	}

	carregarTiposRetorno() {
		this.tipoRetornoService.findAll().subscribe(
			(response) => {
				this.tiposRetorno = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar tipos de retorno");
			},
		);
	}

	carregarTipoRemessa() {
		this.tipoRemessaService.find(this.id).subscribe(
			(response) => {
				this.tipoRemessa = response.content;
				this.form.patchValue(this.tipoRemessa);
				if (this.tipoRemessa.tipoRetorno) {
					this.form
						.get("tipoRetorno")
						.setValue(this.tipoRemessa.tipoRetorno.codigo);
				}
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar tipo de remessa");
				this.voltarListagem();
			},
		);
	}

	voltarListagem() {
		this.router.navigate(["adm", "config-financeiras", "tipo-remessa"]);
	}

	salvar(): void {
		if (
			!this.recurso ||
			!(
				this.recurso.editar ||
				this.recurso.incluir ||
				!(
					this.isEdicao &&
					(this.recurso.consultar || this.recurso.incluirConsultar)
				)
			)
		) {
			let resource = "INCLUIR";
			if (this.isEdicao) {
				resource = "EDITAR";
			}
			this.notificationService.error(
				`Você não possui permissão para esta operação, "${resource} 4.09 - TIPOS DE REMESSA"`,
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}

		if (this.form.invalid) {
			this.notificationService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const dadosFormulario = this.form.getRawValue();
		Object.assign(this.tipoRemessa, dadosFormulario);
		if (this.form.get("tipoRetorno").value) {
			this.tipoRemessa.tipoRetorno = {
				codigo: this.form.get("tipoRetorno").value,
			};
		}

		this.tipoRemessaService.save(this.tipoRemessa).subscribe(
			(response) => {
				this.notificationService.success(
					this.isEdicao
						? "Tipo de Remessa atualizado com sucesso!"
						: "Tipo de Remessa cadastrado com sucesso!",
				);
				this.voltarListagem();
			},
			(error) => {
				const errorMessage =
					error && error.error && error.error.meta && error.error.meta.message
						? error.error.meta.message
						: "Erro ao salvar tipo de remessa.";
				this.notificationService.error(errorMessage);
			},
		);
	}

	novo() {
		this.form.reset();
		this.tipoRemessa = new TipoRemessa();
		this.isEdicao = false;
		this.cd.detectChanges();
	}

	get tituloFormulario(): string {
		return this.isEdicao ? "Editar Tipo de Remessa" : "Novo Tipo de Remessa";
	}
}
