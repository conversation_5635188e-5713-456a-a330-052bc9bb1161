<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@tipo-remessa:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricao" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Tipo de Retorno -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Tipo de Retorno: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="tiposRetorno"
							[useFullOption]="false"
							[placeholder]="'Selecione o tipo de retorno'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="tipoRetorno"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Arquivo de Layout da Remessa -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Arquivo de Layout da Remessa: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="arquivosLayoutRemessa"
							[useFullOption]="false"
							[placeholder]="'Selecione o arquivo de layout'"
							[valueKey]="'value'"
							[nameKey]="'label'"
							formControlName="arquivoLayoutRemessa"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Tipo de Remessa -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Tipo de Remessa: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="tipoRemessa" />
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				titulo="Log tipo de remessa"
				[url]="urlLog"
				class="mr-2"></pacto-log>

			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Cancelar
			</button>
			<button
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
