<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@tipo-remessa:modulo"
	i18n-pageTitle="@@tipo-remessa:title"
	modulo="Configurações"
	pageTitle="Tipo de Remessa">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableTipoRemessa
			(btnAddClick)="novoTipoRemessa()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editTipoRemessa($event)"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="tipoRemessa"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@tipo-remessa:filtro-situacao" xingling="filtro-situacao">
		Situação
	</span>
	<span i18n="@@tipo-remessa:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@tipo-remessa:desativado" xingling="inativo">Inativo</span>

	<span i18n="@@tipo-remessa:tipoRemessaDeleted" xingling="DELETED">
		Tipo de Remessa excluído com sucesso
	</span>
	<span i18n="@@tipo-remessa:editAction" xingling="ACTION_EDIT">Editar</span>
	<span i18n="@@tipo-remessa:editTooltip" xingling="TOOLTIP_EDIT">
		Editar Tipo de Remessa
	</span>
	<span i18n="@@tipo-remessa:deleteAction" xingling="ACTION_DELETE">
		Excluir
	</span>
	<span i18n="@@tipo-remessa:deleteTooltip" xingling="TOOLTIP_DELETE">
		Excluir Tipo de Remessa
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@tipo-remessa:columnCodigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@tipo-remessa:columnDescricao">Descrição</span>
</ng-template>
<ng-template #columnTipoRetorno>
	<span i18n="@@tipo-remessa:columnTipoRetorno">Tipo de Retorno</span>
</ng-template>
<ng-template #columnArquivoLayout>
	<span i18n="@@tipo-remessa:columnArquivoLayout">
		Arquivo de Layout da Remessa
	</span>
</ng-template>
<ng-template #columnTipoRemessa>
	<span i18n="@@tipo-remessa:columnTipoRemessa">Tipo de Remessa</span>
</ng-template>
