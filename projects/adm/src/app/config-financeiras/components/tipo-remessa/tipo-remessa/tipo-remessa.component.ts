import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiTipoRemessaService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../../adm-rest.service";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-tipo-remessa",
	templateUrl: "./tipo-remessa.component.html",
	styleUrls: ["./tipo-remessa.component.scss"],
})
export class TipoRemessaComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnTipoRetorno", { static: true })
	columnTipoRetorno: TemplateRef<any>;
	@ViewChild("columnArquivoLayout", { static: true })
	columnArquivoLayout: TemplateRef<any>;
	@ViewChild("columnTipoRemessa", { static: true })
	columnTipoRemessa: TemplateRef<any>;
	@ViewChild("tableTipoRemessa", { static: false })
	tableTipoRemessa: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private tipoRemessaService: CadastroAuxApiTipoRemessaService,
		private ngbModal: NgbModal,
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.TIPO_REMESSA,
		);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoTipoRemessa() {
		if (!this.recurso || (!this.recurso.incluir && !this.recurso.incluirConsultar)) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "INCLUIR 4.09 - TIPOS DE REMESSA"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		this.router.navigate(["adm", "config-financeiras", "tipo-remessa", "novo"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editTipoRemessa") {
			this.editTipoRemessa(event.row);
		} else if (event.iconName === "deleteTipoRemessa") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		if (!this.recurso || (!this.recurso.excluir)) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "EXCLUIR 4.09 - TIPOS DE REMESSA"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este tipo de remessa?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteTipoRemessa(event.row);
				}
			})
			.catch((error) => {
			});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initFilter();
	}

	editTipoRemessa(tipoRemessa) {
		if (!this.recurso || (!this.recurso.editar && !this.recurso.incluirConsultar && !this.recurso.consultar)) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "CONSULTAR 4.09 - TIPOS DE REMESSA"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		this.router.navigate([
			"adm",
			"config-financeiras",
			"tipo-remessa",
			tipoRemessa.codigo,
		]);
	}

	deleteTipoRemessa(row: any) {
		this.tipoRemessaService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				if (this.tableTipoRemessa.data.content) {
					this.tableTipoRemessa.data.content =
						this.tableTipoRemessa.data.content.filter(
							(obj) => obj.codigo !== row.codigo,
						);
				}
				this.tableTipoRemessa.reloadData();
			},
			(error) => {
				const errorMessage =
					error && error.error && error.error.meta && error.error.meta.message
						? error.error.meta.message
						: "Erro ao excluir tipo de retorno.";
				this.notificationService.error(errorMessage);
			},
		);
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "situacao",
						label: this.traducao.getLabel("filtro-situacao"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "true", label: this.traducao.getLabel("ativo") },
							{ value: "false", label: this.traducao.getLabel("inativo") },
						],
						initialValue: ["true"],
					},
				],
			};
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/tipo-remessa",
					false,
					Api.MSCADAUX,
				),
				logUrl: this.admRest.buildFullUrlCadAux("log/TIPOREMESSA"),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "tipoRetorno",
						titulo: this.columnTipoRetorno,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => v.descricao,
					},
					{
						nome: "arquivoLayoutRemessa",
						titulo: this.columnArquivoLayout,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "tipoRemessa",
						titulo: this.columnTipoRemessa,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: "editTipoRemessa",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
					},
					{
						nome: "deleteTipoRemessa",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
