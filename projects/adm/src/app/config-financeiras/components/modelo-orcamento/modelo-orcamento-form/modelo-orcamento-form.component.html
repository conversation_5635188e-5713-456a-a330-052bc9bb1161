<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@modelo-orcamento:title"
	modulo="Administrativo"
	id="adm-layout-modelo-orcamento"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain id="pacto-cat-card-plain-modelo-orcamento"
												class="compact-card-padding">
		<form class="compact-form" [formGroup]="form"
					id="form-geral-modelo-orcamento">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricao" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Situação -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Situação: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="situacoes"
							[useFullOption]="false"
							[placeholder]="'Selecione a situação'"
							[valueKey]="'id'"
							[nameKey]="'descricao'"
							formControlName="situacao"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Modalidade -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Modalidade: *</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="modalidades"
							[useFullOption]="false"
							[placeholder]="'Selecione a modalidade'"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							formControlName="modalidade"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Pacote -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Pacote:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="pacotes"
							[useFullOption]="false"
							[placeholder]="'Selecione o pacote'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="pacote"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Data Definição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Data Definição: *</label>
				</div>
				<div class="input-column">
					<ds3-input-date
						ds3Input
						[control]="form.get('dataDefinicao')"></ds3-input-date>
				</div>
			</div>

			<!-- Texto -->
			<div class="form-row-centered">
				<quill-editor
					class="compact-editor-modelo-orcamento"
					[formControl]="form.get('texto')"
					[id]="'editor-texto-modelo-contrato'"
					[modules]="modules"
					[styles]="{ width: '100%', height: '400px' }"></quill-editor>
			</div>
		</form>

		<div class="row">
			<div id="informe-os-dados">
				<p i18n="@@modelo-orcamento:informe-os-paramentros-1">
					Tag = [exemplo_exemplo]
				</p>
				<p i18n="@@modelo-orcamento:informe-os-paramentros-2">
					Nome da modalidade = [ORCA]NomeModalidade
				</p>
				<p i18n="@@modelo-orcamento:informe-os-paramentros-3">
					Turma desta modalidade com horarios e vagas = [ORCA]HorariosTurma
				</p>
			</div>
		</div>

		<div class="button-actions-centered">
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				titulo="Log modelo de orçamento"
				[url]="urlLog"
				class="mr-2"></pacto-log>
			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Cancelar
			</button>
			<button type="button" ds3-outlined-button (click)="imprimir()">
				Imprimir
			</button>
			<button
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
