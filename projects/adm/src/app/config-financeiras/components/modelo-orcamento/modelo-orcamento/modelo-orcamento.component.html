<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@modelo-orcamento:modulo"
	i18n-pageTitle="@@modelo-orcamento:title"
	modulo="Configurações"
	pageTitle="Modelo de Orçamento">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableModeloOrcamento
			(btnAddClick)="novoModeloOrcamento()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editModeloOrcamento($event)"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="modeloOrcamento"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@modelo-orcamento:filtro-situacao" xingling="filtro-situacao">
		Situação
	</span>
	<span i18n="@@modelo-orcamento:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@modelo-orcamento:desativado" xingling="inativo">Inativo</span>

	<span i18n="@@modelo-orcamento:modeloOrcamentoDeleted" xingling="DELETED">
		Modelo de Orçamento excluído com sucesso
	</span>
	<span i18n="@@modelo-orcamento:editAction" xingling="ACTION_EDIT">
		Editar
	</span>
	<span i18n="@@modelo-orcamento:editTooltip" xingling="TOOLTIP_EDIT">
		Editar Modelo de Orçamento
	</span>
	<span i18n="@@modelo-orcamento:deleteAction" xingling="ACTION_DELETE">
		Excluir
	</span>
	<span i18n="@@modelo-orcamento:deleteTooltip" xingling="TOOLTIP_DELETE">
		Excluir Modelo de Orçamento
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@modelo-orcamento:columnCodigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@modelo-orcamento:columnDescricao">Descrição</span>
</ng-template>
<ng-template #columnSituacao>
	<span i18n="@@modelo-orcamento:columnSituacao">Situação</span>
</ng-template>
<ng-template #columnModalidade>
	<span i18n="@@modelo-orcamento:columnModalidade">Modalidade</span>
</ng-template>
<ng-template #columnPacote>
	<span i18n="@@modelo-orcamento:columnPacote">Pacote</span>
</ng-template>
<ng-template #columnDataDefinicao>
	<span i18n="@@modelo-orcamento:columnDataDefinicao">Data Definição</span>
</ng-template>
<ng-template #columnResponsavel>
	<span i18n="@@modelo-orcamento:columnResponsavel">Responsável Definição</span>
</ng-template>
