@import "projects/ui/assets/import.scss";

.titulo {
	font-family: Nunito Sans;
	font-style: normal;
	font-weight: bold;
	font-size: 24px;
	line-height: 16px;
	color: $pretoPri;
	margin-bottom: 32px;
	margin-top: 8px;
}

.section-header {
	margin: 20px 0 15px 0;
	padding: 10px 0;
	border-bottom: 1px solid #e0e0e0;

	h3 {
		margin: 0;
		font-size: 16px;
		font-weight: 600;
		color: #333;
	}
}

#informe-os-dados {
	width: 100%;
	height: 88px;
	margin: 0px 16px 32px 16px;
	background: #f7f7f7;
	border: 1px solid #d3d5d7;
	box-sizing: border-box;
	border-radius: 8px;
}

#informe-os-dados h1,
#informe-os-dados p {
	font-family: "Nunito Sans";
	font-style: normal;
	font-size: 14px;
	line-height: 16px;
	align-items: center;
	margin-left: 16px;
	margin-right: 16px;
}

#informe-os-dados h1 {
	margin-top: 16px;
	font-weight: 600;
	color: black;
}

#informe-os-dados p {
	margin-top: 8px;
	color: $preto02;
}

.restricoes-section {
	margin-top: 30px;
	padding: 20px;
	border: 1px solid #d3d5d7;
	border-radius: 8px;
	background: #f9f9f9;

	h2 {
		font-family: "Nunito Sans";
		font-weight: 600;
		font-size: 16px;
		color: $pretoPri;
		margin-bottom: 15px;
	}

	.restricoes-list {

		.restricao-item-plano {
			display: grid !important;
			grid-template-columns: 6fr 0.5fr;
			grid-gap: 10px;
		}

		.premio-item {
			display: grid !important;
			grid-template-columns: 2fr 2fr 2fr 1fr 0.5fr;
			grid-gap: 10px;
		}

		.restricao-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			margin-bottom: 10px;
			background: white;
			border: 1px solid #e0e0e0;
			border-radius: 4px;

			span {
				font-family: "Nunito Sans";
				font-size: 14px;
				color: $preto02;
			}

			.btn-remove {
				background: none;
				border: none;
				color: #e74c3c;
				cursor: pointer;
				padding: 5px;

				&:hover {
					color: #c0392b;
				}
			}
		}
	}
}
