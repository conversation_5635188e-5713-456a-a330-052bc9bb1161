<div class="modal-body">
	<form [formGroup]="form" class="form-novo-lote">
		<!-- Gerar cupom aleatório -->
		<div class="form-group checkbox-group">
			<ds3-form-field>
				<ds3-field-label>Gerar cupom aleatório:</ds3-field-label>
				<ds3-checkbox
					formControlName="gerarCupomAleatorio"
					ds3Input>
				</ds3-checkbox>
			</ds3-form-field>
		</div>

		<!-- Nome do cupom (só aparece se não for aleatório) -->
		<div class="form-group" *ngIf="!form.get('gerarCupomAleatorio')?.value">
			<ds3-form-field>
				<ds3-field-label>Nome do cupom:</ds3-field-label>
				<input
					type="text"
					ds3Input
					formControlName="nomeCupom"
					placeholder="Digite o nome do cupom" />
			</ds3-form-field>
		</div>

		<!-- Quantidade de cupons a gerar -->
		<div class="form-group">
			<ds3-form-field>
				<ds3-field-label>Quantidade de cupons a gerar:</ds3-field-label>
				<input
					type="number"
					ds3Input
					formControlName="quantidadeCupons"
					placeholder="0"
					min="1" />
			</ds3-form-field>
		</div>

		<!-- Observações -->
		<div class="form-group">
			<ds3-form-field>
				<ds3-field-label>Observação:</ds3-field-label>
				<textarea
					ds3Input
					formControlName="observacoes"
					rows="4"></textarea>
			</ds3-form-field>
		</div>
	</form>
</div>

<div class="modal-footer">
	<button type="button" ds3-outlined-button (click)="fecharModal()">
		Cancelar
	</button>
	<button
		type="button"
		ds3-flat-button
		(click)="salvar()">
		Gravar
	</button>
</div>
