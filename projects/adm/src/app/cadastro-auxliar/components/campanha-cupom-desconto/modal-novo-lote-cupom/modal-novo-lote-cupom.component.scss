@import "projects/ui/assets/import.scss";

// Header do modal
.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	background: #1e5a8a;
	color: white;
	border-radius: 8px 8px 0 0;

	.modal-title {
		font-family: "Nunito Sans";
		font-weight: 600;
		font-size: 18px;
		margin: 0;
		color: white;
	}

	.btn-close {
		background: none;
		border: none;
		font-size: 24px;
		cursor: pointer;
		color: white;
		padding: 0;
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;

		&:hover {
			background: rgba(255, 255, 255, 0.1);
			border-radius: 4px;
		}

		span {
			line-height: 1;
		}
	}
}

// Body do modal
.modal-body {
	padding: 20px 20px 0 20px;
	background: white;

	.form-novo-lote {
		.form-group {
			margin-bottom: 20px;

			&.checkbox-group {
				ds3-form-field {
					display: flex;
					align-items: center;
					gap: 10px;

					ds3-field-label {
						margin: 0;
						font-family: "Nunito Sans";
						font-weight: 500;
						color: #666;
						font-size: 14px;
					}
				}
			}

			ds3-field-label {
				font-family: "Nunito Sans";
				font-weight: 500;
				color: #666;
				font-size: 14px;
				margin-bottom: 8px;
				display: block;
			}

			input[ds3Input], textarea[ds3Input] {
				width: 100%;
				font-family: "Nunito Sans";
				font-size: 14px;
			}

			input[type="number"] {
				text-align: left;
			}

			textarea {
				resize: vertical;
				min-height: 80px;
			}

			ds3-helper-message {
				font-size: 12px;
				color: #e74c3c;
				margin-top: 4px;
			}
		}
	}
}

// Footer do modal
.modal-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	padding: 15px 20px;
	border-radius: 0 0 8px 8px;
	border: none;

	button {
		min-width: 80px;
		font-family: "Nunito Sans";
		font-weight: 500;
	}
}

// Responsividade
@media (max-width: 768px) {
	.modal-header {
		padding: 12px 15px;

		.modal-title {
			font-size: 16px;
		}
	}

	.modal-body {
		padding: 15px;
	}

	.modal-footer {
		padding: 12px 15px;
		flex-direction: column;
		gap: 8px;

		button {
			width: 100%;
		}
	}
}