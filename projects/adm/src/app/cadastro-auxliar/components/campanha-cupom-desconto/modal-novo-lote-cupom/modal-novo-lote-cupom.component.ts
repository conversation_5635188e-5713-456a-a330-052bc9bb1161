import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { SessionService } from "sdk";
import { SnotifyService } from "ng-snotify";
import { FormBuilder, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	ZwBootCampanhaCupomDescontoService,
} from "adm-legado-api";

@Component({
	selector: "adm-modal-novo-lote-cupom",
	templateUrl: "./modal-novo-lote-cupom.component.html",
	styleUrls: ["./modal-novo-lote-cupom.component.scss"],
})
export class ModalNovoLoteCupomComponent implements OnInit {

	@Input()
	public campanha: any;
	@Output()
	update = new EventEmitter<any>();
	form: FormGroup;

	constructor(
		private fb: FormBuilder,
		private readonly sessionService: SessionService,
		private notificationService: SnotifyService,
		private zwBootCampanhaCupomDescontoService: ZwBootCampanhaCupomDescontoService,
		private activeModal: NgbActiveModal,
	) {
	}

	ngOnInit() {
		this.form = this.fb.group({
			gerarCupomAleatorio: [true],
			nomeCupom: [""],
			quantidadeCupons: [0],
			observacoes: [""],
		});
	}

	salvar() {
		const body = this.form.getRawValue();
		body.empresa = this.sessionService.empresaId;
		body.campanhaCupomDesconto = this.campanha.id;

		if (!body.gerarCupomAleatorio && body.nomeCupom.length <= 0) {
			this.notificationService.error("Informe um nome para o cupom.");
			return;
		}
		if (body.quantidadeCupons <= 0) {
			this.notificationService.error("Informe uma quantidade de cupons maior que zero.");
			return;
		}
		if (body.observacoes.length <= 0) {
			this.notificationService.error("Informe uma observação.");
			return;
		}
		this.zwBootCampanhaCupomDescontoService.salvarLoteCupom(body).subscribe(
			(response) => {
				this.notificationService.success("Lote de cupom gerado com sucesso!");
				this.update.emit("update");
				this.cancelar();
			},
			(error) => {
				const errorMessage =
					error &&
					error.error &&
					error.error.meta &&
					error.error.meta.messageValue
						? error.error.meta.messageValue
						: "Erro ao gerar lote de cupom.";
				this.notificationService.error(errorMessage);
			},
		);
	}

	fecharModal() {
		this.activeModal.dismiss();
	}

	cancelar() {
		this.fecharModal();
	}
}
