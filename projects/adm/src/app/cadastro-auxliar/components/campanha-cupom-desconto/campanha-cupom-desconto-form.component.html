<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@campanha-cupom-desconto:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="id" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição Campanha -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>
						Descrição campanha:
						<span class="required">*</span>
					</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricaoCampanha" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Vigência De -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>
						Vigência De:
						<span class="required">*</span>
					</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-input-date
							ds3Input
							[control]="form.get('vigenciaInicial')"></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>

			<!-- Vigência Até -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>
						Vigência Até:
						<span class="required">*</span>
					</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-input-date
							ds3Input
							[control]="form.get('vigenciaFinal')"></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>

			<!-- Total de Cotas -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Total de lotes:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							[disabled]="true"
							type="number"
							ds3Input
							formControlName="totalLote" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Quantidade Utilizada -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Quantidade cupom extra:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							[disabled]="true"
							type="number"
							ds3Input
							formControlName="quantidadeCupomExtra" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Total Cupons Utilizados -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Total Cupons Utilizados:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							[disabled]="true"
							type="number"
							ds3Input
							formControlName="totalCupomUtilizado" />
					</ds3-form-field>
				</div>
			</div>

			<div class="section-header">
				<h3>Restringir planos para esta campanha</h3>
			</div>

			<!-- Descrição do Plano -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição do Plano:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="planos"
							[useFullOption]="false"
							[placeholder]="'Selecione o plano'"
							[valueKey]="'codigo'"
							[nameKey]="'descricaoApresentar'"
							formControlName="planoRestricaoOpt"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('planoRestricaoOpt').value !== null">
				<button
					type="button"
					ds3-outlined-button
					(click)="adicionarRestricao()">
					Adicionar restrição
				</button>
			</div>

			<!-- Seção de Restrições -->
			<div class="restricoes-section" *ngIf="restricoes.length > 0">
				<div class="restricoes-list">
					<div
						*ngFor="let restricao of restricoes; let i = index"
						class="restricao-item restricao-item-plano">
						<span>{{ restricao }}</span>
						<button
							type="button"
							class="btn-remove"
							(click)="removerRestricao(i)">
							<i class="pct pct-trash-2"></i>
						</button>
					</div>
				</div>
			</div>

			<div class="section-header">
				<h3>Prêmios ao portador do cupom</h3>
			</div>

			<div class="form-row-centered">
				<div class="label-column">
					<label>Tipo prêmio:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="tipoPremioOptions"
							[placeholder]="'-'"
							[valueKey]="'id'"
							[nameKey]="'nome'"
							formControlName="tipoPremioOpt"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('tipoPremioOpt').value === 'PRODUTO'">
				<div class="label-column">
					<label>Descrição do produto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="produtos"
							[useFullOption]="false"
							[placeholder]="'-'"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							formControlName="produtoOpt"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('tipoPremioOpt').value === 'MENSALIDADE'">
				<div class="label-column">
					<label>Descrição da parcela:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="parcelas"
							[useFullOption]="false"
							[placeholder]="'-'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="parcelaOpt"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('tipoPremioOpt').value !== null">
				<div class="label-column">
					<label>Descrição do plano:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="planos"
							[useFullOption]="false"
							[placeholder]="'-'"
							[valueKey]="'codigo'"
							[nameKey]="'descricaoApresentar'"
							formControlName="planoOpt"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('tipoPremioOpt').value !== null">
				<div class="label-column">
					<label>Desconto:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="tipoDescontoOptions"
							[placeholder]="'-'"
							[valueKey]="'id'"
							[nameKey]="'nome'"
							formControlName="tipoDescontoOpt"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('tipoDescontoOpt').value === 'VA'">
				<div class="label-column">
					<label>Valor:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" ds3Input formControlName="valorDesconto" />
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('tipoDescontoOpt').value === 'PE'">
				<div class="label-column">
					<label>Percentual:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="number"
							ds3Input
							formControlName="percentualDesconto" />
					</ds3-form-field>
				</div>
			</div>

			<div
				class="form-row-centered"
				*ngIf="form.get('tipoPremioOpt').value !== null">
				<button type="button" ds3-outlined-button (click)="adicionarPremio()">
					Adicionar prêmio
				</button>
			</div>

			<div class="restricoes-section" *ngIf="premios.length > 0">
				<div class="restricoes-list">
					<div
						*ngFor="let premio of premios; let i = index"
						class="restricao-item premio-item">
						<span>{{ premio?.tipoPremio }}</span>
						<span>{{ premio?.descricaoPremio }}</span>
						<span>{{ premio?.descricaoPlano }}</span>
						<span *ngIf="premio?.valorDesconto > 0">
							{{ premio?.valorDesconto | currency : "BRL" : "symbol-narrow" }}
						</span>
						<span *ngIf="premio?.percentualDesconto > 0">
							{{ premio?.percentualDesconto }}%
						</span>
						<button type="button" class="btn-remove" (click)="removerPremio(i)">
							<i class="pct pct-trash-2"></i>
						</button>
					</div>
				</div>
			</div>

			<div class="section-header" *ngIf="isEdicao">
				<h3>Cupons</h3>
			</div>

			<div class="form-row-centered">
				<button
					type="button"
					ds3-outlined-button
					(click)="adicionarNovoLoteCupom()"
					*ngIf="isEdicao">
					Novo Lote Cupom
				</button>
			</div>

			<div class="restricoes-section" *ngIf="cupons.length > 0">
				<div class="restricoes-list">
					<div
						*ngFor="let cupom of cupons; let i = index"
						class="restricao-item premio-item">
						<span>{{ cupom?.dataLancamento | date : "dd/MM/yyyy" }}</span>
						<span>{{ cupom?.lote }}</span>
						<span>{{ cupom?.numeroCupom }}</span>
						<span>{{ cupom?.qtdCuponsNomeFixo }}</span>
						<span>{{ cupom?.qtdUtilizadoCuponsNomeFixo }}</span>
					</div>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				titulo="Log campanha cupom desconto"
				[url]="urlLog"
				class="mr-2"></pacto-log>
			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Cancelar
			</button>
			<button
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
