import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import {
	ZwBootCampanhaCupomDescontoService,
} from "adm-legado-api";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

declare var moment;

@Component({
	selector: "adm-campanha-cupom-desconto",
	templateUrl: "./campanha-cupom-desconto.component.html",
	styleUrls: ["./campanha-cupom-desconto.component.scss"],
})
export class CampanhaCupomDescontoComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricaoCampanha", { static: true })
	columnDescricaoCampanha: TemplateRef<any>;
	@ViewChild("columnVigenciaDe", { static: true })
	columnVigenciaDe: TemplateRef<any>;
	@ViewChild("columnVigenciaAte", { static: true })
	columnVigenciaAte: TemplateRef<any>;
	@ViewChild("columnTotalCotas", { static: true })
	columnTotalCotas: TemplateRef<any>;
	@ViewChild("columnQuantidadeUtilizada", { static: true })
	columnQuantidadeUtilizada: TemplateRef<any>;
	@ViewChild("columnTotalCuponUtilizados", { static: true })
	columnTotalCuponUtilizados: TemplateRef<any>;
	@ViewChild("columnDescricaoPlano", { static: true })
	columnDescricaoPlano: TemplateRef<any>;
	@ViewChild("tableCampanhaCupomDesconto", { static: false })
	tableCampanhaCupomDesconto: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private zwBootCampanhaCupomDescontoService: ZwBootCampanhaCupomDescontoService,
		private ngbModal: NgbModal,
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.CAMPANHA_CUPOM_DESCONTO,
		);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoCampanhaCupomDesconto() {
		if (!this.recurso || (!this.recurso.incluir && !this.recurso.incluirConsultar)) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, \"INCLUIR 5.62 - CAMPANHA CUPOM DESCONTO\"",
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		this.router.navigate(["adm", "cad-aux", "campanha-cupom-desconto", "novo"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editCampanhaCupomDesconto") {
			this.editCampanhaCupomDesconto(event.row);
		} else if (event.iconName === "deleteCampanhaCupomDesconto") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		if (!this.recurso || (!this.recurso.excluir)) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, \"EXCLUIR 5.62 - CAMPANHA CUPOM DESCONTO\"",
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}

		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir esta campanha cupom desconto?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteCampanhaCupomDesconto(event.row);
				}
			})
			.catch((error) => {
			});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initFilter();
	}

	editCampanhaCupomDesconto(campanhaCupomDesconto) {
		if (!this.recurso || (!this.recurso.editar && !this.recurso.incluirConsultar && !this.recurso.consultar)) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, \"CONSULTAR 5.62 - CAMPANHA CUPOM DESCONTO\"",
				{
					timeout: 5000,
					bodyMaxLength: 300,
				},
			);
			return;
		}
		this.router.navigate([
			"adm",
			"cad-aux",
			"campanha-cupom-desconto",
			campanhaCupomDesconto.id,
		]);
	}

	deleteCampanhaCupomDesconto(row: any) {
		this.zwBootCampanhaCupomDescontoService.delete(row.id).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				if (this.tableCampanhaCupomDesconto.data.content) {
					this.tableCampanhaCupomDesconto.data.content =
						this.tableCampanhaCupomDesconto.data.content.filter(
							(obj) => obj.id !== row.id,
						);
				}
				this.tableCampanhaCupomDesconto.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			},
		);
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "situacao",
						label: this.traducao.getLabel("filtro-situacao"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "true", label: this.traducao.getLabel("ativo") },
							{ value: "false", label: this.traducao.getLabel("inativo") },
						],
						initialValue: ["true"],
					},
				],
			};
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrlZwBack("campanha-cupom-desconto"),
				logUrl: this.admRest.buildFullUrlCadAux(`log/CAMPANHACUPOMDESCONTO`),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: false,
				columns: [
					{
						nome: "id",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricaoCampanha",
						titulo: this.columnDescricaoCampanha,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "vigenciaInicial",
						titulo: this.columnVigenciaDe,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
						date: true,
					},
					{
						nome: "vigenciaFinal",
						titulo: this.columnVigenciaAte,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
						date: true,
					},
				],
				actions: [
					{
						nome: "editCampanhaCupomDesconto",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
						actionFn: (row) => this.editCampanhaCupomDesconto(row),
					},
					{
						nome: "deleteCampanhaCupomDesconto",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
						actionFn: (row) => this.deleteCampanhaCupomDesconto(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
