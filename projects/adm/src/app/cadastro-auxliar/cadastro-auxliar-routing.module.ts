import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PaisComponent } from "./components/pais/pais.component";
import { PaisFormComponent } from "./components/pais-form/pais-form.component";
import { InstrucaoComponent } from "./components/instrucao/instrucao.component";
import { InstrucaoFormComponent } from "./components/instrucao-form/instrucao-form.component";
import { DepartamentosComponent } from "./components/departamentos/departamentos.component";
import { DepartamentosFormComponent } from "./components/departamentos-form/departamentos-form.component";
import { CategoriaComponent } from "./components/categoria/categoria.component";
import { CategoriaFormComponent } from "./components/categoria-form/categoria-form.component";
import { ParentescoComponent } from "./components/parentesco/parentesco.component";
import { ParentescoFormComponent } from "./components/parentesco-form/parentesco-form.component";
import { ProfissaoFormComponent } from "./components/profissao-form/profissao-form.component";
import { ProfissaoComponent } from "./components/profissao/profissao.component";
import { GrupoComponent } from "./components/grupo/grupo.component";
import { GrupoFormComponent } from "./components/grupo-form/grupo-form.component";
import { PerguntaComponent } from "./components/pergunta/pergunta.component";
import { PerguntaFormComponent } from "./components/pergunta-form/pergunta-form.component";
import { ClassificacaoComponent } from "./components/classificacao/classificacao.component";
import { ClassificacaoFormComponent } from "./components/classificacao-form/classificacao-form.component";
import { PesquisaSatisfacaoComponent } from "./components/pesquisa-satisfacao/pesquisa-satisfacao.component";
import { PesquisaSatisfacaoFormComponent } from "./components/pesquisa-satisfacao-form/pesquisa-satisfacao-form.component";
import { PerfilAcessoRecurso } from "sdk";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "../perfil-acesso/perfil-acesso-recurso.model";
import { PerfilAcessoGuard } from "../guards/perfil-acesso.guard";
import { PesquisaSatisfacaoPreViewComponent } from "./components/pesquisa-satisfacao-pre-view/pesquisa-satisfacao-pre-view.component";
import { CidadeComponent } from "./components/cidade/cidade.component";
import { CidadeFormComponent } from "./components/cidade-form/cidade-form.component";
import { QuestionarioComponent } from "./components/questionario/questionario.component";
import { QuestionarioFormComponent } from "./components/questionario-form/questionario-form.component";
import { BancoComponent } from "../financeiro/components/banco/banco.component";
import { BancoFormComponent } from "../financeiro/components/banco-form/banco-form.component";
import { IndiceFinanceiroComponent } from "./components/indice-financeiro/indice-financeiro/indice-financeiro.component";
import { IndiceFinanceiroFormComponent } from "./components/indice-financeiro/indice-financeiro-form/indice-financeiro-form.component";
import { CampanhaCupomDescontoComponent } from "./components/campanha-cupom-desconto/campanha-cupom-desconto.component";
import { CampanhaCupomDescontoFormComponent } from "./components/campanha-cupom-desconto/campanha-cupom-desconto-form.component";
import { AddConvidadoComponent } from "./components/add-convidado/add-convidado.component";

const routes: Routes = [
	{
		path: "pais",
		component: PaisComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PAIS, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "novo-pais",
		component: PaisFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PAIS, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "pais/:id",
		component: PaisFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PAIS, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "instrucao",
		component: InstrucaoComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GRAU_INSTRUCAO, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "novo-instrucao",
		component: InstrucaoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GRAU_INSTRUCAO, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "novo-instrucao/:id",
		component: InstrucaoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GRAU_INSTRUCAO, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "departamentos",
		component: DepartamentosComponent,
	},
	{
		path: "novo-departamento",
		component: DepartamentosFormComponent,
	},
	{
		path: "departamentos/:id",
		component: DepartamentosFormComponent,
	},
	{
		path: "cidade",
		component: CidadeComponent,
	},
	{
		path: "nova-cidade",
		component: CidadeFormComponent,
	},
	{
		path: "cidade/:id",
		component: CidadeFormComponent,
	},
	{
		path: "categoria",
		component: CategoriaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.CATEGORIA, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "novo-categoria",
		component: CategoriaFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.CATEGORIA, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "categoria/:id",
		component: CategoriaFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.CATEGORIA, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "grupo",
		component: GrupoComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GRUPO, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "novo-grupo",
		component: GrupoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GRUPO, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "grupo/:id",
		component: GrupoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GRUPO, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "parentesco",
		component: ParentescoComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PARENTESCO, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "novo-parentesco",
		component: ParentescoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PARENTESCO, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "parentesco/:id",
		component: ParentescoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PARENTESCO, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "profissao",
		component: ProfissaoComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PROFISSAO, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "novo-profissao",
		component: ProfissaoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PROFISSAO, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "profissao/:id",
		component: ProfissaoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PROFISSAO, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "pergunta",
		component: PerguntaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PERGUNTA, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "nova-pergunta",
		component: PerguntaFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PERGUNTA, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "pergunta/:id",
		component: PerguntaFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PERGUNTA, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "classificacao",
		component: ClassificacaoComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.CLASSIFICACAO, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "novo-classificacao",
		component: ClassificacaoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.CLASSIFICACAO, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "classificacao/:id",
		component: ClassificacaoFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.CLASSIFICACAO, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "indice-financeiro",
		component: IndiceFinanceiroComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.CLASSIFICACAO, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
			]),
		},
	},
	{
		path: "novo-indice-financeiro",
		component: IndiceFinanceiroFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.INDICE_FINANCEIRO_REAJUSTE_PRECO,
				[
					PerfilRecursoPermissoTipo.INCLUIR,
					PerfilRecursoPermissoTipo.CONSULTAR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				]
			),
		},
	},
	{
		path: "indice-financeiro/:id",
		component: IndiceFinanceiroFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.INDICE_FINANCEIRO_REAJUSTE_PRECO,
				[
					PerfilRecursoPermissoTipo.EDITAR,
					PerfilRecursoPermissoTipo.CONSULTAR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				]
			),
		},
	},
	{
		path: "questionario",
		component: QuestionarioComponent,
	},
	{
		path: "novo-questionario",
		component: QuestionarioFormComponent,
	},
	{
		path: "questionario/:id",
		component: QuestionarioFormComponent,
	},
	{
		path: "pesquisa-satisfacao",
		component: PesquisaSatisfacaoComponent,
	},
	{
		path: "nova-pesquisa-satisfacao",
		component: PesquisaSatisfacaoFormComponent,
	},
	{
		path: "pesquisa-satisfacao/:id",
		component: PesquisaSatisfacaoFormComponent,
	},
	{
		path: "pesquisa-satisfacao-pre-view",
		component: PesquisaSatisfacaoPreViewComponent,
	},
	{
		path: "convidado",
		component: AddConvidadoComponent,
	},
	{
		path: "campanha-cupom-desconto",
		component: CampanhaCupomDescontoComponent,
	},
	{
		path: "campanha-cupom-desconto/novo",
		component: CampanhaCupomDescontoFormComponent,
	},
	{
		path: "campanha-cupom-desconto/:id",
		component: CampanhaCupomDescontoFormComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class CadastroAuxliarRoutingModule {}
