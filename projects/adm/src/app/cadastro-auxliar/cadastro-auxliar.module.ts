import { NgModule } from "@angular/core";
import { CommonModule, DeprecatedI18NPipesModule } from "@angular/common";
import { CadastroAuxliarRoutingModule } from "./cadastro-auxliar-routing.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { HttpClientModule } from "@angular/common/http";
import { UiModule } from "ui-kit";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { SnotifyModule } from "ng-snotify";
import { SdkModule } from "sdk";
import { PaisComponent } from "./components/pais/pais.component";
import { LayoutModule } from "../layout/layout.module";
import { PaisFormComponent } from "./components/pais-form/pais-form.component";
import { InstrucaoComponent } from "./components/instrucao/instrucao.component";
import { InstrucaoFormComponent } from "./components/instrucao-form/instrucao-form.component";
import { DepartamentosComponent } from "./components/departamentos/departamentos.component";
import { DepartamentosFormComponent } from "./components/departamentos-form/departamentos-form.component";
import { CategoriaComponent } from "./components/categoria/categoria.component";
import { CategoriaFormComponent } from "./components/categoria-form/categoria-form.component";
import { ProfissaoComponent } from "./components/profissao/profissao.component";
import { ProfissaoFormComponent } from "./components/profissao-form/profissao-form.component";
import { CidadeComponent } from "./components/cidade/cidade.component";
import { CidadeFormComponent } from "./components/cidade-form/cidade-form.component";
import { AddPaisComponent } from "./components/add-pais/add-pais.component";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { QuestionarioComponent } from "./components/questionario/questionario.component";
import { QuestionarioFormComponent } from "./components/questionario-form/questionario-form.component";
import { ParentescoComponent } from "./components/parentesco/parentesco.component";
import { ParentescoFormComponent } from "./components/parentesco-form/parentesco-form.component";
import { GrupoComponent } from "./components/grupo/grupo.component";
import { GrupoFormComponent } from "./components/grupo-form/grupo-form.component";
import { PerguntaComponent } from "./components/pergunta/pergunta.component";
import { PerguntaFormComponent } from "./components/pergunta-form/pergunta-form.component";
import { ClassificacaoComponent } from "./components/classificacao/classificacao.component";
import { ClassificacaoFormComponent } from "./components/classificacao-form/classificacao-form.component";
import { PesquisaSatisfacaoComponent } from "./components/pesquisa-satisfacao/pesquisa-satisfacao.component";
import { PesquisaSatisfacaoFormComponent } from "./components/pesquisa-satisfacao-form/pesquisa-satisfacao-form.component";
import { PesquisaSatisfacaoPreViewComponent } from "./components/pesquisa-satisfacao-pre-view/pesquisa-satisfacao-pre-view.component";
import { IndiceFinanceiroComponent } from "./components/indice-financeiro/indice-financeiro/indice-financeiro.component";
import { IndiceFinanceiroFormComponent } from "./components/indice-financeiro/indice-financeiro-form/indice-financeiro-form.component";
import { AddConvidadoComponent } from "./components/add-convidado/add-convidado.component";
import { CampanhaCupomDescontoComponent } from "./components/campanha-cupom-desconto/campanha-cupom-desconto.component";
import { CampanhaCupomDescontoFormComponent } from "./components/campanha-cupom-desconto/campanha-cupom-desconto-form.component";

@NgModule({
	declarations: [
		PaisComponent,
		PaisFormComponent,
		InstrucaoComponent,
		InstrucaoFormComponent,
		ProfissaoComponent,
		ProfissaoFormComponent,
		ParentescoComponent,
		ParentescoFormComponent,
		DepartamentosComponent,
		DepartamentosFormComponent,
		CategoriaComponent,
		CategoriaFormComponent,
		GrupoComponent,
		GrupoFormComponent,
		PerguntaComponent,
		PerguntaFormComponent,
		ClassificacaoComponent,
		ClassificacaoFormComponent,
		CidadeComponent,
		CidadeFormComponent,
		CidadeFormComponent,
		AddPaisComponent,
		QuestionarioComponent,
		QuestionarioFormComponent,
		PesquisaSatisfacaoComponent,
		PesquisaSatisfacaoFormComponent,
		PesquisaSatisfacaoPreViewComponent,
		CidadeComponent,
		CidadeFormComponent,
		CidadeFormComponent,
		AddPaisComponent,
		IndiceFinanceiroComponent,
		IndiceFinanceiroFormComponent,
		AddConvidadoComponent,
		CampanhaCupomDescontoComponent,
		CampanhaCupomDescontoFormComponent,
	],
	imports: [
		CommonModule,
		CadastroAuxliarRoutingModule,
		NgbModule,
		HttpClientModule,
		UiModule,
		SdkModule,
		ReactiveFormsModule,
		SnotifyModule,
		LayoutModule,
		MatFormFieldModule,
		MatInputModule,
		FormsModule,
		DeprecatedI18NPipesModule,
	],
	entryComponents: [
		AddPaisComponent
	],
})
export class CadastroAuxliarModule {}
