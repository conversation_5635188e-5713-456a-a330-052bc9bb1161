import { HTTP_INTERCEPTORS, HttpClientModule } from "@angular/common/http";
import { LOCALE_ID, NgModule } from "@angular/core";
import { BrowserModule } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { NgbActiveModal, NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { SdkModule, SessionService } from "sdk";
import { UiModule } from "ui-kit";

import { CurrencyPipe, DecimalPipe, registerLocaleData } from "@angular/common";
import localePt from "@angular/common/locales/pt";
import { ReactiveFormsModule } from "@angular/forms";
import { NavigationEnd, Router } from "@angular/router";
import {
	SnotifyModule,
	SnotifyPosition,
	SnotifyService,
	ToastDefaults,
} from "ng-snotify";
import { NgxMaskModule } from "ngx-mask";
import { NotificacaoApiConfigProvider } from "./api-config-providers/notificacao-api/notificacao-api-config-provider";
import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { ConvenioDescontoFormComponent } from "./convenio-desconto/components/convenio-desconto-form/convenio-desconto-form.component";
import { ConvenioDescontoComponent } from "./convenio-desconto/components/convenio-desconto/convenio-desconto.component";
import { TableConvenioDescontoConfigComponent } from "./convenio-desconto/components/table-convenio-desconto-config/table-convenio-desconto-config.component";
import { LoggedinGuard } from "./guards/logged-in.guard";
import { HomeComponent } from "./home/<USER>";
import { LoaderInterceptor } from "./interceptors/loader.interceptor";
import { ShareInterceptor } from "./interceptors/share.interceptor";
import { LayoutModule } from "./layout/layout.module";
import { MenuAdmV2Component } from "./menu-adm-v2/menu-adm-v2.component";
import { MyAddAccountComponent } from "./my-add-account/my-add-account.component";
import { PacoteFormComponent } from "./pacotes/components/form-pacote/form-pacote.component";
import { ListaPacoteComponent } from "./pacotes/components/lista-pacote/lista-pacote.component";
import { AddHorarioComponent } from "./planos/components/add-horario/add-horario.component";
import { CadastrarModalidadeHorarioComponent } from "./planos/components/cadastrar-modalidade-horario/cadastrar-modalidade-horario.component";
import { CadastrarModalidadePlanoComponent } from "./planos/components/cadastrar-modalidade-plano/cadastrar-modalidade-plano.component";
import { CadastrarModalidadeRepeticaoComponent } from "./planos/components/cadastrar-modalidade-repeticao/cadastrar-modalidade-repeticao.component";
import { CadastrarPlanoComponent } from "./planos/components/cadastrar-plano/cadastrar-plano.component";
import { TableProdutosComponent } from "./planos/components/cadastrar-plano/lista-produtos/table-produtos.component";
import { PlanoCondicaoPagamentoComponent } from "./planos/components/cadastrar-plano/plano-condicao-pagamento/plano-condicao-pagamento.component";
import { PlanoDadosBasicosComponent } from "./planos/components/cadastrar-plano/plano-dados-basicos/plano-dados-basicos.component";
import { DadosContratuaisConfigAvancadasComponent } from "./planos/components/cadastrar-plano/plano-dados-contratuais/dados-contratuais-config-avancadas/dados-contratuais-config-avancadas.component";
import { PlanoDadosContratuaisComponent } from "./planos/components/cadastrar-plano/plano-dados-contratuais/plano-dados-contratuais.component";
import { TableDuracaoValorComponent } from "./planos/components/cadastrar-plano/table-duracao-valor/table-duracao-valor.component";
import { CondicaoPagamentoFormComponent } from "./planos/components/condicao-pagamento-form/condicao-pagamento-form.component";
import { CondicaoPagamentoPlanoTableComponent } from "./planos/components/condicao-pagamento-plano-table/condicao-pagamento-plano-table.component";
import { CondicaoPagamentoComponent } from "./planos/components/condicao-pagamento/condicao-pagamento.component";
import { CadastrarPlanoCreditoComponent } from "./planos/components/credito/cadastrar-plano-credito/cadastrar-plano-credito.component";
import { PcCadastroComponent } from "./planos/components/credito/pc-cadastro/pc-cadastro.component";
import { PcCondicaoPagamentoComponent } from "./planos/components/credito/pc-condicao-pagamento/pc-condicao-pagamento.component";
import { PcConfigDadosBasicosComponent } from "./planos/components/credito/pc-config-dados-basicos/pc-config-dados-basicos.component";
import { PcConfigDadosContratuaisComponent } from "./planos/components/credito/pc-config-dados-contratuais/pc-config-dados-contratuais.component";
import { PcDadosBasicosComponent } from "./planos/components/credito/pc-dados-basicos/pc-dados-basicos.component";
import { PcDadosContratualComponent } from "./planos/components/credito/pc-dados-contratual/pc-dados-contratual.component";
import { PcDuracaoValorComponent } from "./planos/components/credito/pc-duracao-valor/pc-duracao-valor.component";
import { PcEditFormComponent } from "./planos/components/credito/pc-edit-form/pc-edit-form.component";
import { PcModalidadesComponent } from "./planos/components/credito/pc-modalidades/pc-modalidades.component";
import { PcProdutosComponent } from "./planos/components/credito/pc-produtos/pc-produtos.component";
import { PcTableHorarioComponent } from "./planos/components/credito/pc-table-horario/pc-table-horario.component";
import { PlanoCreditoDadosContratuaisComponent } from "./planos/components/credito/plano-credito-dados-contratuais/plano-credito-dados-contratuais.component";
import { DescontoFormComponent } from "./planos/components/desconto-form/desconto-form.component";
import { DescontoComponent } from "./planos/components/desconto/desconto.component";
import { DialogConfirmInativarProdutoComponent } from "./planos/components/dialog-confirm-inativar-produto/dialog-confirm-inativar-produto.component";
import { EditFormPlanoComponent } from "./planos/components/edit-form-plano/edit-form-plano.component";
import { HorarioFormComponent } from "./planos/components/horario-form/horario-form.component";
import { HorarioComponent } from "./planos/components/horario/horario.component";
import { PlanosComponent } from "./planos/components/lista-planos/planos.component";
import { PlanoPersonalDadosContratuaisComponent } from "./planos/components/personal/plano-personal-dados-contratuais/plano-personal-dados-contratuais.component";
import { PpCadastroComponent } from "./planos/components/personal/pp-cadastro/pp-cadastro.component";
import { PpDadosBasicosComponent } from "./planos/components/personal/pp-dados-basicos/pp-dados-basicos.component";
import { PpDadosContratuaisComponent } from "./planos/components/personal/pp-dados-contratuais/pp-dados-contratuais.component";
import { PpEditFormComponent } from "./planos/components/personal/pp-edit-form/pp-edit-form.component";
import { ConfigCreditoComponent } from "./planos/components/plano-advanced-config/config-credito/config-credito.component";
import { ConfigNormalComponent } from "./planos/components/plano-advanced-config/config-normal/config-normal.component";
import { ConfigRecorrenciaDadosContratuaisComponent } from "./planos/components/plano-advanced-config/config-recorrencia-dados-contratuais/config-recorrencia-dados-contratuais.component";
import { ConfigRecorrenciaComponent } from "./planos/components/plano-advanced-config/config-recorrencia/config-recorrencia.component";
import { PlanoAdvancedConfigComponent } from "./planos/components/plano-advanced-config/plano-advanced-config.component";
import { PlanoEmpresaComponent } from "./planos/components/plano-empresa/plano-empresa.component";
import { PlanoRecorrenciaDadosContratuaisComponent } from "./planos/components/recorrencia/plano-recorrencia-dados-contraturais/plano-recorrencia-dados-contratuais.component";
import { PrAdvancedConfigDadosContratuaisComponent } from "./planos/components/recorrencia/pr-advanced-config-dados-contratuais/pr-advanced-config-dados-contratuais.component";
import { PrAdvancedConfigComponent } from "./planos/components/recorrencia/pr-advanced-config/pr-advanced-config.component";
import { PrCadastroComponent } from "./planos/components/recorrencia/pr-cadastro/pr-cadastro.component";
import { PrDadosBasicosComponent } from "./planos/components/recorrencia/pr-dados-basicos/pr-dados-basicos.component";
import { PrDadosContratuaisComponent } from "./planos/components/recorrencia/pr-dados-contratuais/pr-dados-contratuais.component";
import { PrEditComponent } from "./planos/components/recorrencia/pr-edit/pr-edit.component";
import { PrTableHorariosComponent } from "./planos/components/recorrencia/pr-table-horarios/pr-table-horarios.component";
import { PrTableModalidadesComponent } from "./planos/components/recorrencia/pr-table-modalidades/pr-table-modalidades.component";
import { PrTableProdutosComponent } from "./planos/components/recorrencia/pr-table-produtos/pr-table-produtos.component";
import { ReplicarPlanoComponent } from "./planos/components/replicar-plano/replicar-plano.component";
import { TipoPlanoFormComponent } from "./planos/components/tipo-plano-form/tipo-plano-form.component";
import { TipoPlanoComponent } from "./planos/components/tipo-plano/tipo-plano.component";
import { PlataformaLinkRedirectComponent } from "./plataforma-link-adm/plataforma-link-redirect.component";
import { FormProdutoComponent } from "./produtos/components/form-produto/form-produto.component";
import { ListaProdutoComponent } from "./produtos/components/lista-produto/lista-produto.component";
import { ProdutosComponent } from "./produtos/components/lista-produto/produtos/produtos.component";
import { LocalizationService } from "./services/localization.service";

import {
	CadastroAuxApiConfigProviderBase,
	CadastroAuxApiModule,
} from "cadastro-aux-api";
import {
	MsPactopayApiConfigProviderBase,
	MsPactopayApiModule,
} from "ms-pactopay-api";
import { CadastroAuxApiConfigProviderService } from "./api-config-providers/cadastro-aux-api-config-provider.service";

import { PlanoApiConfigProviderBase, PlanoApiModule } from "plano-api";
import { PlanoApiConfigProviderService } from "./api-config-providers/plano-api-config-provider.service";

import {
	AcessoSistemaApiConfigProviderBase,
	AcessoSistemaApiModule,
} from "acesso-sistema-api";
import { ClubeVantagensApiConfigProviderBase } from "clube-vantagens-api";
import { AcessoSistemaApiProviderConfigService } from "./api-config-providers/acesso-sistema-api-provider-config.service";
import { ClubeVantagesApiProviderConfigService } from "./clube-vantagens/providers/clube-vantages-api-provider-config.service";

import { ProdutoApiConfigProviderBase, ProdutoApiModule } from "produto-api";
import { ProdutoApiConfigProviderService } from "./api-config-providers/produto-api-config-provider.service";

import {
	RelatorioApiConfigProviderBase,
	RelatorioApiModule,
} from "relatorio-api";
import { RelatorioApiConfigProviderService } from "./api-config-providers/relatorio-api-config-provider.service";

import { AdmCoreApiConfigProviderBase, AdmCoreApiModule } from "adm-core-api";
import {
	AdmLegadoApiConfigProviderBase,
	AdmLegadoApiModule,
} from "adm-legado-api";
import { AdmMsApiConfigProviderBase, AdmMsApiModule } from "adm-ms-api";
import { CrmApiConfigProviderBase, CrmApiModule } from "crm-api";
import {
	FinanceiroMsApiConfigProviderBase,
	FinanceiroMsApiModule,
} from "financeiro-ms-api";
import {
	LoginAppApiConfigProviderBase,
	LoginAppApiModule,
} from "login-app-api";
import {
	MarketingApiConfigProviderBase,
	MarketingApiModule,
} from "marketing-api";
import { CookieService } from "ngx-cookie-service";
import { PactoApiConfigProviderBase, PactoApiModule } from "pacto-api";
import {
	CURRENT_MODULE,
	PactoLayoutModule,
	PactoLayoutSDKWrapper,
	PactoLayoutSearchService,
	PlataformModuleConfig,
} from "pacto-layout";
import {
	ZwPactopayApiConfigProviderBase,
	ZwPactopayApiModule,
} from "zw-pactopay-api";
import {
	ZwServletApiConfigProviderBase,
	ZwServletApiModule,
} from "zw-servlet-api";
import { AdmCoreApiConfigProviderService } from "./api-config-providers/adm-core-api-config-provider.service";
import { AdmLegadoApiConfigProviderService } from "./api-config-providers/adm-legado-api-config-provider.service";
import { AdmMsApiConfigProviderService } from "./api-config-providers/adm-ms-api-config-provider.service";
import { CrmApiConfigProviderService } from "./api-config-providers/crm-api-config-provider.service";
import { FinanceiroMsApiConfigProviderService } from "./api-config-providers/financeiro-ms-api-config-provider.service";
import { LoginAppApiConfigProviderService } from "./api-config-providers/login-app-api-config-provider.service";
import { MarketingApiProviderConfigService } from "./api-config-providers/marketing-api-provider-config.service";
import { PactoApiConfigProvider } from "./api-config-providers/pacto-api-config-provider.service";
import { ZwServletApiConfigProvider } from "./api-config-providers/zw-servlet-api-config-provider.service";
import { FormConfiguracoesBuilderComponent } from "./configuracao/components/inputs/form-configuracoes-builder/form-configuracoes-builder.component";
import { ConfiguracaoModule } from "./configuracao/configuracao.module";
import { DiariasComponent } from "./diarias/components/diarias/diarias.component";
import { AdmUiModule } from "./modules/adm-ui/adm-ui.module";
import { AdmCatTooltipComponent } from "./pacotes/components/tooltip/adm-cat-tooltip.component";
import { CatTooltipTriggerDirective } from "./pacotes/components/tooltip/cat-tooltip-trigger.directive";
import { AdmSdkWrapperService } from "./services/adm-sdk-wrapper.service";
import { SafeUrlPipe } from "./util/safeurl.pipe";
import { MsPactopayApiConfigProvider } from "./api-config-providers/ms-pactopay-api-config-provider.service";
import { ZwPactopayApiConfigProvider } from "./api-config-providers/zw-pactopay-api-config-provider.service";
import {
	PessoaMsApiConfigProviderBase,
	PessoaMsApiModule,
} from "pessoa-ms-api";
import { BaseCoreModule } from "@base-core/base-core.module";
import { PessoaMsApiConfigProviderService } from "./api-config-providers/pessoa-ms-api-config-provider.service";
import { QuillModule } from "ngx-quill";
import { TreinoApiConfigProviderBase } from "treino-api";
import { TreinoApiConfigProvider } from "./api-config-providers/treino-api-config-provider.service";
import { PlanoPacoteComponent } from "./planos/components/plano-pacote/plano-pacote.component";
import { PlanoModalidadeComponent } from "./planos/components/plano-modalidade/plano-modalidade.component";
import { ModalVezesSemanaComponent } from "./planos/components/plano-modalidade/modal-vezes-semana/modal-vezes-semana.component";
import { CategoriaPlanoComponent } from "./planos/components/categoria-plano/categoria-plano.component";
import { PlanoDuracaoComponent } from "./planos/components/plano-duracao/plano-duracao.component";
import { ModalPlanoDuracaoComponent } from "./planos/components/plano-duracao/modal-plano-duracao/modal-plano-duracao.component";
import { ModalCondicaoPagamentoDuracaoComponent } from "./planos/components/plano-duracao/modal-condicao-pagamento-duracao/modal-condicao-pagamento-duracao.component";
import { AddEditModalidadeComponent } from "./modalidade/add-edit-modalidade/add-edit-modalidade.component";
import { ListModalidadeComponent } from "./modalidade/list-modalidade/list-modalidade.component";
import { TableProdutoComponent } from "./modalidade/table-produto/table-produto.component";
import { ModalConfirmExclusaoComponent } from "./modalidade/modal-confirm-exclusao/modal-confirm-exclusao.component";
import { NgxCurrencyModule } from "ngx-currency";
import { BiMsApiProviderConfigService } from "./api-config-providers/bi-ms-api/bi-ms-api-provider-config.service";
import { BiMsApiModule, BiMsApiConfigProviderBase } from "bi-ms-api";
import { PlanoEmpresaRedeAcessoComponent } from "./planos/components/plano-empresa-rede-acesso/plano-empresa-rede-acesso.component";
import { PlanoHorarioComponent } from "./planos/components/plano-horario/plano-horario.component";
import { CaixaOperadorComponent } from "./caixa-operador/caixa-operador.component";
import { CaixaEmAbertoModule } from "./caixa-em-aberto/caixa-em-aberto.module";
import { ModalAddFotoComponent } from "@adm/adicionar-cliente/modal-add-foto/modal-add-foto.component";
import { ModalAddProfissaoComponent } from "@adm/adicionar-cliente/modal-add-profissao/modal-add-profissao.component";
import { ModalTransferirClienteEmpresaComponent } from "@adm/adicionar-cliente/modal-transferir-cliente-empresa/modal-transferir-cliente-empresa.component";
import { ModalClienteSemelhanteComponent } from "@adm/adicionar-cliente/modal-cliente-semelhante/modal-cliente-semelhante.component";
import { ModalAddConfirmacaoComponent } from "@adm/adicionar-cliente/modal-add-confirmacao/modal-add-confirmacao.component";

import { NotificacaoApiConfigProviderBaseService } from "notificacao-api";
import { ModalEnviarLinkPagamentoComponent } from "@adm/negociacao/modal-enviar-link-pagamento/modal-enviar-link-pagamento.component";

declare var window;
registerLocaleData(localePt, "pt");

// Configurations
ToastDefaults.toast.timeout = 3000;
ToastDefaults.toast.position = SnotifyPosition.rightTop;
@NgModule({
	declarations: [
		AppComponent,
		HomeComponent,
		PlanosComponent,
		TipoPlanoComponent,
		TipoPlanoFormComponent,
		CadastrarPlanoComponent,
		MyAddAccountComponent,
		TableProdutosComponent,
		CadastrarModalidadePlanoComponent,
		CadastrarModalidadeHorarioComponent,
		CadastrarModalidadeRepeticaoComponent,
		PlanoDadosBasicosComponent,
		PlanoDadosContratuaisComponent,
		DadosContratuaisConfigAvancadasComponent,
		PlanoCondicaoPagamentoComponent,
		EditFormPlanoComponent,
		PlanoAdvancedConfigComponent,
		FormProdutoComponent,
		ListaProdutoComponent,
		TableDuracaoValorComponent,
		ConfigNormalComponent,
		ConfigRecorrenciaComponent,
		ConfigCreditoComponent,
		MenuAdmV2Component,
		ConfigRecorrenciaDadosContratuaisComponent,
		PlanoRecorrenciaDadosContratuaisComponent,
		PlanoPersonalDadosContratuaisComponent,
		PlanoCreditoDadosContratuaisComponent,
		PcDadosBasicosComponent,
		CadastrarPlanoCreditoComponent,
		PcModalidadesComponent,
		PcDuracaoValorComponent,
		PcProdutosComponent,
		PcDadosContratualComponent,
		PcCondicaoPagamentoComponent,
		PcEditFormComponent,
		PrCadastroComponent,
		PrDadosBasicosComponent,
		PrTableModalidadesComponent,
		PrTableProdutosComponent,
		PrTableHorariosComponent,
		PrDadosContratuaisComponent,
		PrAdvancedConfigComponent,
		PrAdvancedConfigDadosContratuaisComponent,
		PrEditComponent,
		PcCadastroComponent,
		PcTableHorarioComponent,
		PcConfigDadosBasicosComponent,
		PcConfigDadosContratuaisComponent,
		PlanoEmpresaComponent,
		PpCadastroComponent,
		PpDadosBasicosComponent,
		PpDadosContratuaisComponent,
		PpEditFormComponent,
		TipoPlanoFormComponent,
		PlataformaLinkRedirectComponent,
		DescontoFormComponent,
		DescontoComponent,
		ListaPacoteComponent,
		PacoteFormComponent,
		ProdutosComponent,
		ConvenioDescontoComponent,
		ConvenioDescontoFormComponent,
		TableConvenioDescontoConfigComponent,
		CondicaoPagamentoComponent,
		CondicaoPagamentoPlanoTableComponent,
		CondicaoPagamentoFormComponent,
		ReplicarPlanoComponent,
		DialogConfirmInativarProdutoComponent,
		HorarioComponent,
		HorarioFormComponent,
		AddHorarioComponent,
		DiariasComponent,
		AdmCatTooltipComponent,
		CatTooltipTriggerDirective,
		SafeUrlPipe,
		PlanoPacoteComponent,
		PlanoModalidadeComponent,
		ModalVezesSemanaComponent,
		CategoriaPlanoComponent,
		PlanoDuracaoComponent,
		ModalPlanoDuracaoComponent,
		ModalCondicaoPagamentoDuracaoComponent,
		AddEditModalidadeComponent,
		ListModalidadeComponent,
		TableProdutoComponent,
		ModalConfirmExclusaoComponent,
		PlanoHorarioComponent,
		PlanoEmpresaRedeAcessoComponent,
		SafeUrlPipe,
		ModalAddFotoComponent,
		ModalAddProfissaoComponent,
		ModalTransferirClienteEmpresaComponent,
		ModalClienteSemelhanteComponent,
		ModalAddConfirmacaoComponent,
		CaixaOperadorComponent,
		ModalEnviarLinkPagamentoComponent,
	],
	entryComponents: [
		CadastrarModalidadePlanoComponent,
		CadastrarModalidadeHorarioComponent,
		CadastrarModalidadeRepeticaoComponent,
		PlanoAdvancedConfigComponent,
		PrAdvancedConfigComponent,
		PrAdvancedConfigDadosContratuaisComponent,
		PcConfigDadosBasicosComponent,
		PcConfigDadosContratuaisComponent,
		DialogConfirmInativarProdutoComponent,
		AddHorarioComponent,
		FormConfiguracoesBuilderComponent,
		ModalVezesSemanaComponent,
		ModalPlanoDuracaoComponent,
		ModalCondicaoPagamentoDuracaoComponent,
		ModalConfirmExclusaoComponent,
		ModalAddFotoComponent,
		ModalAddProfissaoComponent,
		ModalTransferirClienteEmpresaComponent,
		ModalClienteSemelhanteComponent,
		ModalAddConfirmacaoComponent,
		ModalEnviarLinkPagamentoComponent,
	],
	imports: [
		NgbModule,
		BrowserAnimationsModule,
		HttpClientModule,
		UiModule,
		PactoLayoutModule,
		NgxMaskModule.forRoot(),
		BrowserModule,
		AppRoutingModule,
		SdkModule,
		ReactiveFormsModule,
		LayoutModule,
		SnotifyModule,
		BaseCoreModule,
		AcessoSistemaApiModule,
		BiMsApiModule,
		CadastroAuxApiModule,
		PessoaMsApiModule,
		PlanoApiModule,
		ProdutoApiModule,
		RelatorioApiModule,
		AdmCoreApiModule,
		AdmLegadoApiModule,
		MsPactopayApiModule,
		AdmUiModule,
		PactoApiModule,
		ZwPactopayApiModule,
		AdmMsApiModule,
		LoginAppApiModule,
		MarketingApiModule,
		CrmApiModule,
		FinanceiroMsApiModule,
		ZwServletApiModule,
		ConfiguracaoModule,
		QuillModule,
		NgxCurrencyModule,
		CaixaEmAbertoModule,
	],
	exports: [LayoutModule, SafeUrlPipe],
	providers: [
		{ provide: LOCALE_ID, useValue: "pt" },
		{ provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptor, multi: true },
		{ provide: HTTP_INTERCEPTORS, useClass: ShareInterceptor, multi: true },
		LoggedinGuard,
		LocalizationService,
		{ provide: "SnotifyToastConfig", useValue: ToastDefaults },
		SnotifyService,
		NgbActiveModal,
		CurrencyPipe,
		DecimalPipe,
		CookieService,
		{
			provide: CadastroAuxApiConfigProviderBase,
			useClass: CadastroAuxApiConfigProviderService,
		},
		{
			provide: AcessoSistemaApiConfigProviderBase,
			useClass: AcessoSistemaApiProviderConfigService,
		},
		{
			provide: PlanoApiConfigProviderBase,
			useClass: PlanoApiConfigProviderService,
		},
		{
			provide: ClubeVantagensApiConfigProviderBase,
			useClass: ClubeVantagesApiProviderConfigService,
		},
		{
			provide: ProdutoApiConfigProviderBase,
			useClass: ProdutoApiConfigProviderService,
		},
		{
			provide: RelatorioApiConfigProviderBase,
			useClass: RelatorioApiConfigProviderService,
		},
		{
			provide: AdmCoreApiConfigProviderBase,
			useClass: AdmCoreApiConfigProviderService,
		},
		{
			provide: AdmLegadoApiConfigProviderBase,
			useClass: AdmLegadoApiConfigProviderService,
		},
		{ provide: PactoApiConfigProviderBase, useClass: PactoApiConfigProvider },
		{
			provide: ZwPactopayApiConfigProviderBase,
			useClass: ZwPactopayApiConfigProvider,
		},
		{
			provide: AdmMsApiConfigProviderBase,
			useClass: AdmMsApiConfigProviderService,
		},
		{
			provide: LoginAppApiConfigProviderBase,
			useClass: LoginAppApiConfigProviderService,
		},
		{
			provide: PessoaMsApiConfigProviderBase,
			useClass: PessoaMsApiConfigProviderService,
		},
		{ provide: CURRENT_MODULE, useValue: PlataformModuleConfig.ADM },
		{ provide: PactoLayoutSDKWrapper, useClass: AdmSdkWrapperService },
		{
			provide: MarketingApiConfigProviderBase,
			useClass: MarketingApiProviderConfigService,
		},
		{
			provide: CrmApiConfigProviderBase,
			useClass: CrmApiConfigProviderService,
		},
		{
			provide: FinanceiroMsApiConfigProviderBase,
			useClass: FinanceiroMsApiConfigProviderService,
		},
		{
			provide: ZwServletApiConfigProviderBase,
			useClass: ZwServletApiConfigProvider,
		},
		{
			provide: MsPactopayApiConfigProviderBase,
			useClass: MsPactopayApiConfigProvider,
		},
		{ provide: TreinoApiConfigProviderBase, useClass: TreinoApiConfigProvider },
		{
			provide: BiMsApiConfigProviderBase,
			useClass: BiMsApiProviderConfigService,
		},
		{
			provide: NotificacaoApiConfigProviderBaseService,
			useClass: NotificacaoApiConfigProvider,
		},
	],
	bootstrap: [AppComponent],
})
export class AppModule {
	constructor(private router: Router, private session: SessionService) {}
}
