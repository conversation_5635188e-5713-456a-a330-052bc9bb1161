import { TipoModalidadeFormComponent } from "@adm/tipo-modalidade/tipo-modalidade-form/tipo-modalidade-form.component";
import { TipoModalidadeComponent } from "@adm/tipo-modalidade/tipo-modalidade.component";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { ComponentsModule, UiModule } from "ui-kit";
import { LayoutModule } from "../layout/layout.module";

import { TipoModalidadeRoutingModule } from "./tipo-modalidade-routing.module";

@NgModule({
	declarations: [TipoModalidadeComponent, TipoModalidadeFormComponent],
	imports: [
		NgbModule,
		CommonModule,
		TipoModalidadeRoutingModule,
		UiModule,
		ComponentsModule,
		LayoutModule,
	],
})
export class TipoModalidadeModule {}
