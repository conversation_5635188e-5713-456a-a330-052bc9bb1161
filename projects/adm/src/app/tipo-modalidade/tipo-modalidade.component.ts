import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiTipoModalidadeService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, SessionService, PerfilAcessoRecurso } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../adm-rest.service";

@Component({
	selector: "adm-tipo-modalidade",
	templateUrl: "./tipo-modalidade.component.html",
	styleUrls: ["./tipo-modalidade.component.scss"],
})
export class TipoModalidadeComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnIdentificador", { static: true })
	columnIdentificador: TemplateRef<any>;
	@ViewChild("tableTipoModalidade", { static: false })
	tableTipoModalidade: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private tipoModalidadeService: CadastroAuxApiTipoModalidadeService,
		private ngbModal: NgbModal
	) {
		this.recurso = this.session.recursos.get(
			PerfilAcessoRecursoNome.TIPO_MODALIDADE
		);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoTipoModalidade() {
		if (
			!this.recurso ||
			!(this.recurso.incluir || this.recurso.incluirConsultar)
		) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, INCLUIR 5.17 - TIPO DE MODALIDADE"
			);
			return;
		}
		this.router.navigate(["adm", "tipo-modalidade", "novo"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editTipoModalidade") {
			this.editTipoModalidade(event.row);
		} else if (event.iconName === "deleteTipoModalidade") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		if (!this.recurso || !this.recurso.excluir) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "EXCLUIR 5.17 - TIPO DE MODALIDADE"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este tipo de modalidade?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteTipoModalidade(event.row);
				}
			})
			.catch((error) => {});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initFilter();
	}

	editTipoModalidade(tipoModalidade) {
		if (
			!this.recurso ||
			(!this.recurso.editar &&
				!this.recurso.consultar &&
				!this.recurso.incluirConsultar)
		) {
			this.notificationService.error(
				"Você não possui permissão para esta operação, 5.17 - TIPO DE MODALIDADE"
			);
			return;
		}
		this.router.navigate(["adm", "tipo-modalidade", tipoModalidade.codigo]);
	}

	deleteTipoModalidade(row: any) {
		if (!this.recurso || !this.recurso.excluir) {
			this.notificationService.error(
				'Você não possui permissão para esta operação, "EXCLUIR 5.17 - TIPO DE MODALIDADE"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		this.tipoModalidadeService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				if (this.tableTipoModalidade.data.content) {
					this.tableTipoModalidade.data.content =
						this.tableTipoModalidade.data.content.filter(
							(tipoModalidade) => tipoModalidade.codigo !== row.codigo
						);
				}
				this.tableTipoModalidade.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [],
			};
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/tipo-modalidade",
					false,
					Api.MSCADAUX
				),
				pagination: true,
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nome",
						titulo: this.columnNome,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "identificador",
						titulo: this.columnIdentificador,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: "editTipoModalidade",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
					},
					{
						nome: "deleteTipoModalidade",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
