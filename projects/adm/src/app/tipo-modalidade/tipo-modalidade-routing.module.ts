import { PerfilAcessoGuard } from "@adm/guards/perfil-acesso.guard";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "@adm/perfil-acesso/perfil-acesso-recurso.model";
import { TipoModalidadeFormComponent } from "@adm/tipo-modalidade/tipo-modalidade-form/tipo-modalidade-form.component";
import { TipoModalidadeComponent } from "@adm/tipo-modalidade/tipo-modalidade.component";
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PerfilAcessoRecurso } from "sdk";

const routes: Routes = [
	{
		path: "",
		component: TipoModalidadeComponent,
	},
	{
		path: "novo",
		component: TipoModalidadeFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.TIPO_MODALIDADE,
				[
					PerfilRecursoPermissoTipo.INCLUIR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
					PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
				]
			),
		},
	},
	{
		path: ":id",
		component: TipoModalidadeFormComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(
				PerfilAcessoRecursoNome.TIPO_MODALIDADE,
				[
					PerfilRecursoPermissoTipo.EDITAR,
					PerfilRecursoPermissoTipo.CONSULTAR,
					PerfilRecursoPermissoTipo.TOTAL,
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
					PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
				]
			),
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class TipoModalidadeRoutingModule {}
