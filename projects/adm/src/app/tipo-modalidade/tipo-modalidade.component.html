<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@tipo-modalidade:modulo"
	i18n-pageTitle="@@tipo-modalidade:title"
	modulo="Configurações"
	pageTitle="Tipo Modalidade">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableTipoModalidade
			idSuffix="tipo-modalidade"
			(btnAddClick)="novoTipoModalidade()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editTipoModalidade($event)"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="tipo-modalidade"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@tipo-modalidade:filtro-situacao" xingling="filtro-situacao">
		Situação
	</span>
	<span i18n="@@tipo-modalidade:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@tipo-modalidade:desativado" xingling="inativo">Inativo</span>
	<span i18n="@@tipo-modalidade:codigo" xingling="codigo">Código</span>
	<span i18n="@@tipo-modalidade:nome" xingling="nome">Nome</span>
	<span i18n="@@tipo-modalidade:identificador" xingling="identificador">
		Identificador
	</span>
	<span i18n="@@tooltip-edit" xingling="TOOLTIP_EDIT">Editar</span>
	<span i18n="@@tooltip-delete" xingling="TOOLTIP_DELETE">Excluir</span>
	<span i18n="@@deleted" xingling="DELETED">Excluído com sucesso!</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@tipo-modalidade:codigo">Código</span>
</ng-template>

<ng-template #columnNome>
	<span i18n="@@tipo-modalidade:nome">Nome</span>
</ng-template>

<ng-template #columnIdentificador>
	<span i18n="@@tipo-modalidade:identificador">Identificador</span>
</ng-template>
