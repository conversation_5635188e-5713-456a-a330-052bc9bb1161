<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@tipo-modalidade:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div *ngIf="isEdicao" class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							id="input-tipo-modalidade-codigo"
							ds3Input
							formControlName="codigo"
							readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Nome -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>* Nome:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							id="input-tipo-modalidade-nome"
							ds3Input
							formControlName="nome" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Identificador -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>* Identificador:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="number"
							id="input-tipo-modalidade-identificador"
							ds3Input
							formControlName="identificador" />
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<pacto-log
				*ngIf="urlLog"
				[table]="true"
				titulo="Log modalidade"
				[url]="urlLog"
				class="mr-2"></pacto-log>

			<button
				id="btn-tipo-modalidade-cancel"
				type="button"
				ds3-outlined-button
				(click)="voltarListagem()">
				Cancelar
			</button>
			<button
				id="btn-tipo-modalidade-save"
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Salvar {{ isEdicao ? "alterações" : "cadastro" }}
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
