import { AdmRestService } from "@adm/adm-rest.service";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
	CadastroAuxApiTipoModalidadeService,
	TipoModalidade,
} from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import { LoaderService } from "ui-kit";

@Component({
	selector: "adm-tipo-modalidade-form",
	templateUrl: "./tipo-modalidade-form.component.html",
	styleUrls: ["./tipo-modalidade-form.component.scss"],
})
export class TipoModalidadeFormComponent implements OnInit {
	form: FormGroup;
	tipoModalidade: TipoModalidade = new TipoModalidade();
	id: string;
	isEdicao = false;
	urlLog: string;
	recurso: PerfilAcessoRecurso;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private session: SessionService,
		private loaderService: LoaderService,
		private tipoModalidadeService: CadastroAuxApiTipoModalidadeService,
		private cd: ChangeDetectorRef,
		private admRest: AdmRestService
	) {
		this.recurso = this.session.recursos.get(
			PerfilAcessoRecursoNome.TIPO_MODALIDADE
		);
	}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id && this.id !== "novo";

		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			nome: ["", Validators.required],
			identificador: [null, Validators.required],
		});

		if (
			this.isEdicao &&
			(this.recurso.consultar || this.recurso.incluirConsultar) &&
			!this.recurso.editar &&
			!this.recurso.incluir
		) {
			this.form.get("nome").disable();
			this.form.get("identificador").disable();
		}

		if (this.isEdicao) {
			this.carregarTipoModalidade();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`tipo-modalidade/log/${this.id}`
			);
		}
	}

	carregarTipoModalidade() {
		this.tipoModalidadeService.find(this.id).subscribe(
			(response) => {
				this.tipoModalidade = response.content;
				this.form.patchValue(this.tipoModalidade);
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar tipo de modalidade");
				this.voltarListagem();
			}
		);
	}

	voltarListagem() {
		this.router.navigate(["adm", "tipo-modalidade"]);
	}

	salvar(): void {
		if (
			!this.recurso ||
			!(
				this.recurso.editar ||
				this.recurso.incluir ||
				!(
					this.isEdicao &&
					(this.recurso.consultar || this.recurso.incluirConsultar)
				)
			)
		) {
			let resource = "INCLUIR";
			if (this.isEdicao) {
				resource = "EDITAR";
			}
			this.notificationService.error(
				`Você não possui permissão para esta operação, "${resource} 5.17 - TIPO DE MODALIDADE"`,
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}

		if (this.form.invalid) {
			this.notificationService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const dadosFormulario = this.form.getRawValue();
		Object.assign(this.tipoModalidade, dadosFormulario);

		this.tipoModalidadeService.save(this.tipoModalidade).subscribe(
			(response) => {
				this.notificationService.success(
					this.isEdicao
						? "Tipo de modalidade atualizado com sucesso!"
						: "Tipo de modalidade cadastrado com sucesso!"
				);
				this.voltarListagem();
			},
			(error) => {
				const errorMessage =
					error &&
					error.error &&
					error.error.meta &&
					error.error.meta.messageValue
						? error.error.meta.messageValue
						: "Erro ao salvar tipo de modalidade.";
				this.notificationService.error(errorMessage);
			}
		);
	}

	get tituloFormulario(): string {
		return this.isEdicao ? "Editar Tipo Modalidade" : "Novo Tipo Modalidade";
	}
}
