import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	FormArray,
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { CatTableEditableComponent, PactoDataGridConfig } from "ui-kit";
import { CaixaEmAbertoService } from "../../caixa-em-aberto.service";
import { SnotifyService } from "ng-snotify";

import moment from "moment";

@Component({
	selector: "adm-cheque",
	templateUrl: "./cheque.component.html",
	styleUrls: ["./cheque.component.scss"],
})
export class ChequeComponent implements OnInit {
	@Input() public quantoFalta: number;
	@Input() public index: number;
	@Input()
	public form: FormGroup;
	public configFG: FormGroup;
	optionsBancos = [];
	optionsCodBancos = [];
	optionsTipoIdentificacao = [
		{ value: "cpf", name: "<PERSON><PERSON>" },
		{ value: "cnpj", name: "<PERSON><PERSON><PERSON>" },
	];
	mascaraDocumentoSel = "000.000.000-00";
	valorInicial: number;
	codBanco = new FormControl();
	public table: PactoDataGridConfig;
	@ViewChild("tableEditable", { static: false })
	public tableEditable: CatTableEditableComponent;

	constructor(
		private readonly fb: FormBuilder,
		private readonly cd: ChangeDetectorRef,
		private caixaAbertoService: CaixaEmAbertoService,
		private noticacaoService: SnotifyService
	) {}

	ngOnInit() {
		Object.keys(this.form.controls).forEach(async (controlName) => {
			if (controlName !== "formaDePagamento" && controlName !== "valor") {
				this.form.removeControl(controlName);
			}
		});
		this.valorInicial = this.quantoFalta;
		this.form.get("valor").setValue(this.quantoFalta);
		this.form.get("valor").enable();
		this.caixaAbertoService.getBanco().subscribe((res) => {
			this.optionsBancos = res.map((v) => ({
				value: v.codigo,
				name: v.nome,
			}));
		});
		const cheques = this.fb.array([]);
		this.form.addControl("cheques", cheques);

		const meses = [];
		this.caixaAbertoService.parcelas.map((v) => {
			const mesParcela = moment(v.dataVencimento).format("MM/YYYY");
			if (!meses.find((tp) => tp === mesParcela)) {
				meses.push(mesParcela);
			}
		});

		this.configFG = this.fb.group({
			banco: this.fb.control("", Validators.required),
			agencia: this.fb.control("", Validators.required),
			conta: this.fb.control("", Validators.required),
			numero: this.fb.control("", Validators.required),
			tipoDeIdentificacao: this.fb.control("CPNJ ou CPF", Validators.required),
			documento: this.fb.control("", Validators.required),
			quantidadeDeCheques: this.fb.control(meses.length, Validators.required),
			nome: this.fb.control(""),
		});
		// this.formArrayCheques.valueChanges.subscribe((v) => {
		// this.formArrayCheques.setErrors({
		// 	editandoOuNaoGerados:
		// 		this.taEditandoOuAdicionando && !this.chequesGerados,
		// });
		// });
	}

	gerarCheques() {
		const qtd = this.configFG.get("quantidadeDeCheques").value;
		const valorInicialPorCheque = parseFloat(
			(this.form.get("valor").value / qtd).toFixed(2)
		);
		const sobraValor = parseFloat(
			(this.form.get("valor").value - valorInicialPorCheque * qtd).toFixed(2)
		);
		if (sobraValor !== 0) {
			this.noticacaoService.info(
				"O diferencial da divisão do valor foi inserido na primeira parcela",
				{ timeout: 5000 }
			);
		}
		for (let index = 0; index < qtd; index++) {
			let valorCheque = (
				index == 0 ? valorInicialPorCheque + sobraValor : valorInicialPorCheque
			).toFixed(2);
			const cheque = this.fb.group({
				banco: this.fb.control(""),
				agencia: this.fb.control(""),
				conta: this.fb.control(""),
				numero: this.fb.control(""),
				nome: this.fb.control(""),
				documento: this.fb.control(""),
				dataCompensacao: this.fb.control(moment().toDate()),
				valor: this.fb.control(valorCheque),
			});
			cheque.patchValue(this.configFG.getRawValue());
			cheque
				.get("documento")
				.setValue(this.ajustarMascara(cheque.get("documento").value));
			this.formArrayCheques.push(cheque);
		}
		this.gerarDatas();
		this.gerarNumero();
		this.atualizarValores();
		if (this.formArrayCheques.length > 0) {
			this.form.get("valor").disable();
		}
	}

	get formArrayCheques(): FormArray {
		return this.form.get("cheques") as FormArray;
	}

	gerarDatas() {
		const dataInicial = moment(
			this.formArrayCheques.controls[0].get("dataCompensacao").value
		);
		this.formArrayCheques.controls.forEach((v, i) => {
			if (v instanceof FormGroup) {
				const novaData = dataInicial.clone().add(i, "months");
				v.get("dataCompensacao").setValue(novaData.toDate());
			}
		});
	}

	gerarNumero() {
		const numeroInicialCheque =
			this.formArrayCheques.controls[0].get("numero").value;
		if (!isNaN(Number(numeroInicialCheque))) {
			this.formArrayCheques.controls.forEach((v, i) => {
				if (v instanceof FormGroup) {
					v.get("numero").setValue(Number(numeroInicialCheque) + i);
				}
			});
		}
	}

	atualizarValores() {
		let valorSomadoCheques = 0;
		this.formArrayCheques.controls.forEach((v, i) => {
			if (v instanceof FormGroup) {
				valorSomadoCheques += parseFloat(v.get("valor").value);
			}
		});
		if (this.form.get("valor").value !== valorSomadoCheques) {
			this.form.get("valor").setValue(valorSomadoCheques);
		}
	}

	addCheque(i: number) {
		const novoCheque = this.fb.group({
			banco: this.fb.control(""),
			agencia: this.fb.control(""),
			conta: this.fb.control(""),
			numero: this.fb.control(""),
			nome: this.fb.control(""),
			documento: this.fb.control(""),
			dataCompensacao: this.fb.control(moment().toDate()),
			valor: this.fb.control(0),
		});
		this.formArrayCheques.insert(i, novoCheque);
		this.gerarDatas();
		this.atualizarValores();
	}

	removeCheque(i: number) {
		this.formArrayCheques.removeAt(i);
		if (this.formArrayCheques.length > 1) {
			this.gerarDatas();
		}
		this.atualizarValores();
		if (this.formArrayCheques.length === 0) {
			this.form.get("valor").enable();
			this.form.get("valor").setValue(this.valorInicial);
		}
	}

	public mascaraDocumento() {
		const tipoDeIdentificacao = this.configFG.get("tipoDeIdentificacao").value;
		if (tipoDeIdentificacao === "cnpj") {
			this.mascaraDocumentoSel = "00.000.000/0000-00";
		} else {
			this.mascaraDocumentoSel = "000.000.000-00";
		}
		return this.mascaraDocumentoSel;
	}

	public mascaraDocumentoItem(index) {
		let value = this.formArrayCheques.controls[index].get("documento").value;
		value = this.ajustarMascara(value);
		this.formArrayCheques.controls[index].get("documento").setValue(value);
	}

	ajustarMascara(value) {
		if (!value) {
			return value;
		}
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 14);
		if (value.length <= 11) {
			value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
		} else {
			value = value.replace(
				/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g,
				"$1.$2.$3/$4-$5"
			);
		}
		return value;
	}

	setBanco(valorSelecionado) {
		if (
			this.optionsBancos.some((b) => b.value === parseInt(valorSelecionado))
		) {
			this.codBanco.setValue(parseInt(valorSelecionado));
			this.configFG.controls["banco"].setValue(parseInt(valorSelecionado));
		} else {
			this.codBanco.reset();
			this.configFG.controls["banco"].reset();
		}
	}
}
