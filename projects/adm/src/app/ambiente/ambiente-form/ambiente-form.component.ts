import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	CadastroAuxApiAmbienteService,
	Ambiente,
	TipoAmbiente,
	SituacaoAmbiente,
} from "cadastro-aux-api";
import { LoaderService } from "ui-kit";

@Component({
	selector: "adm-ambiente-form",
	templateUrl: "./ambiente-form.component.html",
	styleUrls: ["./ambiente-form.component.scss"],
})
export class AmbienteFormComponent implements OnInit {
	form: FormGroup;
	ambiente: Ambiente = new Ambiente();
	isEdicao = false;
	tituloFormulario = "Novo Ambiente";

	tiposAmbiente = [
		{ value: "Sessao", label: "Sessão" },
		{ value: "Outros", label: "Outros" },
	];

	coletores = [{ value: "", label: "--Selecione--" }];

	situacoesAmbiente = [
		{ value: "Ativo", label: "Ativo" },
		{ value: "Inativo", label: "Inativo" },
	];

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private snotifyService: SnotifyService,
		private loaderService: LoaderService,
		private ambienteService: CadastroAuxApiAmbienteService
	) {
		this.initForm();
	}

	ngOnInit(): void {
		this.loaderService.initForce();
		this.checkRouteParams();
	}

	initForm(): void {
		this.form = new FormGroup({
			codigo: new FormControl({ value: "", disabled: true }),
			descricao: new FormControl("", [Validators.required]),
			capacidade: new FormControl(""),
			tipoAmbiente: new FormControl("", [Validators.required]),
			coletor: new FormControl(""),
			situacaoAmbiente: new FormControl("Ativo", [Validators.required]),
			piscinaMgb: new FormControl(""),
		});
	}

	checkRouteParams(): void {
		const id = this.route.snapshot.paramMap.get("id");
		if (id && id !== "novo") {
			this.isEdicao = true;
			this.tituloFormulario = "Editar Ambiente";
			this.carregarAmbiente(+id);
		}
	}

	carregarAmbiente(id: number): void {
		this.ambienteService.find(id).subscribe((response) => {
			this.ambiente = response.data;
			this.form.patchValue(this.ambiente);
		});
	}

	salvar(): void {
		if (this.form.valid) {
			const ambienteData = { ...this.ambiente, ...this.form.value };
			this.ambienteService.save(ambienteData).subscribe(() => {
				this.snotifyService.success("Ambiente salvo com sucesso!");
				this.voltarListagem();
			});
		}
	}

	novo(): void {
		this.form.reset();
		this.ambiente = new Ambiente();
		this.isEdicao = false;
		this.tituloFormulario = "Novo Ambiente";
		this.form.get("situacaoAmbiente").setValue("Ativo");
	}

	excluir(): void {
		if (this.isEdicao && confirm("Deseja realmente excluir este ambiente?")) {
			this.ambienteService.delete(this.ambiente.codigo).subscribe(() => {
				this.snotifyService.success("Ambiente excluído com sucesso!");
				this.voltarListagem();
			});
		}
	}

	voltarListagem(): void {
		this.router.navigate(["/adm/config-financeiras/ambiente"]);
	}
}
