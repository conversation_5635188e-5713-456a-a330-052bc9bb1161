<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@ambiente:title"
	modulo="Administrativo"
	[pageTitle]="tituloFormulario">
	<pacto-cat-card-plain class="compact-card-padding">
		<form class="compact-form" [formGroup]="form">
			<!-- Código -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Código</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigo" readonly />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricao" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Capacidade -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Capacidade:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" ds3Input formControlName="capacidade" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Tipo do Ambiente -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Tipo do Ambiente:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="tiposAmbiente"
							[useFullOption]="false"
							[placeholder]="'Selecione o tipo'"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="form.get('tipoAmbiente')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Coletor -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Coletor:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="coletores"
							[useFullOption]="false"
							[placeholder]="'Selecione'"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="form.get('coletor')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Situação do Ambiente -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Situação do Ambiente:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="situacoesAmbiente"
							[useFullOption]="false"
							[placeholder]="'Selecione a situação'"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="form.get('situacaoAmbiente')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Piscina Mgb -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Piscina Mgb:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" ds3Input formControlName="piscinaMgb" />
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="button-actions-centered">
			<button type="button" ds3-outlined-button (click)="novo()">Novo</button>
			<button
				type="button"
				ds3-flat-button
				[disabled]="form.invalid"
				(click)="salvar()">
				Gravar
			</button>
			<button type="button" ds3-outlined-button (click)="excluir()">
				Excluir
			</button>
			<button type="button" ds3-outlined-button (click)="voltarListagem()">
				Voltar para a Lista
			</button>
			<button type="button" ds3-outlined-button id="log">
				<i class="pct pct-list"></i>
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
