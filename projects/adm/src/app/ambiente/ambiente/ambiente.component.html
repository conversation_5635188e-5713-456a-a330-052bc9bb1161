<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@ambiente:modulo"
	i18n-pageTitle="@@ambiente:title"
	modulo="Configurações"
	pageTitle="Ambiente">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableAmbiente
			(btnAddClick)="novoAmbiente()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editAmbiente($event)"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="ambiente"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@ambiente:filtro-situacao" xingling="filtro-situacao">
		Situação
	</span>
	<span i18n="@@ambiente:ativo" xingling="ativo">Ativo</span>
	<span i18n="@@ambiente:desativado" xingling="inativo">Inativo</span>

	<span i18n="@@ambiente:ambienteDeleted" xingling="DELETED">
		Ambiente excluído com sucesso
	</span>
	<span i18n="@@ambiente:editAction" xingling="ACTION_EDIT">Editar</span>
	<span i18n="@@ambiente:editTooltip" xingling="TOOLTIP_EDIT">
		Editar Ambiente
	</span>
	<span i18n="@@ambiente:deleteAction" xingling="ACTION_DELETE">Excluir</span>
	<span i18n="@@ambiente:deleteTooltip" xingling="TOOLTIP_DELETE">
		Excluir Ambiente
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@ambiente:columnCodigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@ambiente:columnDescricao">Descrição</span>
</ng-template>
<ng-template #columnCapacidade>
	<span i18n="@@ambiente:columnCapacidade">Capacidade</span>
</ng-template>
<ng-template #columnTipoAmbiente>
	<span i18n="@@ambiente:columnTipoAmbiente">Tipo do Ambiente</span>
</ng-template>
<ng-template #columnSituacao>
	<span i18n="@@ambiente:columnSituacao">Situação do Ambiente</span>
</ng-template>
