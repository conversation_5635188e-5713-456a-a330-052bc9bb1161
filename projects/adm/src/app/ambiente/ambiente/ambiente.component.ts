import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiAmbienteService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../adm-rest.service";

@Component({
	selector: "adm-ambiente",
	templateUrl: "./ambiente.component.html",
	styleUrls: ["./ambiente.component.scss"],
})
export class AmbienteComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnCapacidade", { static: true })
	columnCapacidade: TemplateRef<any>;
	@ViewChild("columnTipoAmbiente", { static: true })
	columnTipoAmbiente: TemplateRef<any>;
	@ViewChild("columnSituacao", { static: true })
	columnSituacao: TemplateRef<any>;
	@ViewChild("tableAmbiente", { static: false })
	tableAmbiente: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private ambienteService: CadastroAuxApiAmbienteService,
		private ngbModal: NgbModal
	) {}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoAmbiente() {
		this.router.navigate(["adm", "novo-ambiente"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editAmbiente") {
			this.editAmbiente(event.row);
		} else if (event.iconName === "deleteAmbiente") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		const modalConfirmacao = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.componentInstance.message =
			"Deseja realmente excluir este ambiente?";

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteAmbiente(event.row);
				}
			})
			.catch((error) => {});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initFilter();
	}

	editAmbiente(ambiente) {
		this.router.navigate(["adm", "ambiente", ambiente.codigo]);
	}

	deleteAmbiente(row: any) {
		this.ambienteService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				this.tableAmbiente.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "situacao",
						label: this.traducao.getLabel("filtro-situacao"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "true", label: this.traducao.getLabel("ativo") },
							{ value: "false", label: this.traducao.getLabel("inativo") },
						],
						initialValue: ["true"],
					},
				],
			};
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/ambiente",
					false,
					Api.MSCADAUX
				),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "capacidade",
						titulo: this.columnCapacidade,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "tipoAmbiente",
						titulo: this.columnTipoAmbiente,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "situacaoAmbiente",
						titulo: this.columnSituacao,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: "editAmbiente",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
						actionFn: (row) => this.editAmbiente(row),
					},
					{
						nome: "deleteAmbiente",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
						actionFn: (row) => this.deleteAmbiente(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
