import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnInit,
	Optional,
	Renderer2,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService, SessionService } from "sdk";
import { MsPactopayApiConvenioCobrancaService } from "ms-pactopay-api";
import {
	AdmCoreApiNegociacaoService,
	AdmCoreApiQuestionarioClienteService,
	ApiResponseSingle as AdmCoreApiResponseSingle,
	Negociacao,
	OrigemSistema,
	Plano,
	QuestionarioCliente,
} from "adm-core-api";
import { AbstractControl, FormControl } from "@angular/forms";
import { NegociacaoService } from "../negociacao.service";
import { ModalObservacaoComponent } from "../modal-observacao/modal-observacao.component";
import { ModalOpcoesAvancadasComponent } from "../modal-opcoes-avancadas/modal-opcoes-avancadas.component";
import { ModalEditarParcelasComponent } from "../modal-editar-parcelas/modal-editar-parcelas.component";
import { ModalAutorizacaoCobrancaComponent } from "../modal-autorizacao-cobranca/modal-autorizacao-cobranca.component";
import { ModalEscolhaClienteNegociacaoComponent } from "../modal-escolha-cliente-negociacao/modal-escolha-cliente-negociacao.component";
import {
	BUTTON_SIZE,
	BUTTON_TYPE,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	LoaderService,
	PactoModalSize,
	PactoModalRef,
	TraducoesXinglingComponent,
} from "ui-kit";

import { ModalEnviarLinkPagamentoComponent } from "../modal-enviar-link-pagamento/modal-enviar-link-pagamento.component";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	ApiResponseSingle as AdmLegadoApiResponseSingle,
	Empresa,
} from "adm-legado-api";
import { DescontoNegociacaoComponent } from "../desconto-negociacao/desconto-negociacao.component";
import { ModalRenovarContratoComponent } from "../modal-renovar-contrato/modal-renovar-contrato.component";
import {
	LayoutNavigationService,
	PermissaoService,
	PlataformModuleConfig,
} from "pacto-layout";
import { Observable, Subject } from "rxjs";
import { ModalAvisoEdicaoParcelasComponent } from "../modal-aviso-edicao-parcelas/modal-aviso-edicao-parcelas.component";
import { ParcelasNegociacaoComponent } from "../parcelas-negociacao/parcelas-negociacao.component";
import { ModalDadosObrigatoriosComponent } from "../modal-dados-obrigatorios/modal-dados-obrigatorios.component";
import { InformacoesPessoaService } from "pessoa-ms-api";
import { ModalDataLancamentoComponent } from "../modal-data-lancamento/modal-data-lancamento.component";
import { ModalValoresArrendondarComponent } from "../modal-valores-arrendondar/modal-valores-arrendondar.component";
import { ModalAvisoClienteRestricaoComponent } from "../modal-aviso-cliente-restricao/modal-aviso-cliente-restricao.component";
import { MatDialog } from "@angular/material";
import { ModalBvComponent } from "../modal-bv/modal-bv.component";
import { Util } from "../../util/Util";
import { ModalAutorizacaoCobrancaObrigatorioComponent } from "../modal-autorizacao-cobranca-obrigatorio/modal-autorizacao-cobranca-obrigatorio.component";
import { LinkAcessoRapidoService } from "adm-legado-api";
import { PlataformaMenuV2Config } from "../../../../../pacto-layout/src/lib/sdk-wrapper/sdk-wrappers";
import { PlataformaModulo } from "../../../../../sdk/src/lib/services/models/client-discovery.model";
import { AdmSdkWrapperService } from "@adm/services/adm-sdk-wrapper.service";

import { ModalService } from "@base-core/modal/modal.service";
import { ModalNotificacaoComponent } from "@adm/negociacao/modal-autorizacao-cobranca/modal-notificacao/modal-notificacao.component";

@Component({
	selector: "adm-contrato",
	templateUrl: "./contrato.component.html",
	styleUrls: ["./contrato.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContratoComponent implements OnInit, AfterViewInit {
	@ViewChild("fixo", { static: false }) fixo: ElementRef;
	@ViewChild("fluxo", { static: false }) fluxo: ElementRef;
	@ViewChild("addDescontoComponent", { static: false })
	addDescontoComponent: DescontoNegociacaoComponent;
	@ViewChild("parcelas", { static: false })
	parcelas: ParcelasNegociacaoComponent;
	@ViewChild("infosTexto", { static: true })
	xingling: TraducoesXinglingComponent;
	planoFc = new FormControl();
	duracaoFc = new FormControl();
	condicaoFc = new FormControl();
	diaCartaoFc = new FormControl();
	horarioFc = new FormControl();
	creditoFc = new FormControl();
	planos: Array<Plano>;
	planosOriginais: Array<Plano>;
	negociacao: Negociacao;
	simulado;
	primeiraSimulacao;
	cliente;
	usarArredondamento = false;
	parcelasAberto = 0.0;
	planoSelecionado = false;
	condicoes = new Array<any>();
	creditos = new Array<any>();
	creditoEscolhido;
	parcelasEditadas;
	blockButtons = false;
	irParaVersaoAntiga = false;
	recursoNegociacaoPadraoEmpresa: boolean = true;
	recurso = "NEGOCIACAO";
	usaProdutos = false;
	usaDesconto = false;
	autorizacaoCobrancaCliente;
	codigoConvenioCobranca;
	configEmpresa;
	cadastrouCartao = false;
	check;
	linha;
	isIntegradoMgb = false;
	private dataLancamentoContratoSelecionada: number;
	private carregamentoInicial: boolean = false;

	private abriuBV: boolean = false;
	private atualizouBV: boolean = false;
	questionarioCliente: QuestionarioCliente | undefined = undefined;
	private alterouDataInicioContrato: boolean = false;
	private clienteSelecionado$ = new Subject<void>();
	configuracao: PlataformaMenuV2Config = new PlataformaMenuV2Config();

	@ViewChild(DescontoNegociacaoComponent, { static: false })
	descontoNegociacaoComponent: DescontoNegociacaoComponent;
	constructor(
		private cd: ChangeDetectorRef,
		private layoutNavigationService: LayoutNavigationService,
		private service: AdmCoreApiNegociacaoService,
		private modal: DialogService,
		private router: Router,
		private negociacaoState: NegociacaoService,
		private renderer: Renderer2,
		private el: ElementRef,
		private route: ActivatedRoute,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private loaderService: LoaderService,
		private clientDiscoveryService: ClientDiscoveryService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private pessoaService: InformacoesPessoaService,
		private permissaoService: PermissaoService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private matDialog: MatDialog,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private modalService: ModalService,
		private readonly msPactopayApiConvenioCobranca: MsPactopayApiConvenioCobrancaService,
		private questionarioClienteService: AdmCoreApiQuestionarioClienteService,
		private linkAcessoRapidoService: LinkAcessoRapidoService,
		@Optional()
		private pactoLayoutSDKWrapper: AdmSdkWrapperService
	) {}

	ngOnInit() {
		this.carregamentoInicial = true;
		this.init();
	}

	ngAfterViewInit() {}

	carregarRecursoPadraoEmpresa() {
		this.service.recursoPadraoEmpresa(this.recurso).subscribe(
			(response) => {
				this.recursoNegociacaoPadraoEmpresa = response;
			},
			(httpErrorResponse) => {
				console.log(httpErrorResponse);
				this.recursoNegociacaoPadraoEmpresa = false;
			}
		);
	}

	clear() {
		this.duracaoFc = new FormControl();
		this.condicaoFc = new FormControl();
		this.horarioFc = new FormControl();
	}

	init() {
		this.route.params.subscribe((params) => {
			this.linha = params.linha;
		});
		this.loadEmpresa();
		this.carregarRecursoPadraoEmpresa();
		this.notificar("NEGOCIACAO_ENTROU");
		this.negociacaoState.getConfigsContrato().observacao = null;
		this.negociacaoState.getConfigsContrato().contratoRenovar = null;
		this.negociacaoState.getConfigsContrato().contratoBase = null;
		this.negociacaoState.getConfigsContrato().usuario =
			this.sessionService.loggedUser.id;
		this.negociacaoState.getConfigsContrato().origemSistema =
			OrigemSistema.NOVA_TELA_NEGOCIACAO;
		this.route.params.subscribe((params) => {
			this.linha = params.linha;
			if (params.id) {
				this.irParaVersaoAntiga = true;
				this.service.clientes(params.id).subscribe((result) => {
					this.consultarIntegracaoMgb(result[0].codigo);
					this.selectCliente(result[0], null);
					this.clienteSelecionado$.subscribe(() => {
						this.selectPlano();
					});
					setTimeout(() => {
						this.onScroll(null);
					}, 1000);
				});
			} else {
				this.abrirEscolherCliente(null);
				setTimeout(() => {
					this.onScroll(null);
				}, 1000);
			}
		});

		if (this.pactoLayoutSDKWrapper) {
			this.configuracao.key = this.pactoLayoutSDKWrapper.companyKey();
			this.configuracao.empresa =
				this.pactoLayoutSDKWrapper.loggedCompanyId.toString();
			this.configuracao.codigoUsuarioZw =
				this.pactoLayoutSDKWrapper.codigoUsuarioZw()
					? +this.pactoLayoutSDKWrapper.codigoUsuarioZw()
					: 0;
			this.configuracao.independente =
				this.pactoLayoutSDKWrapper.modulosHabilitados()
					? this.pactoLayoutSDKWrapper.modulosHabilitados().includes("ZW")
					: false;
			this.pactoLayoutSDKWrapper.getConfig().subscribe((response) =>
				Object.keys(response).forEach((key) => {
					if (!this.configuracao[key]) {
						this.configuracao[key] = response[key];
					}
				})
			);
		}
	}

	private consultarPlanos(
		planoForcar,
		dataLancamentoContrato: number,
		contrato
	) {
		const incluirBolsa = this.sessionService.perfilUsuario.funcionalidades.get(
			"lancarplanostipobolsa"
		);
		this.dataLancamentoContratoSelecionada = dataLancamentoContrato;
		this.service
			.consultarPlanos(
				incluirBolsa,
				planoForcar,
				this.cliente.codigo,
				contrato,
				dataLancamentoContrato
			)
			.subscribe((planos) => {
				this.planosOriginais = planos;
				this.planos = planos;
				if (this.cliente) {
					this.filtrarPlanosPorCategoria(this.cliente.codigoCategoria);
				}
				this.cd.detectChanges();
				if (planoForcar > 0) {
					this.planoFc.setValue(planoForcar);
				} else {
					this.planoFc.valueChanges.subscribe(() => {
						this.selectPlano();
					});
				}
			});
	}

	private loadEmpresa() {
		this.telaClienteService
			.configuracoesEmpresa(
				this.sessionService.chave,
				+this.sessionService.empresaId
			)
			.subscribe((response: AdmLegadoApiResponseSingle<Empresa>) => {
				this.configEmpresa = response.content;
			});
	}

	private consultarIntegracaoMgb(codigoCliente) {
		try {
			this.admLegadoTelaClienteService
				.consultarDadosMgbAluno(
					this.sessionService.chave,
					this.sessionService.empresaId,
					codigoCliente
				)
				.subscribe((response) => {
					this.isIntegradoMgb = response.integrado;
				});
		} catch (e) {
			console.error(e);
		}
	}

	aplicarPlanoRenovacao(plano, emitEventControlPlano: boolean = true) {
		if (this.planos && this.planos.length > 0) {
			const planoEncontrado = this.planos.find((p) => p.codigo === plano);
			if (planoEncontrado) {
				if (planoEncontrado.bloquearRecompra) {
					return;
				}
				this.planoFc.setValue(plano, { emitEvent: emitEventControlPlano });
				return;
			} else {
				return;
			}
		}
		this.service
			.consultarPlanos(
				this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades.findIndex(
					(f) => f.nome === "lancarplanostipobolsa" && f.possuiFuncionalidade
				) > -1,
				plano,
				this.dataLancamentoContratoSelecionada,
				this.cliente.codigo,
				this.linha
			)
			.subscribe((planos) => {
				this.planosOriginais = planos;
				this.planos = planos;
				const planoConsultado = planos.find((p) => p.codigo === plano);
				if (planoConsultado) {
					if (planoConsultado.bloquearRecompra) {
						return;
					} else {
						this.planoFc.setValue(plano, { emitEvent: emitEventControlPlano });
					}
				}
			});
	}

	abrirModalRenovar(check, cliente, acao) {
		this.cliente = cliente;
		const modal = this.modal.open(
			"Renovação de contrato",
			ModalRenovarContratoComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.data = check.finalContratoAtivo;
		modal.result.then(
			(escolha) => {
				if (escolha === "sim") {
					this.simulado.situacaoContrato = "Renovação";
					this.aplicarPlanoRenovacao(check.planoRenovacao, false);
					this.selectPlanoTipo("RN", check.codigoContratoRenovacao);
					this.negociacaoState.getConfigsContrato().contratoBase =
						check.codigoContratoRenovacao;
					const resetar: boolean =
						this.cliente && this.cliente.codigo && this.cliente.codigo > 0;
					this.negociacaoState.updateCliente(cliente);
					this.parcelasAberto = check.valorParcelasAberto;
					if (resetar) {
						this.reset();
					} else {
						this.simular();
					}
					if (acao) {
						this.abrirAutorizacao(acao);
					} else {
						this.cd.detectChanges();
					}
				} else if (check.permiteContratoConcomitante === false) {
					this.resetAll();
				} else {
					this.negociacaoState.updateCliente(cliente);
					this.cliente = cliente;
					this.parcelasAberto = check.valorParcelasAberto;
					if (acao) {
						this.abrirAutorizacao(acao);
					} else {
						this.cd.detectChanges();
					}
				}
				this.loadBvCliente(this.cliente.codigo);
			},
			() => {
				if (check.permiteContratoConcomitante === false) {
					this.resetAll();
				}
			}
		);
	}

	abrirModalRematricula(check, cliente, acao) {
		this.cliente = cliente;
		const modal = this.modal.open(
			"Rematricular aluno",
			ModalRenovarContratoComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.renovar = false;
		if (check.permiteContratoConcomitante === false) {
			modal.componentInstance.novaMatricula = false;
		}
		modal.result.then(
			(escolha) => {
				const resetar: boolean =
					this.cliente && this.cliente.codigo && this.cliente.codigo > 0;
				this.negociacaoState.updateCliente(cliente);
				if (resetar) {
					this.reset();
				}
				this.parcelasAberto = check.valorParcelasAberto;
				if (acao) {
					this.abrirAutorizacao(acao);
				} else {
					this.cd.detectChanges();
				}
				if (escolha === "sim") {
					this.simulado.situacaoContrato = "Rematrícula";
					this.negociacaoState.getConfigsContrato().contratoBase =
						check.codigoContratoRematricula;
					this.selectPlanoTipo(
						"RE",
						this.negociacaoState.getConfigsContrato().contratoBase
					);
				} else {
					this.negociacaoState.getConfigsContrato().contratoBase = null;
				}
				this.simular();
			},
			() => {
				if (check.permiteContratoConcomitante === false) {
					this.resetAll();
				}
			}
		);
	}

	get tipoVenda() {
		const tipo =
			this.simulado && this.simulado.situacaoContrato
				? this.simulado.situacaoContrato
				: "";
		if (tipo === "Rematrícula") {
			return "RE";
		} else if (tipo === "Renovação") {
			return "RN";
		}
		if (this.linha && this.linha.startsWith("rematricular")) {
			return "RE";
		} else if (this.linha && this.linha.startsWith("renovar")) {
			return "RN";
		}
		return "MA";
	}

	selectPlano() {
		if (
			this.linha &&
			(this.linha.startsWith("renovar") ||
				this.linha.startsWith("rematricular"))
		) {
			this.negociacaoState.getConfigsContrato().contratoBase = this.linha
				.replace("rematricular_", "")
				.replace("renovar_", "");
		}
		this.selectPlanoTipo(
			this.tipoVenda,
			this.negociacaoState.getConfigsContrato().contratoBase
				? this.negociacaoState.getConfigsContrato().contratoBase
				: 0
		);
	}

	selectPlanoTipo(tipo, contrato) {
		this.service
			.dadosNegociacao(this.planoFc.value, tipo, contrato)
			.subscribe((negociacao) => {
				if (this.addDescontoComponent) {
					this.addDescontoComponent.reset();
				}
				this.negociacaoState.getConfigsContrato().arredondamento = null;
				this.negociacaoState.getConfigsContrato().produtos = [];
				negociacao.produtos.forEach((produto) => {
					if (produto.obrigatorio) {
						this.negociacaoState.getConfigsContrato().produtos.push(produto);
					}
				});
				this.clear();
				this.negociacao = negociacao;
				this.usarArredondamento = negociacao.usaArredondamento;
				this.initModalidadesPlano();
				this.planoSelecionado = true;
				this.cd.detectChanges();
				if (
					!negociacao.duracaoSugerida &&
					negociacao.duracoes &&
					negociacao.duracoes.length > 0
				) {
					this.duracaoFc.setValue(negociacao.duracoes[0].codigo);
				} else {
					this.duracaoFc.setValue(negociacao.duracaoSugerida);
				}
				this.prepararCondicoes(false);
				this.duracaoFc.valueChanges.subscribe(() => {
					this.prepararCondicoes(true);
					this.prepararCreditos();
					if (!this.carregamentoInicial) {
						this.simular();
					}
				});
				if (
					!negociacao.horarioSugerido ||
					(this.negociacao.creditoSessao && this.negociacao.horarios.length > 0)
				) {
					this.horarioFc.setValue(this.negociacao.horarios[0].codigo);
				} else {
					this.horarioFc.setValue(negociacao.horarioSugerido);
				}
				this.horarioFc.valueChanges.subscribe(() => {
					this.prepararCreditos();
					if (!this.carregamentoInicial) {
						this.simular();
					}
				});
				if (this.negociacao.creditoSessao === true) {
					this.horarioFc.disable();
				} else {
					this.horarioFc.enable();
				}
				if (negociacao.diaSugerido && negociacao.diaSugerido > 0) {
					this.diaCartaoFc = new FormControl(negociacao.diaSugerido);
					this.diaCartaoFc.valueChanges.subscribe(() => {
						if (!this.carregamentoInicial) {
							this.simular();
						}
					});
				}
				this.prepararCreditos();
				setTimeout(() => {
					this.negociacaoState.update(this.negociacao);
					this.onScroll(null);
				}, 200);
				if (
					!this.carregamentoInicial ||
					this.linha === undefined ||
					this.linha.startsWith("novo")
				) {
					this.simular();
				}
				this.carregamentoInicial = false;
			});
	}

	removerDescontoTrocaDuracao() {
		this.descontoNegociacaoComponent.removerDescontosExtras();
	}

	initModalidadesPlano() {
		const modalidades = new Array<any>();
		const pacotes = new Array<any>();
		if (
			this.negociacao &&
			this.negociacao.pacotes &&
			this.negociacao.pacotesSugeridos &&
			this.negociacao.pacotesSugeridos.length > 0
		) {
			for (const idPacote of this.negociacao.pacotesSugeridos) {
				if (pacotes.length > 0) {
					break;
				}
				for (const pacote of this.negociacao.pacotes) {
					if (idPacote === pacote.codigo) {
						pacotes.push(pacote);
						break;
					}
				}
			}
		} else if (
			this.negociacao &&
			this.negociacao.modalidades &&
			this.negociacao.modalidadesSugeridas
		) {
			this.negociacao.modalidadesSugeridas.forEach((idModalidade) => {
				this.negociacao.modalidades.forEach((modalidade) => {
					if (idModalidade === modalidade.codigo) {
						const row: any = modalidade;
						row.nrvezes = modalidade.nrvezes;
						row.frequencia = modalidade.nrvezes + "x";
						row.valor = modalidade.valorModalidade;
						row.utilizarTurma = modalidade.utilizarTurma;
						if (modalidade.vezesSugeridas) {
							row.frequencia = modalidade.vezesSugeridas.descricao;
							row.nrvezes = modalidade.vezesSugeridas.vezes;
							if (modalidade.vezesSugeridas.tipoOperacao === "EX") {
								row.valorModalidade = modalidade.vezesSugeridas.valorEspecifico;
							}
						}
						row.turmas = [];
						if (this.check && this.check.horariosTurma) {
							this.check.horariosTurma.forEach((turma) => {
								if (turma.modalidade === modalidade.codigo) {
									row.turmas.push(turma);
								}
							});
						}
						modalidades.push(row);
					}
				});
			});
		}
		this.negociacaoState.getConfigsContrato().pacotes = pacotes;
		this.negociacaoState.getConfigsContrato().modalidades = modalidades;
	}

	prepararCreditos() {
		this.creditoEscolhido = null;
		this.creditos = [];
		if (
			this.negociacao.vendaCreditoTreino === true &&
			this.duracaoFc.value &&
			this.horarioFc.value
		) {
			this.creditoFc = new FormControl();
			this.negociacao.duracoes.forEach((duracao) => {
				// tslint:disable-next-line:radix
				if (duracao.codigo === parseInt(this.duracaoFc.value)) {
					this.negociacao.horarios.forEach((horario) => {
						// tslint:disable-next-line:radix
						if (horario.codigo === parseInt(this.horarioFc.value)) {
							duracao.creditos.forEach((credito) => {
								if (horario.livre === credito.livre) {
									this.creditoFc.setValue(credito.codigo);
									this.creditoEscolhido = credito;
									this.creditos.push(credito);
								}
							});
						}
					});
				}
			});
			if (
				this.check &&
				this.check.creditosSugerido &&
				this.check.creditosSugerido > 0
			) {
				this.creditos.forEach((credito) => {
					if (credito.quantidadeCreditoCompra === this.check.creditosSugerido) {
						this.creditoFc.setValue(credito.codigo);
						this.creditoEscolhido = credito;
					}
				});
			}
			this.creditoFc.valueChanges.subscribe(() => {
				this.creditoSelecionado();
				if (!this.carregamentoInicial) {
					this.simular();
				}
				this.cd.detectChanges();
			});
		}
	}

	prepararCondicoes(simular: boolean) {
		if (this.duracaoFc.value) {
			let nrMeses = 1;
			this.condicaoFc = new FormControl();
			this.negociacao.duracoes.forEach((duracao) => {
				// tslint:disable-next-line:radix
				if (duracao.codigo === parseInt(this.duracaoFc.value)) {
					nrMeses = duracao.nrMeses;
				}
				// tslint:disable-next-line:radix
				if (duracao.codigo === parseInt(this.duracaoFc.value)) {
					this.condicoes = duracao.condicoes;
					this.setarUltimaCondicao(nrMeses, simular);
					if (!this.condicaoFc.value) {
						nrMeses = 1;
						this.setarUltimaCondicao(nrMeses, simular);
					}
					this.cd.detectChanges();
				}
			});
			this.condicaoFc.valueChanges.subscribe(() => {
				this.negociacaoState.getConfigsContrato().arredondamento = null;
				if (!this.carregamentoInicial) {
					this.simular();
				}
				this.cd.detectChanges();
			});
		}
	}

	private setarUltimaCondicao(nrMeses: number, simular: boolean) {
		if (localStorage.getItem("condicao" + this.planoFc.value)) {
			this.condicaoFc.setValue(
				localStorage.getItem("condicao" + this.planoFc.value)
			);
			if (simular) {
				if (!this.carregamentoInicial) {
					this.simular();
				}
			}
		} else {
			this.condicoes.forEach((condicao) => {
				if (!this.condicaoFc.value && condicao.nrParcelas === nrMeses) {
					this.condicaoFc.setValue(condicao.codigo);
					if (simular) {
						if (!this.carregamentoInicial) {
							this.simular();
						}
					}
				}
			});
		}
	}

	public simular() {
		this.montarConfigs();
		this.negociacaoState.simular().subscribe(
			() => {
				this.cd.detectChanges();
			},
			(error) => {
				if (typeof error === "string" && error.includes("excedem")) {
					this.descontoNegociacaoComponent.descontoExcedido(error);
				}
			}
		);
		this.negociacaoState.getSimulado().subscribe((data) => {
			this.simulado = data;
			if (
				this.primeiraSimulacao === undefined &&
				Object.keys(this.simulado).length > 0
			) {
				this.primeiraSimulacao = { ...data };
			}
			if (
				this.parcelasEditadas &&
				this.parcelasEditadas.length > 0 &&
				this.validarEdicaoParcelas()
			) {
				this.simulado.parcelas = this.parcelasEditadas;
			}
			if (
				this.simulado &&
				this.simulado.diasCartao &&
				this.simulado.diasCartao > 0 &&
				this.negociacao &&
				this.negociacao.diasCartao &&
				this.negociacao.diasCartao.length > 0
			) {
				this.diaCartaoFc = new FormControl(this.simulado.diasCartao);
				this.diaCartaoFc.valueChanges.subscribe(() => {
					if (!this.carregamentoInicial) {
						this.simular();
					}
				});
			}
			if (
				this.diaCartaoFc &&
				this.diaCartaoFc.value &&
				this.diaCartaoFc.value === 999 &&
				this.simulado.inicio
			) {
				const inicioDate = new Date(this.simulado.inicio);
				const today = new Date();
				const todayDayOfMonth = today.getDate();
				if (todayDayOfMonth !== inicioDate.getDate()) {
					this.diaCartaoFc = new FormControl(inicioDate.getDate());
					this.diaCartaoFc.valueChanges.subscribe(() => {
						if (!this.carregamentoInicial) {
							this.simular();
						}
					});
				}
			}
			if (this.parcelas) {
				this.parcelas.dividirParcelasApresentar(this.simulado.parcelas);
			}
			// if (this.simulado && this.simulado.questionarioBvCliente && this.simulado.questionarioBvCliente.codigo && !this.atualizouBV) {
			// 	this.preencherBv();
			// }
			this.cd.detectChanges();
		});
	}

	private montarConfigs() {
		this.negociacaoState.getConfigsContrato().plano = this.planoFc.value;
		if (this.codigoConvenioCobranca) {
			this.negociacaoState.getConfigsContrato().codigoConvenio =
				this.codigoConvenioCobranca;
		} else if (
			this.autorizacaoCobrancaCliente &&
			this.autorizacaoCobrancaCliente.convenioCobranca
		) {
			this.negociacaoState.getConfigsContrato().codigoConvenio =
				this.autorizacaoCobrancaCliente.convenioCobranca;
		}
		// console.log(this.negociacaoState.getConfigsContrato().codigoConvenio);

		this.negociacaoState.getConfigsContrato().duracao = this.duracaoFc.value;
		this.negociacaoState.getConfigsContrato().horario = this.horarioFc.value;
		if (
			this.bolsa === true &&
			!this.condicaoFc.value &&
			this.condicoes.length > 0
		) {
			this.negociacaoState.getConfigsContrato().condicao =
				this.condicoes[0].codigo;
		} else {
			this.negociacaoState.getConfigsContrato().condicao =
				this.condicaoFc.value;
		}
		this.negociacaoState.getConfigsContrato().cliente = this.cliente
			? this.cliente.codigo
			: null;
		this.negociacaoState.getConfigsContrato().cliente = this.cliente
			? this.cliente.codigo
			: null;
		this.negociacaoState.getConfigsContrato().vencimentoCartao =
			this.diaCartaoFc.value;
		this.negociacaoState.getConfigsContrato().credito = this.creditoEscolhido;

		if (!this.negociacaoState.getConfigsContrato().configuracoesAvancadas) {
			this.negociacaoState.getConfigsContrato().configuracoesAvancadas = {
				cobrarMatricula: this.negociacao
					? this.negociacao.cobrarAdesaoSeparada
					: false,
				vezesCobrarMatricula: 1,
				vezesCobrarProdutosSeparados: 1,
				diaPrimeiraParcela: null,
				dividirProdutoParcela: false,
				escolherDiaPrimeiraParcela: null,
				escolherDiaProrata: null,
				diaProrata: null,
				cobrarProdutosSeparados: this.negociacao
					? this.negociacao.cobrarProdutoSeparado
					: false,
			};
		}
	}

	creditoSelecionado() {
		if (this.negociacao.vendaCreditoTreino === true && this.creditoFc.value) {
			this.negociacao.duracoes.forEach((duracao) => {
				if (duracao.codigo === parseInt(this.duracaoFc.value, 10)) {
					duracao.creditos.forEach((credito) => {
						if (credito.codigo === parseInt(this.creditoFc.value, 10)) {
							this.creditoEscolhido = credito;
						}
					});
				}
			});
		}
	}

	voltarHome(): void {
		window.history.back();
	}

	abrirAntigaTelaClienteZW() {
		this.loaderService.initForce();
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				const url = `${urlZw}&urlRedirect=uriCliente&matriculaCliente=${this.cliente.matricula}&menu=true`;
				window.open(url, "_self");
			});
	}

	isElementVisible(element: HTMLElement): boolean {
		const rect = element.getBoundingClientRect();
		const windowHeight =
			window.innerHeight || document.documentElement.clientHeight;
		const windowWidth =
			window.innerWidth || document.documentElement.clientWidth;

		return (
			rect.top >= 0 &&
			rect.left >= 0 &&
			rect.bottom <= windowHeight &&
			rect.right <= windowWidth
		);
	}

	onScroll(event): void {
		this.outClick();
		if (this.fluxo) {
			const div1Visible = this.isElementVisible(this.fluxo.nativeElement);
			if (div1Visible) {
				this.renderer.addClass(this.fixo.nativeElement, "hidden");
			} else {
				this.renderer.removeClass(this.fixo.nativeElement, "hidden");
			}
		}
	}

	outClick() {
		const elementsByClassName =
			document.getElementsByClassName("simule-clique");
		const array = Array.from(elementsByClassName);
		for (const save of array) {
			save.dispatchEvent(
				new MouseEvent("click", {
					bubbles: true,
					cancelable: true,
					view: window,
				})
			);
		}
	}

	abrirAvisoEdicaoParcelas(acao) {
		this.parcelasEditadas = null;
		this.negociacaoState.getConfigsContrato().parcelas = null;
		const modal = this.modal.open(
			"Atenção: Negociação em andamento!",
			ModalAvisoEdicaoParcelasComponent,
			PactoModalSize.LARGE,
			"modal-aviso-editar-parcelas"
		);
		modal.result.then(
			(escolha) => {
				switch (escolha) {
					case "editar-parcelas":
						this.abrirEditarParcelas();
						break;
					case "finalizar":
						this.abrirAutorizacao(acao);
						break;
				}
			},
			() => {}
		);
	}

	validarConvenioAutorizacao(): boolean {
		if (
			(this.negociacao && this.negociacao.fecharNegociacaoSemAutorizacaoDCC) ||
			this.cadastrouCartao
		) {
			return true;
		}
		let temCondicaoConvenio: boolean = false;
		this.condicoes.forEach((condicao) => {
			// tslint:disable-next-line:radix
			if (
				parseInt(this.negociacaoState.getConfigsContrato().condicao) ===
				condicao.codigo
			) {
				temCondicaoConvenio = condicao.tipoConvenio > 0;
			}
		});
		if (!temCondicaoConvenio) {
			return true;
		}
		if (this.permissaoService.temPermissaoAdm("2.74")) {
			return true;
		}
		return false;
	}

	validarCondicaoPagamentoGravadaAutomaticoPlanoRecorrente(): boolean {
		let temGravarCondicaoPlanoRecorrente: boolean = false;
		if (this.negociacao != null && this.negociacao.recorrencia) {
			this.condicoes.forEach((condicao) => {
				if (
					condicao.geradoAutomaticoPlanoRecorrente &&
					condicao.tipoConvenio !== undefined &&
					condicao.tipoConvenio !== null &&
					condicao.tipoConvenio === 0
				) {
					temGravarCondicaoPlanoRecorrente = true;
				}
			});
		}
		return temGravarCondicaoPlanoRecorrente;
	}

	validarEdicaoParcelas(): boolean {
		if (!this.parcelasEditadas || this.parcelasEditadas.length === 0) {
			return true;
		}
		if (
			!this.simulado ||
			!this.simulado.parcelas ||
			this.simulado.parcelas.length === 0
		) {
			return true;
		}
		if (this.simulado.parcelas.length !== this.parcelasEditadas.length) {
			return false;
		}
		let valorParcelasSimuladas = 0.0;
		let valorParcelasEditadas = 0.0;
		this.simulado.parcelas.forEach((parcelaSimulada) => {
			valorParcelasSimuladas += parcelaSimulada.valorParcela;
		});
		this.parcelasEditadas.forEach((parcelaEditada) => {
			valorParcelasEditadas += parcelaEditada.valorParcela;
		});
		return valorParcelasSimuladas === valorParcelasEditadas;
	}

	abrirAutorizacao(acao, assinar = false): void {
		if (this.isIntegradoMgb && !this.isNiveisMgbIguais()) {
			this.notificationService.error(
				"A integração com o MGB está ativa, obrigatoriamente os horários selecionados devem possuir o mesmo nível vinculado ao nível MGB"
			);
			return;
		}
		if (this.validarEdicaoParcelas() === false) {
			this.abrirAvisoEdicaoParcelas(acao);
			return;
		}
		if (this.validarConvenioAutorizacao() === false) {
			this.notificationService.warning(
				"Informe uma autorização de cobrança para finalizar a negociação."
			);
			this.autorizacaoCobranca();
			return;
		}
		if (
			this.validarCondicaoPagamentoGravadaAutomaticoPlanoRecorrente() === true
		) {
			this.notificationService.warning(
				"Como o plano é Recorrente, deve ir na Condição de Pagamento e configurar o Tipo Convênio para permitir vender sem preencher a Autorização de Cobrança."
			);
			this.autorizacaoCobranca();
			return;
		}
		const validado = this.validar();
		if (validado === false) {
			return;
		}
		if (!this.cliente || !this.cliente.matricula) {
			this.abrirEscolherCliente(acao);
			return;
		}
		this.pessoaService
			.confereDadosObrigatorios(this.cliente.codigo)
			.subscribe((listaCamposFaltantes) => {
				if (listaCamposFaltantes && listaCamposFaltantes.length > 0) {
					this.openModalObrigatorio(listaCamposFaltantes, acao, assinar);
					return;
				} else {
					this.autorizarAcao(acao, assinar);
				}
			});
	}

	private isNiveisMgbIguais() {
		try {
			if (this.negociacaoState.getConfigsContrato().modalidades) {
				let primeiroNivelMgb = "";
				for (const modalidade of this.negociacaoState.getConfigsContrato()
					.modalidades) {
					for (const horario of modalidade.turmas) {
						if (primeiroNivelMgb === "") {
							primeiroNivelMgb = horario.nivelCodigoMgb;
						}
						if (
							horario.nivelCodigoMgb !== "" &&
							primeiroNivelMgb !== "" &&
							primeiroNivelMgb !== horario.nivelCodigoMgb
						) {
							return false;
						}
					}
				}
			}
			return true;
		} catch (e) {
			console.error(e);
			return true;
		}
	}

	private autorizarAcao(acao, assinar: boolean) {
		if (this.sessionService.loggedUser.pedirSenhaFuncionalidade) {
			const modalConfirmacao: any = this.matDialog.open(
				DialogAutorizacaoAcessoComponent,
				{
					disableClose: true,
					id: "autorizacao-acesso",
					autoFocus: false,
				}
			);
			modalConfirmacao.componentInstance.form
				.get("usuario")
				.setValue(this.sessionService.loggedUser.username);
			modalConfirmacao.componentInstance.confirm.subscribe((result) => {
				this.autorizarAcessoService
					.validarPermissao(
						this.sessionService.chave,
						result.data.usuario,
						result.data.senha,
						"FecharReceberContrato",
						"2.69 Permite Fechar e Receber Contrato",
						this.cliente.empresa.toString()
					)
					.subscribe(
						(response: any) => {
							this.negociacaoState.getConfigsContrato().usuario =
								response.content;
							result.modal.close();
							switch (acao) {
								case "receber":
									this.receber();
									break;
								case "caixa":
									this.irProCaixa(assinar);
									break;
								case "enviar":
									this.enviarLink();
									break;
							}
						},
						(error) => {
							console.log(error);
							this.notificationService.error(error.error.meta.message);
						}
					);
			});
		} else {
			this.blockButtons = true;
			this.autorizarAcessoService
				.validarPermissaoUsuarioLogado(
					this.sessionService.chave,
					this.sessionService.loggedUser.id,
					this.sessionService.empresaId,
					"FecharReceberContrato",
					"2.69 Permite Fechar e Receber Contrato"
				)
				.subscribe(
					(response: any) => {
						this.negociacaoState.getConfigsContrato().usuario =
							response.content;
						switch (acao) {
							case "receber":
								this.receber();
								break;
							case "caixa":
								this.irProCaixa(assinar);
								break;
							case "enviar":
								this.enviarLink();
								break;
						}
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
						this.blockButtons = false;
					}
				);
		}
	}

	receber(): void {
		this.salvar(true, false);
	}

	openModalObrigatorio(data, acao, assinar: boolean): void {
		const modalRef = this.modal.open(
			"Dados obrigatórios pendente!",
			ModalDadosObrigatoriosComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.data = data;
		modalRef.componentInstance.idPessoa = this.cliente.codigo;
		modalRef.result.then(() => {
			this.autorizarAcao(acao, assinar);
		});
	}

	irProCaixa(assinar: boolean): void {
		this.salvar(false, assinar);
	}

	validar(): boolean {
		if (
			(!this.negociacaoState.getConfigsContrato().modalidades ||
				this.negociacaoState.getConfigsContrato().modalidades.length === 0) &&
			(!this.negociacaoState.getConfigsContrato().pacotes ||
				this.negociacaoState.getConfigsContrato().pacotes.length === 0)
		) {
			this.notificationService.warning("Adicione pelo menos uma modalidade.", {
				position: "rightTop",
			});
			return false;
		}
		let temModalidadeSemTurma = false;
		if (
			this.negociacao.vendaCreditoTreino === true &&
			this.negociacaoState.getConfigsContrato().horario
		) {
			this.negociacao.duracoes.forEach((duracao) => {
				// tslint:disable-next-line:radix
				if (duracao.codigo === parseInt(this.duracaoFc.value)) {
					this.negociacao.horarios.forEach((horario) => {
						// tslint:disable-next-line:radix
						if (horario.codigo === parseInt(this.horarioFc.value)) {
							if (horario.livre === true) {
								return true;
							}
						}
					});
				}
			});
		}

		this.negociacaoState
			.getConfigsContrato()
			.modalidades.forEach((modalidade) => {
				if (
					modalidade.utilizarTurma === true &&
					(!modalidade.aulaColetivaFixa ||
						modalidade.aulaColetivaFixa === false) &&
					(!modalidade.turmas ||
						(this.vendaCreditoTreino && this.negociacao.creditoSessao === false
							? this.nrVezesCreditoTreino
							: modalidade.nrvezes) !== modalidade.turmas.length)
				) {
					this.notificationService.warning(
						`Escolha todos os horários de turma para a modalidade ${modalidade.descricao}.`,
						{
							position: "rightTop",
						}
					);
					temModalidadeSemTurma = true;
				}
			});
		if (temModalidadeSemTurma) {
			return false;
		}
		return true;
	}

	get vendaCreditoTreino(): boolean {
		return this.negociacao && this.negociacao.vendaCreditoTreino === true;
	}

	get nrVezesCreditoTreino(): number {
		return this.negociacaoState.getConfigsContrato() &&
			this.negociacaoState.getConfigsContrato().credito
			? this.negociacaoState.getConfigsContrato().credito.numeroVezesSemana
			: 0;
	}

	enviarLink(): void {
		this.loaderService.initForce();
		this.negociacaoState.getConfigsContrato().gerarLink = true;
		this.negociacaoState.getConfigsContrato().alterouDataInicioContrato =
			this.alterouDataInicioContrato;
		this.montarConfigs();
		this.negociacaoState.gravar().subscribe(
			(data) => {
				this.loaderService.stopForce();
				if (data.error) {
					this.notificationService.error(data.error, { position: "rightTop" });
					return;
				}
				this.notificationService.success("Contrato lançado com sucesso!", {
					position: "rightTop",
				});
				this.notificar("NEGOCIACAO_ENVIAR_LINK");
				this.abrirEnviarLink(data);
				this.cd.detectChanges();
			},
			(httpErrorResponse) => {
				this.blockButtons = false;
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue, {
						position: "rightTop",
					});
				} else {
					this.notificationService.error(
						"Ocorreu um erro inesperado, tente novamente.",
						{
							position: "rightTop",
						}
					);
				}
			}
		);
	}

	salvar(receber: boolean, assinar: boolean): void {
		if (
			!this._obrigatorioPreencherBv() &&
			this.questionarioCliente &&
			this.questionarioCliente.preencherQuestionario &&
			!this.atualizouBV
		) {
			this._cadastrarBv(receber, assinar);
		} else {
			this._salvarNegociacao(receber, assinar);
		}
	}

	private _salvarNegociacao(receber: boolean, assinar: boolean): void {
		console.log(assinar);
		console.log("entrei no salvarNegociacaoCacetaDoida");
		this.loaderService.initForce();
		this.negociacaoState.getConfigsContrato().gerarLink = false;
		this.negociacaoState.getConfigsContrato().alterouDataInicioContrato =
			this.alterouDataInicioContrato;
		this.montarConfigs();
		this.negociacaoState.gravar().subscribe(
			(data) => {
				if (data.error) {
					console.log("entrou no error");
					this.notificationService.error(data.error);
					if (data.error.includes("MSG-5737")) {
						this._openModalObrigatorioAutorizacaoCobranca();
					}
					this.loaderService.stopForce();
					return;
				}
				console.log("passou o error");
				this.loaderService.initForce();
				const id = data.contrato;
				this.notificationService.success("Contrato lançado com sucesso!", {
					position: "rightTop",
				});
				this.notificar(
					receber === true ? "NEGOCIACAO_RECEBER" : "NEGOCIACAO_CAIXA_ABERTO"
				);
				if (this.bolsa === true) {
					this.resetAll(true);
					this.loaderService.stopForce();
					this.blockButtons = false;
					this.cd.detectChanges();
					this.router.navigate(["adm", "negociacao", "contrato"]);
					return;
				}
				this.cd.detectChanges();
				if (!id) {
					this.notificationService.warning(
						"Contrato salvo, mas não foi retornado o código. Você será redirecionado para o caixa em aberto do cliente!"
					);
				}

				if (assinar) {
					this.obterLinkAcesso(id);
				} else {
					this.service.recursoHabilitado("CAIXA_ABERTO").subscribe({
						next: (responseV) => {
							if (responseV) {
								this.acessarCaixaEmAbertoNovo(receber, id);
							} else {
								this.acessarCaixaEmAbertoAntigo(receber, id);
							}
						},
						error: (err) => {
							this.acessarCaixaEmAbertoAntigo(receber, id);
						},
					});
				}
			},
			(httpErrorResponse) => {
				this.loaderService.stopForce();
				this.blockButtons = false;
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				} else {
					this.notificationService.error(
						"Ocorreu um erro inesperado, tente novamente."
					);
				}
			}
		);
	}

	acessarCaixaEmAbertoNovo(receber, id) {
		this.loaderService.stopForce();
		if (receber === true) {
			if (
				this.configEmpresa.habilitarCobrancaAutomaticaNaVenda &&
				this.negociacaoState.getConfigsContrato() &&
				this.negociacaoState.getConfigsContrato().codigoConvenio
			) {
				this.router.navigateByUrl(
					"/adm/caixa-em-aberto/lista/" + this.cliente.nome
				);
			} else {
				this.router.navigateByUrl(
					"/adm/caixa-em-aberto/receber-parcelas/pagamentoContrato_" + id
				);
			}
		} else {
			if (this.cliente.nome) {
				this.router.navigateByUrl(
					"/adm/caixa-em-aberto/lista/" + this.cliente.nome
				);
			} else {
				this.router.navigateByUrl("/adm/caixa-em-aberto");
			}
		}
	}

	acessarCaixaEmAbertoAntigo(receber, id) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				if (receber === true) {
					let redirectUrl = "";
					if (
						this.configEmpresa.habilitarCobrancaAutomaticaNaVenda &&
						this.negociacaoState.getConfigsContrato() &&
						this.negociacaoState.getConfigsContrato().codigoConvenio
					) {
						redirectUrl = `&urlRedirect=uriCliente&matriculaCliente=${this.cliente.matricula}&menu=true`;
					} else {
						redirectUrl = "&urlRedirect=pagamentoContrato_" + id;
					}
					window.open(result + redirectUrl, "_self");
				} else {
					window.open(
						result + "&urlRedirect=caixaEmAberto_" + this.cliente.nome,
						"_self"
					);
				}
			});
	}

	obterLinkAcesso(id: any) {
		try {
			this.linkAcessoRapidoService
				.obterLinkAppsPacto(
					this.configuracao.key,
					this.configuracao.empresa,
					this.configuracao.codigoUsuarioZw.toString(),
					"assinaturadigital"
				)
				.subscribe((response: any) => {
					let sufix = "";
					let linkAcesso = "";

					if (!response.content.linkAcesso.startsWith("http")) {
						sufix =
							this.configuracao.pathUrlZw ||
							this.pactoLayoutSDKWrapper.serviceUrls().zwUrl;
					}

					linkAcesso = sufix + response.content.linkAcesso + "&contrato=" + id;

					window.open(linkAcesso, "_blank");

					this.loaderService.stopForce();

					// 1. Navega para a rota (se não estiver nela ainda)
					this.router
						.navigate([
							"pessoas",
							"perfil-v2",
							this.cliente.matricula,
							"contratos",
						])
						.then(() => {
							// 2. Recarrega a rota (força reexecutar o componente)
							this.router
								.navigateByUrl("/", { skipLocationChange: true })
								.then(() => {
									this.router.navigate([
										"pessoas",
										"perfil-v2",
										this.cliente.matricula,
										"contratos",
									]);
								});
						});
				});
		} catch (e) {
			console.log(e);
		}
	}

	private _cadastrarBv(receber: boolean, assinar: boolean) {
		const rematricula = !!(
			this.check.codigoContratoRematricula &&
			this.check.codigoContratoRematricula > 0
		);
		this.questionarioClienteService
			.cadastrarBv(this.questionarioCliente, rematricula)
			.subscribe(
				(response) => {
					this._salvarNegociacao(receber, assinar);
				},
				(httpResponseError) => {
					if (httpResponseError.error) {
						if (httpResponseError.error.meta) {
							if (httpResponseError.error.meta.message) {
								this.notificationService.error(
									httpResponseError.error.meta.message
								);
							}
						}
					}
				}
			);
	}

	convertToFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	removerPlano() {
		this.planoSelecionado = false;
		this.cd.detectChanges();
	}

	arredondarValores() {
		this.negociacaoState.converterQtd();
		const configsContrato = this.negociacaoState.getConfigsContrato();
		configsContrato.arredondar = true;
		this.service.simular(configsContrato).subscribe((contrato) => {
			this.abrirValoresArredondar(
				contrato.parcelas.length,
				contrato.valoresArredondados
			);
		});
	}

	abrirObservacao() {
		const modal = this.modal.open(
			"Adicionar observação",
			ModalObservacaoComponent,
			PactoModalSize.MEDIUM
		);
		if (this.negociacaoState.getConfigsContrato().observacao) {
			modal.componentInstance.obsFC.setValue(
				this.negociacaoState.getConfigsContrato().observacao
			);
		}
		modal.result.then(
			(obs) => {
				this.negociacaoState.getConfigsContrato().observacao = obs;
			},
			() => {}
		);
	}

	abrirOpcoesAvancadas() {
		const modal = this.modal.open(
			"Configurações avançadas",
			ModalOpcoesAvancadasComponent,
			PactoModalSize.MEDIUM,
			"modal-opcoes-avancadas"
		);
		modal.componentInstance.negociacao = this.negociacao;
		modal.result.then(
			(dados) => {
				this.negociacaoState.getConfigsContrato().configuracoesAvancadas =
					dados;
				this.simular();
			},
			() => {}
		);
	}

	abrirEditarParcelas() {
		const modal = this.modal.open(
			"Editar parcelas",
			ModalEditarParcelasComponent,
			PactoModalSize.LARGE,
			"modal-editar-parcelas"
		);
		if (
			this.parcelasEditadas &&
			this.parcelasEditadas.length > 0 &&
			this.validarEdicaoParcelas()
		) {
			modal.componentInstance.setup(this.parcelasEditadas);
		} else {
			modal.componentInstance.setup(this.simulado.parcelas);
		}
		modal.result.then(
			(form) => {
				if (this.simulado && this.simulado.parcelas) {
					this.simulado.parcelas.forEach((parcela) => {
						if (parcela.descricao.includes("Parcela")) {
							parcela.valorParcela = form.get(parcela.descricao)
								? form.get(parcela.descricao).value
								: 0.0;
						}
					});
					this.parcelasEditadas = this.simulado.parcelas;
					this.negociacaoState.getConfigsContrato().parcelas =
						this.simulado.parcelas;
					this.parcelas.dividirParcelasApresentar(this.simulado.parcelas);
					this.cd.detectChanges();
				}
			},
			() => {}
		);
	}

	abrirEscolherCliente(acao) {
		this.carregamentoInicial = false;
		this.negociacaoState.getConfigsContrato().contratoRenovar = null;
		this.negociacaoState.getConfigsContrato().contratoBase = null;
		const modal = this.modal.open(
			"Selecione o cliente",
			ModalEscolhaClienteNegociacaoComponent,
			PactoModalSize.LARGE
		);
		modal.result.then(
			(cli) => {
				if (acao === "cobranca" && cli && cli.codigo && cli.codigo > 0) {
					this.cliente = cli;
					this.negociacaoState.updateCliente(cli);
					this.cobrancaCliente();
					return;
				}
				this.selectCliente(cli, acao);
			},
			() => {
				if (acao === "cobranca") {
					return;
				}
				this.service.check(0, "0").subscribe((check) => {
					this.check = check;
					this.usaProdutos = check.usaProdutos;
					this.usaDesconto = check.usaDesconto;
					this.aplicarPlanoSugerido(check.planoSugerido);
				});
			}
		);
	}

	filtrarPlanosPorCategoria(categoria: number) {
		if (!categoria || categoria === 0) {
			this.planos = this.planosOriginais.filter(
				(plano) => plano.restringeVendaPorCategoria === false
			);
			this.cd.detectChanges();
			return;
		}
		if (this.planosOriginais) {
			this.planos = this.planosOriginais.filter(
				(plano) =>
					plano.restringeVendaPorCategoria === false ||
					(plano.categorias && plano.categorias.includes(categoria))
			);
			this.cd.detectChanges();
		}
	}

	private selectCliente(cli, acao) {
		this.cliente = cli;
		this.negociacaoState.updateCliente(cli);
		if (this.cliente && this.cliente.codigo) {
			this.consultarPlanos(
				0,
				this.dataLancamentoContratoSelecionada,
				this.linha
			);
		} else {
			console.error("Erro: Código do Cliente ainda não definido!");
		}
		const contratoPreviamenteEscolhido =
			this.linha && this.linha !== ""
				? this.linha.replace("rematricular_", "").replace("renovar_", "")
				: "0";
		this.service
			.check(cli.codigo, contratoPreviamenteEscolhido, true)
			.subscribe((check) => {
				setTimeout(() => {
					this.filtrarPlanosPorCategoria(cli.codigoCategoria);
				}, 200);
				this.check = check;
				if (
					this.utilizarGestaoClienteComRestricao &&
					check.clienteRestricoes &&
					check.clienteRestricoes.length > 0
				) {
					this.openModalAvisoClienteRestricao(check.clienteRestricoes);
					return;
				}
				if (check.tipoAutorizacaoCobranca) {
					this.cadastrouCartao = true;
					console.log(check);
					this.autorizacaoCobrancaCliente = {
						tipoAutorizacaoCobranca: check.tipoAutorizacaoCobranca,
						nrsCartao: check.nrsCartao,
						validadeCartao: check.validadeCartao,
						convenioCobranca: check.convenioCobranca,
					};
				} else {
					this.cadastrouCartao = false;
					this.autorizacaoCobrancaCliente = null;
				}
				this.usaProdutos = check.usaProdutos;
				this.usaDesconto = check.usaDesconto;
				cli.nivelAluno = check.nivelAluno;
				this.aplicarPlanoSugerido(check.planoSugerido);
				if (
					check.codigoContratoRematricula &&
					check.codigoContratoRematricula > 0 &&
					!this.linha
				) {
					this.simular();
					this.negociacaoState.updateCliente(cli);
					this.abrirModalRematricula(check, cli, acao);
				} else if (
					check.codigoContratoRenovacao &&
					check.codigoContratoRenovacao > 0 &&
					!this.linha
				) {
					this.simular();
					this.negociacaoState.updateCliente(cli);
					this.abrirModalRenovar(check, cli, acao);
				} else {
					const resetar: boolean =
						this.cliente && this.cliente.codigo && this.cliente.codigo > 0;
					this.cliente = cli;
					this.negociacaoState.updateCliente(cli);
					if (resetar && !this.linha) {
						this.reset();
					}
					this.parcelasAberto = check.valorParcelasAberto;
					if (
						check.codigoContratoRematricula &&
						check.codigoContratoRematricula > 0
					) {
						this.negociacaoState.getConfigsContrato().contratoBase =
							check.codigoContratoRematricula;
					}
					if (this.linha && this.linha.startsWith("rematricular")) {
						this.negociacaoState.getConfigsContrato().contratoBase =
							this.linha.replace("rematricular_", "");
						this.selectPlanoTipo("RE", this.linha.replace("rematricular_", ""));
					} else if (this.linha && this.linha.startsWith("renovar")) {
						this.aplicarPlanoRenovacao(this.check.planoRenovacao, false);
						this.selectPlanoTipo("RN", this.linha.replace("renovar_", ""));
						this.negociacaoState.getConfigsContrato().contratoBase =
							this.linha.replace("renovar_", "");
					} else {
						this.simular();
					}
					this.loadBvCliente(cli.codigo);
					this.cd.detectChanges();
				}
				this.clienteSelecionado$.next(); // notifica que terminou
			});
	}

	aplicarPlanoSugerido(planoSugerido: number) {
		if (
			!planoSugerido ||
			planoSugerido === 0 ||
			(this.planoFc && this.planoFc.value && this.planoFc.value > 0)
		) {
			return;
		}
		if (this.linha === "novo") {
			this.planoFc.setValue(planoSugerido);
		} else {
			this.planoFc = new FormControl(planoSugerido);
			if (!this.carregamentoInicial) {
				this.selectPlano();
			}
			this.planoFc.valueChanges.subscribe(() => {
				this.selectPlano();
			});
		}
	}

	abrirEnviarLink(data) {
		const modal = this.modal.open(
			"Compartilhar link de pagamento",
			ModalEnviarLinkPagamentoComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.config = data;
		modal.result.then(
			(obs) => {},
			() => {
				this.resetAll();
			}
		);
	}

	private resetAll(manterCliente?) {
		this.cliente = {};
		this.negociacaoState.getConfigsContrato().observacao = null;
		this.negociacaoState.getConfigsContrato().contratoBase = null;
		this.check = null;
		this.negociacaoState.updateCliente({});
		this.parcelasAberto = 0.0;
		this.reset();
		if (!manterCliente) {
			this.abrirEscolherCliente(null);
		}
	}

	autorizacaoCobranca() {
		if (!this.cliente || !this.cliente.matricula) {
			this.abrirEscolherCliente("cobranca");
			return;
		}
		this.cobrancaCliente();
	}

	private cobrancaCliente() {
		this.msPactopayApiConvenioCobranca
			.obterTiposDeAutorizacao(
				Number(this.sessionService.empresaId),
				Number(0),
				Number(1)
			)
			.subscribe((res) => {
				const todosFalse = Object.entries(res.content)
					.filter(([key]) => key !== "pix")
					.every(([_, value]) => value === false);

				if (todosFalse) {
					this.abrirModalNenhumConvenio();
				} else {
					const modal = this.modal.open(
						"Autorização de cobrança",
						ModalAutorizacaoCobrancaComponent,
						PactoModalSize.LARGE,
						"modal-editar-parcelas"
					);
					modal.componentInstance.editar = false;
					modal.componentInstance.pessoa = this.cliente.pessoa;
					modal.componentInstance.idVindi = this.cliente.idVindi;
					modal.componentInstance.possuiIdVindi = this.cliente.possuiIdVindi;

					modal.componentInstance.sendModificacao.subscribe((modificacao) => {
						if (modificacao.status === "Salvo com sucesso!") {
							if (
								modificacao.codigoConvenio !== null &&
								modificacao.codigoConvenio !== undefined
							) {
								this.codigoConvenioCobranca = modificacao.codigoConvenio;
							}
						}
					});

					modal.result.then((result) => {
						this.cadastrouCartao = result === "salvo";
						this.cd.detectChanges();
					});
				}
			});
	}

	private reset() {
		if (this.addDescontoComponent) {
			this.addDescontoComponent.reset();
		}
		this.negociacaoState.getConfigsContrato().produtos = [];
		this.selectPlano();
	}

	get btnReceber(): boolean {
		if (!this.negociacao || this.negociacao.recorrencia === false) {
			return true;
		}
		if (
			this.simulado &&
			this.simulado.parcelas &&
			this.simulado.parcelas.length > 0
		) {
			return this.simulado.parcelas.some(
				(parcela) => parcela.nrParcela === 1 && parcela.valorParcela > 0.0
			);
		}
		return true;
	}

	private notificar(string: string) {
		try {
			this.sessionService.notificarRecursoEmpresa(string);
		} catch (e) {
			console.error(e);
		}
	}

	get dataLancamento(): string {
		return this.simulado && this.simulado.dataLancamento
			? this.simulado.dataLancamento.split(" - ")[0]
			: "-";
	}

	get tipoContrato() {
		const tipos = this.negociacaoState.getConfigsContrato().tipoContrato
			? this.negociacaoState.getConfigsContrato().tipoContrato
			: "ESPONTANEO";
		return this.xingling.getLabel(tipos);
	}

	get descricao(): string {
		return this.negociacaoState.getConfigsContrato()["descricao"] || "";
	}

	autorizarDataBase(): void {
		this.autorizar("lancamento", "DataBase", "3.06 - Liberação de Data Base");
	}

	autorizarTipo(): void {
		this.autorizar(
			"tipo",
			"Alterar_Contrato_AgendadoEspontaneo_Autorizar",
			"3.20 - Alterar Contrato Agendado/Espontâneo"
		);
	}

	public abrirValoresArredondar(parcelas, valoresArredondados) {
		const modal = this.modal.open(
			"Valores sugeridos",
			ModalValoresArrendondarComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.valoresArredondados = valoresArredondados;
		modal.componentInstance.parcelas = parcelas === 1 ? 1 : parcelas - 1;
		modal.result.then(
			(v) => {
				this.negociacaoState.getConfigsContrato().arredondamento = v;
				this.simular();
			},
			() => {}
		);
	}

	abrirDataLancamento() {
		if (
			!this.negociacaoState.usuarioDataBase &&
			this.permissaoService.temPermissaoAdm("3.06")
		) {
			this.negociacaoState.usuarioDataBase = this.sessionService.loggedUser.id;
		}
		if (
			!this.negociacaoState.usuarioTipo &&
			this.permissaoService.temPermissaoAdm("3.20")
		) {
			this.negociacaoState.usuarioTipo = this.sessionService.loggedUser.id;
		}
		const modal = this.modal.open(
			"Configurações de contrato",
			ModalDataLancamentoComponent,
			PactoModalSize.MEDIUM
		);
		if (this.negociacaoState.getConfigsContrato().dataLancamento) {
			modal.componentInstance.lancamento.setValue(
				Util.fromTimestampToUTC(
					this.negociacaoState.getConfigsContrato().dataLancamento
				)
			);
		} else {
			modal.componentInstance.lancamento.setValue(new Date().getTime());
		}
		if (this.negociacaoState.getConfigsContrato().tipoContrato) {
			modal.componentInstance.tipoFc.setValue(
				this.negociacaoState.getConfigsContrato().tipoContrato
			);
		}
		modal.componentInstance.dataLimiteRematricula =
			this.check && this.negociacaoState.getConfigsContrato().contratoBase
				? this.check.limiteDataRematricula
				: null;
		modal.componentInstance.inicioOriginal = Util.fromTimestampToUTC(
			this.primeiraSimulacao.inicio
		);
		modal.componentInstance.alterouDataInicioContrato =
			this.alterouDataInicioContrato;
		modal.componentInstance.inicio.setValue(
			Util.fromTimestampToUTC(this.simulado.inicio)
		);
		modal.componentInstance.fim.setValue(
			Util.fromTimestampToUTC(this.simulado.fim)
		);
		modal.componentInstance.fim.disable();
		modal.componentInstance.tipoVenda = this.tipoVenda;
		modal.componentInstance.descricao.setValue(this.descricao);
		modal.result.then(
			(v) => {
				if (v === "autorizarDataBase") {
					this.autorizarDataBase();
				}
				if (v === "tipo") {
					this.autorizarTipo();
				}

				if (v && typeof v.dataLancamentoContrato === "number") {
					this.consultarPlanos(0, v.dataLancamentoContrato, this.linha);
				}
				if (v.simulado) {
					this.primeiraSimulacao = { ...v.simulado };
				}
				this.alterouDataInicioContrato = v.alterouDataInicioContrato;
				this.cd.detectChanges();
				this.negociacaoState.getConfigsContrato().descricao = v.descricao;
			},
			() => {
				this.cd.detectChanges();
			}
		);
	}

	autorizar(tipo, funcionalidade, permissao): void {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form.get("usuario").setValue("");
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					funcionalidade,
					permissao,
					this.sessionService.empresaId.toString()
				)
				.subscribe(
					(response: any) => {
						if (tipo === "lancamento") {
							this.negociacaoState.usuarioDataBase = response.content;
							this.abrirDataLancamento();
						}
						result.modal.close();
					},
					(error) => {
						this.abrirDataLancamento();
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	private openModalAvisoClienteRestricao(clienteRestricoes) {
		let unidades = "";
		clienteRestricoes.forEach((cr) => {
			if (!unidades.includes(cr.nomeEmpresa)) {
				unidades += unidades === "" ? cr.nomeEmpresa : ", " + cr.nomeEmpresa;
			}
		});
		const msg =
			"Identificamos que este cliente está bloqueado devido a pendências na(s) unidade(s): " +
			unidades +
			". Para resolver esta situação e reativar seus serviços, solicitamos que o cliente entre em contato " +
			"com a(s) referida(s) unidade(s) para regularizar suas pendências.";

		const dialogRef = this.modal.open(
			"Aviso",
			ModalAvisoClienteRestricaoComponent,
			PactoModalSize.LARGE,
			"modal-cliente-restricao-msg-bloqueio design-system3-adjust"
		);
		dialogRef.componentInstance.titulo = "Cliente com pendências";
		dialogRef.componentInstance.mensagem = msg;
		dialogRef.result.then(() => {
			this.resetAll();
		});
	}

	get utilizarGestaoClienteComRestricao() {
		return this.permissaoService.temConfiguracaoEmpresaAdm(
			"utilizaGestaoClientesComRestricoes"
		);
	}

	get bolsa(): boolean {
		return this.negociacao && this.negociacao.bolsa;
	}

	get temPermissaoEditarParcelas(): boolean {
		return (
			this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades.findIndex(
				(f) =>
					f.nome === "permiteeditarvalorparcelanegociacao" &&
					f.possuiFuncionalidade
			) > -1
		);
	}

	get descontoRenovacaoAntecipada(): any {
		return this.simulado ? this.simulado.descontoRenovacaoAntecipada : null;
	}

	private loadBvCliente(codigoCliente: number) {
		if (
			this.check.codigoContratoRenovacao &&
			this.check.codigoContratoRenovacao > 0
		) {
			return;
		}
		this.questionarioClienteService
			.ultimoBvCliente(
				codigoCliente,
				!!(
					this.check.codigoContratoRematricula &&
					this.check.codigoContratoRematricula > 0
				)
			)
			.subscribe((response: AdmCoreApiResponseSingle<QuestionarioCliente>) => {
				this.questionarioCliente = response.content;
				const dataHoje = new Date();
				dataHoje.setHours(0, 0, 0, 0);
				this.questionarioCliente.data = dataHoje;
				this.abriuBV = false;
				if (this.questionarioCliente) {
					// Origem sistema nova tela de negociação
					this.questionarioCliente.origemSistema = 17;
					if (this.questionarioCliente.preencherQuestionario) {
						const bvObrigatorio =
							this.permissaoService.temConfiguracaoEmpresaAdm("bvObrigatorio");
						const consultor =
							this.questionarioCliente && this.questionarioCliente.consultor;
						if (consultor) {
							const temConsultor = consultor.codigo;
							const consultorMesmaUnidade =
								consultor.empresa.codigo === +this.sessionService.empresaId;
							if (bvObrigatorio || !temConsultor || !consultorMesmaUnidade) {
								this.preencherBv();
							}
						}
					}
				}
			});
	}

	private _obrigatorioPreencherBv() {
		const bvObrigatorio =
			this.permissaoService.temConfiguracaoEmpresaAdm("bvObrigatorio");
		const consultor =
			this.questionarioCliente && this.questionarioCliente.consultor;
		let obrigatorioPreencherBv = false;
		if (consultor) {
			const temConsultor = consultor.codigo;
			const consultorMesmaUnidade =
				consultor.empresa.codigo === +this.sessionService.empresaId;
			obrigatorioPreencherBv =
				bvObrigatorio || !temConsultor || !consultorMesmaUnidade;
		}
		return obrigatorioPreencherBv;
	}

	preencherBv() {
		if (!this.abriuBV) {
			const modalRef = this.matDialog.open(ModalBvComponent, {
				autoFocus: false,
				disableClose: true,
				width: "1000px",
				height: "550px",
				panelClass: "mdl-bv-negociacao",
				data: {
					questionarioCliente: this.questionarioCliente,
					cliente: this.cliente,
					rematricula: !!(
						this.check.codigoContratoRematricula &&
						this.check.codigoContratoRematricula > 0
					),
					obrigatorioPreencherBv: this._obrigatorioPreencherBv(),
				},
			});

			this.abriuBV = true;

			modalRef.afterClosed().subscribe((modalResult) => {
				this.abriuBV = false;
				if (modalResult && modalResult.atualizouBV) {
					this.atualizouBV = modalResult.atualizouBV;
					this.questionarioCliente = modalResult.questionarioCliente;
					this.questionarioCliente.preencherQuestionario = false;
				}
				this.cd.detectChanges();
			});
		}
	}

	private _openModalObrigatorioAutorizacaoCobranca() {
		this.matDialog.open(ModalAutorizacaoCobrancaObrigatorioComponent, {
			width: "800px",
			panelClass: "mdl-obrg-auto-cobranca",
			autoFocus: false,
		});
	}

	get codigoPlanoSelecionado(): number {
		return this.planoFc && this.planoFc.value ? this.planoFc.value : 0;
	}

	get zwBoot(): boolean {
		return this.sessionService.modulosHabilitados.includes(
			PlataformaModulo.ZWB
		);
	}

	private abrirModalNenhumConvenio() {
		const modal: PactoModalRef = this.modalService.open(
			"Autorização de cobrança",
			ModalNotificacaoComponent,
			PactoModalSize.LARGE
		);

		modal.componentInstance.imagemUrl = "pacto-ui/images/ds3-illustration.svg";
		modal.componentInstance.title = "Nenhum convênio de cobrança encontrado.";
		modal.componentInstance.subtitle =
			"Para adicionar uma autorização de cobrança, é necessário ter um convênio de cobrança cadastrado e ativo no sistema.";
		modal.componentInstance.body = `<div style="display: flex; align-items: center;"><i class="pct pct-info" style="margin-right: 10px; color: #0a64ff;"></i>
																		<span>Para mais informações ou ajuda, conte com o suporte da Pacto.</span></div>`;
		modal.componentInstance.actions = [
			{
				clickHandler: () => modal.close(true),
				type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
				size: BUTTON_SIZE.LARGE,
				label: "Entendi",
				full: true,
			},
		];
	}
}
