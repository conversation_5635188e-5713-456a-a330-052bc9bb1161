import { NgModule } from "@angular/core";
import { CommonModule, CurrencyPipe } from "@angular/common";

import { RelatoriosRoutingModule } from "./relatorios-routing.module";
import { RelatorioVisitantesComponent } from "./components/relatorio-visitantes/relatorio-visitantes.component";
import { LayoutModule } from "../layout/layout.module";
import { UiModule } from "ui-kit";
import { NgbModule, NgbPaginationModule } from "@ng-bootstrap/ng-bootstrap";
import { RelatorioArmarioComponent } from "./components/relatorio-armario/relatorio-armario.component";
import { RelatorioArmarioDetalheComponent } from "./components/relatorio-armario-detalhe/relatorio-armario-detalhe.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RelatorioGympassComponent } from "./components/relatorio-gympass/relatorio-gympass.component";
import { RelatorioGympassDetalheComponent } from "./components/relatorio-gympass-detalhe/relatorio-gympass-detalhe.component";
import { RelatorioConvidadosComponent } from "./components/relatorio-convidados/relatorio-convidados.component";
import { RelatorioConvidadosDetalheComponent } from "./components/relatorio-convidados-detalhe/relatorio-convidados-detalhe.component";
import { RelatorioSaldoCreditoComponent } from "./components/relatorio-saldo-credito/relatorio-saldo-credito.component";
import { RelatorioPersonalComponent } from "./components/relatorio-personal/relatorio-personal.component";
import { RelatorioPersonalDetalheComponent } from "./components/relatorio-personal-detalhe/relatorio-personal-detalhe.component";
import { RelatorioCupomfiscalComponent } from "./components/relatorio-cupomfiscal/relatorio-cupomfiscal.component";
import { RelatorioCupomFiscalDetalheComponent } from "./components/relatorio-cupomfiscal-detalhe/relatorio-cupomfiscal-detalhe.component";
import { RelatoriosAcessosComponent } from "./components/relatorios-acessos/relatorios-acessos.component";
import { ListaAcessosComponent } from "./components/lista-acessos/lista-acessos.component";
import { ConsultaClienteModalComponent } from "./components/consulta-cliente-modal/consulta-cliente-modal.component";
import { TotalizadorAcessosComponent } from "./components/totalizador-acessos/totalizador-acessos.component";
import { RelatorioContratosPorDuracaoComponent } from "./components/relatorio-contratos-por-duracao/relatorio-contratos-por-duracao.component";
import { ContratoDetalhesModalComponent } from "./components/relatorio-contratos-por-duracao/contrato-detalhes-modal/contrato-detalhes-modal.component";
import { AdmUiModule } from "../modules/adm-ui/adm-ui.module";
import { RelatorioContratosPorDuracaoAlunosComponent } from "./components/relatorio-contratos-por-duracao/relatorio-contratos-por-duracao-alunos/relatorio-contratos-por-duracao-alunos.component";
import { IndicadorAcessosComponent } from "./components/relatorio-indicador-acessos/relatorio-indicador-acessos.component";
import { RelatorioProdutosVigenciaComponent } from "./components/relatorio-produto-vigencia/relatorio-produtos-vigencia.component";
import { RelatorioProdutosVigenciaDetalheComponent } from "./components/relatorio-produto-vigencia-detalhe/relatorio-produtos-vigencia-detalhe.component";
import { ClientesComRestricoesComponent } from "./components/clientes-com-restricao/clientes-com-restricoes.component";
import { RelatorioPedidosPinpadComponent } from "./components/relatorio-pedidos-pinpad/relatorio-pedidos-pinpad.component";
import { PedidoParametrosModalComponent } from "./components/relatorio-pedidos-pinpad/components/pedido-parametros-modal/pedido-parametros-modal.component";
import { PedidoStatusModalComponent } from "./components/relatorio-pedidos-pinpad/components/pedido-status-modal/pedido-status-modal.component";
import { RelatorioSMDComponent } from "./components/relatorio-smd/relatorio-smd.component";
import { ClientesCobrancaAutomaticaBloqueadaComponent } from "./components/clientes-cobranca-automatica-bloqueada/clientes-cobranca-automatica-bloqueada.component";

@NgModule({
	declarations: [
		RelatorioVisitantesComponent,
		RelatorioArmarioComponent,
		RelatorioArmarioDetalheComponent,
		RelatorioGympassComponent,
		RelatorioGympassDetalheComponent,
		RelatorioConvidadosComponent,
		RelatorioConvidadosDetalheComponent,
		RelatorioPersonalComponent,
		RelatorioPersonalDetalheComponent,
		RelatorioCupomfiscalComponent,
		RelatorioCupomFiscalDetalheComponent,
		RelatoriosAcessosComponent,
		ListaAcessosComponent,
		ConsultaClienteModalComponent,
		RelatorioSaldoCreditoComponent,
		TotalizadorAcessosComponent,
		RelatorioContratosPorDuracaoComponent,
		ContratoDetalhesModalComponent,
		RelatorioContratosPorDuracaoAlunosComponent,
		IndicadorAcessosComponent,
		RelatorioProdutosVigenciaComponent,
		RelatorioProdutosVigenciaDetalheComponent,
		RelatorioVisitantesComponent,
		ClientesComRestricoesComponent,
		RelatorioPedidosPinpadComponent,
		PedidoParametrosModalComponent,
		PedidoStatusModalComponent,
		RelatorioSMDComponent,
		ClientesCobrancaAutomaticaBloqueadaComponent,
	],
	imports: [
		CommonModule,
		NgbModule,
		RelatoriosRoutingModule,
		LayoutModule,
		UiModule,
		NgbPaginationModule,
		FormsModule,
		ReactiveFormsModule,
		AdmUiModule,
	],
	entryComponents: [
		RelatorioArmarioDetalheComponent,
		RelatorioGympassDetalheComponent,
		RelatorioConvidadosDetalheComponent,
		RelatorioPersonalDetalheComponent,
		RelatorioCupomFiscalDetalheComponent,
		ConsultaClienteModalComponent,
		ContratoDetalhesModalComponent,
		RelatorioProdutosVigenciaDetalheComponent,
		RelatorioVisitantesComponent,
		PedidoStatusModalComponent,
		PedidoParametrosModalComponent,
	],
	providers: [CurrencyPipe],
})
export class RelatoriosModule {}
