import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { RelatorioArmarioComponent } from "./components/relatorio-armario/relatorio-armario.component";
import { RelatorioVisitantesComponent } from "./components/relatorio-visitantes/relatorio-visitantes.component";
import { RelatorioGympassComponent } from "./components/relatorio-gympass/relatorio-gympass.component";
import { RelatorioConvidadosComponent } from "./components/relatorio-convidados/relatorio-convidados.component";
import { RelatorioPersonalComponent } from "./components/relatorio-personal/relatorio-personal.component";
import { RelatorioCupomfiscalComponent } from "./components/relatorio-cupomfiscal/relatorio-cupomfiscal.component";
import { RelatoriosAcessosComponent } from "./components/relatorios-acessos/relatorios-acessos.component";
import { ListaAcessosComponent } from "./components/lista-acessos/lista-acessos.component";
import { RelatorioSaldoCreditoComponent } from "./components/relatorio-saldo-credito/relatorio-saldo-credito.component";
import { TotalizadorAcessosComponent } from "./components/totalizador-acessos/totalizador-acessos.component";
import { RelatorioContratosPorDuracaoComponent } from "./components/relatorio-contratos-por-duracao/relatorio-contratos-por-duracao.component";
import { IndicadorAcessosComponent } from "./components/relatorio-indicador-acessos/relatorio-indicador-acessos.component";
import { RelatorioProdutosVigenciaComponent } from "./components/relatorio-produto-vigencia/relatorio-produtos-vigencia.component";
import { ClientesComRestricoesComponent } from "./components/clientes-com-restricao/clientes-com-restricoes.component";
import { RelatorioPedidosPinpadComponent } from "./components/relatorio-pedidos-pinpad/relatorio-pedidos-pinpad.component";
import { RelatorioSMDComponent } from "./components/relatorio-smd/relatorio-smd.component";
import { ClientesCobrancaAutomaticaBloqueadaComponent } from "./components/clientes-cobranca-automatica-bloqueada/clientes-cobranca-automatica-bloqueada.component";

const routes: Routes = [
	{
		path: "relatorio-armario",
		component: RelatorioArmarioComponent,
	},
	{
		path: "relatorio-visitantes",
		component: RelatorioVisitantesComponent,
	},
	{
		path: "relatorio-gympass",
		component: RelatorioGympassComponent,
	},
	{
		path: "relatorio-convidados",
		component: RelatorioConvidadosComponent,
	},
	{
		path: "relatorio-saldo-credito",
		component: RelatorioSaldoCreditoComponent,
	},
	{
		path: "relatorio-personal",
		component: RelatorioPersonalComponent,
	},
	{
		path: "relatorio-cupomfiscal",
		component: RelatorioCupomfiscalComponent,
	},
	{
		path: "relatorio-produtos-vigencia",
		component: RelatorioProdutosVigenciaComponent,
	},
	{
		path: "relatorios-acessos",
		component: RelatoriosAcessosComponent,
	},
	{
		path: "relatorios-acessos/lista-acessos",
		component: ListaAcessosComponent,
	},
	{
		path: "relatorios-acessos/totalizador-acessos",
		component: TotalizadorAcessosComponent,
	},
	{
		path: "contratos-por-duracao",
		component: RelatorioContratosPorDuracaoComponent,
	},
	{
		path: "relatorios-acessos/indicador-acessos",
		component: IndicadorAcessosComponent,
	},
	{
		path: "clientes-com-restricoes",
		component: ClientesComRestricoesComponent,
	},
	{
		path: "relatorio-pedidos-pinpad",
		component: RelatorioPedidosPinpadComponent,
	},
	{
		path: "relatorio-smd",
		component: RelatorioSMDComponent,
	},
	{
		path: "clientes-cobranca-automatica-bloqueada",
		component: ClientesCobrancaAutomaticaBloqueadaComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class RelatoriosRoutingModule {}
