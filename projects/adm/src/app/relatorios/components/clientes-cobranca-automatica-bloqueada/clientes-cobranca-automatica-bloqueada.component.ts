import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { catchError, finalize } from "rxjs/operators";
import { of } from "rxjs";
import { DataFiltro, PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { AdmRestService } from "@adm/adm-rest.service";

@Component({
	selector: "adm-clientes-cobranca-automatica-bloqueada",
	templateUrl: "./clientes-cobranca-automatica-bloqueada.component.html",
	styleUrls: ["./clientes-cobranca-automatica-bloqueada.component.scss"],
})
export class ClientesCobrancaAutomaticaBloqueadaComponent implements OnInit {
	@ViewChild("tabelaComponent", { static: false })
	tabelaComponent: RelatorioComponent;
	public form: FormGroup;
	public table: PactoDataGridConfig;
	public loading = false;
	baseFilter: DataFiltro = {};
	empresas = [];
	constructor(
		private router: Router,
		private readonly fb: FormBuilder,
		private admRest: AdmRestService,
		private readonly cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private readonly sessionService: SessionService
	) {}

	ngOnInit() {
		this.init();
		this.initTable();
		this.cd.detectChanges();
	}

	init() {
		this.empresas = [
			{ codigo: 9999, nome: "TODAS AS EMPRESAS" },
			...this.sessionService.empresas,
		];
		this.form = this.fb.group({
			dataBloqueio: new FormControl(),
			dataAte: new FormControl(),
			empresa: new FormControl(Number(this.sessionService.empresaId)),
		});
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlZwBack(
				"clientes-bloqueio-cobranca"
			),
			ghostLoad: true,
			ghostAmount: 5,
			pagination: true,
			quickSearch: true,
			showFilters: true,
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "nome",
					titulo: "Nome",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataBloqueio",
					titulo: "Data Bloqueio",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "tipoBloqueioCobranca",
					titulo: "Tipo bloqueio",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "empresaNome",
					titulo: "Empresa",
					visible: true,
					ordenavel: true,
				},
			],
		});
	}

	consultar() {
		let dataBloqueio = this.form.get("dataBloqueio").value;
		let dataAte = this.form.get("dataAte").value;
		let empresaId = this.form.get("empresa").value;
		if (!empresaId || empresaId === 9999) {
			empresaId = null;
		}
		if (dataBloqueio) {
			dataBloqueio = this.formatDateToDDMMYYYY(dataBloqueio);
		}

		if (dataAte) {
			dataAte = this.formatDateToDDMMYYYY(dataAte);
		}
		this.baseFilter.filters = {
			empresa: empresaId,
			dataInicio: dataBloqueio,
			dataFinal: dataAte,
		};
		this.tabelaComponent.reloadData();
	}

	private formatDateToDDMMYYYY(date: Date): string {
		if (!date) {
			return "";
		}
		const day = date.getDate().toString().padStart(2, "0");
		const month = (date.getMonth() + 1).toString().padStart(2, "0");
		const year = date.getFullYear();
		return `${day}/${month}/${year}`;
	}

	exportar() {
		// Implementar lógica de exportação
		this.notificationService.info(
			"Funcionalidade de exportação será implementada."
		);
	}

	voltar() {
		this.router.navigate(["adm"]);
	}
}
