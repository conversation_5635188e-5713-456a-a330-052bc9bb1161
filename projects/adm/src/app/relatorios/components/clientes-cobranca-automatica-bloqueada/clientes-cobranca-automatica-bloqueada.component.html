<adm-layout
	(goBack)="voltar()"
	addBtnLabel="Clientes com cobrança automática bloqueada"
	modulo="Administrativo"
	pageTitle="Clientes com cobrança automática bloqueada">
	<div class="table-wrapper">
		<form [formGroup]="form">
			<div class="filtros-direita">
				<div class="filtros-direita-item">
					<ds3-form-field>
						<ds3-field-label>Data Bloqueio</ds3-field-label>
						<ds3-input-date
							ds3Input
							dateType="dateranger"
							[controlStart]="form.get('dataBloqueio')"
							[controlEnd]="form.get('dataAte')"></ds3-input-date>
					</ds3-form-field>
				</div>
				<div class="filtros-direita-item">
					<ds3-form-field>
						<ds3-field-label>Empresa</ds3-field-label>
						<ds3-select
							id="select-empresa"
							[options]="empresas"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							[formControl]="form.get('empresa')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>
		</form>

		<pacto-relatorio
			#tabelaComponent
			[enableZebraStyle]="true"
			[baseFilter]="baseFilter"
			[showShare]="true"
			[table]="table"
			[customActionsRight]="btnConsultar"
			[enableDs3]="true"
			emptyStateMessage="Nenhum registro encontrado para os filtros informados."
			id="rel-clientes-bloqueio-cobranca"></pacto-relatorio>

		<ng-template #btnConsultar>
			<button
				id="btn-consultar"
				ds3-flat-button
				(click)="consultar()"
				[disabled]="loading">
				<span>Consultar</span>
			</button>
		</ng-template>
	</div>
</adm-layout>
