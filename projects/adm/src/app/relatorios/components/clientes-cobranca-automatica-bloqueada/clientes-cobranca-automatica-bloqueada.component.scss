@import "projects/ui/assets/ds3/colors.var.scss";

form {
	font-size: 14px;
}

.footer {
	display: flex;
	gap: 16px;
	justify-content: flex-end;
	margin-top: 24px;
}

.filtros-direita {
	display: flex;
	padding: 16px 16px 0 16px;
	width: 100%;
	.filtros-direita-item {
		flex: 1;
		&:nth-child(1) {
			margin-right: 45px;
		}
	}
}

ds3-form-field {
	margin-bottom: 16px;
	display: block;
}

.form-card {
	margin-top: 24px;
}

#btn-consultar {
	background-color: #007bff;
	color: white;
	border: none;
	padding: 8px 16px;
	border-radius: 4px;
	cursor: pointer;

	&:hover:not(:disabled) {
		background-color: #0056b3;
	}

	&:disabled {
		background-color: #6c757d;
		cursor: not-allowed;
		opacity: 0.6;
	}
}

.results-card {
	margin-top: 24px;

	h3 {
		margin-bottom: 16px;
		font-weight: 600;
	}
}
