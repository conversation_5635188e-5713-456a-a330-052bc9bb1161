import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import {
	MENU_I18N,
	MenuTranslation,
	PlataformaConfigService,
	PlataformaNavConfigService,
} from "ui-kit";

import { ModuleName } from "@base-core/modulo/modulo.model";
import { PactoLayoutSearchService } from "pacto-layout";
import { PerfilAcessoRecurso } from "sdk";
import { admEn } from "./config-menu/i18n/en/admEn";
import { admModulesEn } from "./config-menu/i18n/en/admModulesEn";
import { configEn } from "./config-menu/i18n/en/config/configEn";
import { admEs } from "./config-menu/i18n/es/admEs";
import { admModulesEs } from "./config-menu/i18n/es/admModulesEs";
import { admModulesPt } from "./config-menu/i18n/pt/admModulesPt";
import { admPt } from "./config-menu/i18n/pt/admPt";
import { configPt } from "./config-menu/i18n/pt/config/configPt";
import { AdmPactoLayoutSearchService } from "./config-menu/navigation/search/adm-pacto-layout-search.service";
import { ConvenioDescontoFormComponent } from "./convenio-desconto/components/convenio-desconto-form/convenio-desconto-form.component";
import { ConvenioDescontoComponent } from "./convenio-desconto/components/convenio-desconto/convenio-desconto.component";
import { DiariasComponent } from "./diarias/components/diarias/diarias.component";
import { LoggedinGuard } from "./guards/logged-in.guard";
import { PerfilAcessoGuard } from "./guards/perfil-acesso.guard";
import { HomeComponent } from "./home/<USER>";
import { LayoutConfigService } from "./layout-config.service";
import { LayoutNavigationService } from "./layout-navigation.service";
import { MenuAdmV2Component } from "./menu-adm-v2/menu-adm-v2.component";
import { AddEditModalidadeComponent } from "./modalidade/add-edit-modalidade/add-edit-modalidade.component";
import { ListModalidadeComponent } from "./modalidade/list-modalidade/list-modalidade.component";
import { MyAddAccountComponent } from "./my-add-account/my-add-account.component";
import { PacoteFormComponent } from "./pacotes/components/form-pacote/form-pacote.component";
import { ListaPacoteComponent } from "./pacotes/components/lista-pacote/lista-pacote.component";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "./perfil-acesso/perfil-acesso-recurso.model";
import { CadastrarPlanoComponent } from "./planos/components/cadastrar-plano/cadastrar-plano.component";
import { CondicaoPagamentoFormComponent } from "./planos/components/condicao-pagamento-form/condicao-pagamento-form.component";
import { CondicaoPagamentoComponent } from "./planos/components/condicao-pagamento/condicao-pagamento.component";
import { PcCadastroComponent } from "./planos/components/credito/pc-cadastro/pc-cadastro.component";
import { DescontoFormComponent } from "./planos/components/desconto-form/desconto-form.component";
import { DescontoComponent } from "./planos/components/desconto/desconto.component";
import { EditFormPlanoComponent } from "./planos/components/edit-form-plano/edit-form-plano.component";
import { HorarioFormComponent } from "./planos/components/horario-form/horario-form.component";
import { HorarioComponent } from "./planos/components/horario/horario.component";
import { PlanosComponent } from "./planos/components/lista-planos/planos.component";
import { PpCadastroComponent } from "./planos/components/personal/pp-cadastro/pp-cadastro.component";
import { PrCadastroComponent } from "./planos/components/recorrencia/pr-cadastro/pr-cadastro.component";
import { TipoPlanoFormComponent } from "./planos/components/tipo-plano-form/tipo-plano-form.component";
import { TipoPlanoComponent } from "./planos/components/tipo-plano/tipo-plano.component";
import { PlataformaLinkRedirectComponent } from "./plataforma-link-adm/plataforma-link-redirect.component";
import { AdicionarClienteModule } from "@adm/adicionar-cliente/adicionar-cliente.module";

const translations: MenuTranslation = {
	pt: {
		modules: admModulesPt,
		navigation: {
			adm: admPt,
			config: configPt,
		},
	},
	en: {
		modules: admModulesEn,
		navigation: {
			adm: admEn,
			config: configEn,
		},
	},
	es: {
		modules: admModulesEs,
		navigation: {
			adm: admEs,
		},
	},
};

const routes: Routes = [
	{
		path: "adicionarConta",
		component: MyAddAccountComponent,
	},
	{
		path: "configuracao",
		component: MenuAdmV2Component,
		data: { moduleId: "config", module: ModuleName.CONFIG },
		canActivate: [LoggedinGuard],
		loadChildren: () =>
			import("./configuracao/configuracao.module").then(
				(m) => m.ConfiguracaoModule
			),
	},
	{
		path: "",
		component: MenuAdmV2Component,
		children: [
			{
				path: "",
				pathMatch: "full",
				redirectTo: "adm",
			},
			{
				path: "redirectADM",
				data: { onStart: true },
				component: PlataformaLinkRedirectComponent,
			},
			{
				data: { moduleId: "adm" },
				path: "adm",
				canActivate: [LoggedinGuard],
				children: [
					{
						path: "",
						component: HomeComponent,
					},
					{
						path: "diarias",
						component: DiariasComponent,
					},
					{
						path: "planos",
						component: PlanosComponent,
						canActivate: [PerfilAcessoGuard],
						data: {
							recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PLANO, [
								PerfilRecursoPermissoTipo.CONSULTAR,
								PerfilRecursoPermissoTipo.EDITAR,
								PerfilRecursoPermissoTipo.EXCLUIR,
								PerfilRecursoPermissoTipo.INCLUIR,
								PerfilRecursoPermissoTipo.TOTAL,
								PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
								PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
							]),
						},
					},
					{
						path: "plano/:id",
						component: EditFormPlanoComponent,
						canActivate: [PerfilAcessoGuard],
						data: {
							recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PLANO, [
								PerfilRecursoPermissoTipo.EDITAR,
								PerfilRecursoPermissoTipo.CONSULTAR,
								PerfilRecursoPermissoTipo.TOTAL,
								PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
								PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
							]),
						},
					},
					{
						path: "planos/novo-plano",
						canActivate: [PerfilAcessoGuard],
						data: {
							recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.PLANO, [
								PerfilRecursoPermissoTipo.INCLUIR,
								PerfilRecursoPermissoTipo.CONSULTAR,
								PerfilRecursoPermissoTipo.TOTAL,
								PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
								PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
							]),
						},
						children: [
							{
								path: "",
								component: CadastrarPlanoComponent,
								children: [
									{
										path: "avancado",
										component: CadastrarPlanoComponent,
									},
								],
							},
							{
								path: "recorrencia",
								component: PrCadastroComponent,
							},
							{
								path: "credito",
								component: PcCadastroComponent,
							},
							{
								path: "personal",
								component: PpCadastroComponent,
							},
						],
					},
					{
						path: "caixa-em-aberto",
						loadChildren: () =>
							import("./caixa-em-aberto/caixa-em-aberto.module").then(
								(m) => m.CaixaEmAbertoModule
							),
					},
					{
						path: "planos/tipo-plano",
						component: TipoPlanoComponent,
					},
					{
						path: "planos/novo-tipo-plano",
						component: TipoPlanoFormComponent,
					},
					{
						path: "planos/tipo-plano/:id",
						component: TipoPlanoFormComponent,
					},
					{
						path: "planos/pacotes",
						component: ListaPacoteComponent,
					},
					{
						path: "planos/novo-pacote",
						component: PacoteFormComponent,
					},
					{
						path: "planos/pacotes/:id",
						component: PacoteFormComponent,
					},
					{
						path: "planos/desconto",
						component: DescontoComponent,
					},
					{
						path: "planos/novo-desconto",
						component: DescontoFormComponent,
					},
					{
						path: "planos/desconto/:id",
						component: DescontoFormComponent,
					},
					{
						path: "planos/horarios",
						component: HorarioComponent,
					},
					{
						path: "planos/novo-horario",
						component: HorarioFormComponent,
					},
					{
						path: "planos/horarios/:id",
						component: HorarioFormComponent,
					},
					{
						path: "planos/convenio-desconto",
						component: ConvenioDescontoComponent,
					},
					{
						path: "planos/novo-convenio-desconto",
						component: ConvenioDescontoFormComponent,
					},
					{
						path: "planos/convenio-desconto/:id",
						component: ConvenioDescontoFormComponent,
					},

					{
						path: "modalidade",
						component: ListModalidadeComponent,
					},

					{
						path: "modalidade/incluir",
						component: AddEditModalidadeComponent,
					},
					{
						path: "modalidade/editar",
						component: AddEditModalidadeComponent,
					},
					{
						path: "modalidade/visualizar",
						component: AddEditModalidadeComponent,
					},

					{
						path: "solicitacao-compra",
						loadChildren: () =>
							import("./solicitacao-compra/solicitacaoCompra.module").then(
								(m) => m.SolicitacaoCompraModule
							),
					},
					{
						path: "cad-aux",
						loadChildren: () =>
							import("./cadastro-auxliar/cadastro-auxliar.module").then(
								(m) => m.CadastroAuxliarModule
							),
					},
					{
						path: "produtos",
						loadChildren: () =>
							import("./produtos/produtos.module").then(
								(m) => m.ProdutosModule
							),
					},
					{
						path: "planos/condicao-pagamento",
						component: CondicaoPagamentoComponent,
						canActivate: [PerfilAcessoGuard],
						data: {
							recurso: new PerfilAcessoRecurso(
								PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO,
								[
									PerfilRecursoPermissoTipo.CONSULTAR,
									PerfilRecursoPermissoTipo.EDITAR,
									PerfilRecursoPermissoTipo.INCLUIR,
									PerfilRecursoPermissoTipo.EXCLUIR,
									PerfilRecursoPermissoTipo.TOTAL,
									PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
									PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
								]
							),
						},
					},
					{
						path: "planos/nova-condicao-pagamento",
						component: CondicaoPagamentoFormComponent,
						canActivate: [PerfilAcessoGuard],
						data: {
							recurso: new PerfilAcessoRecurso(
								PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO,
								[
									PerfilRecursoPermissoTipo.INCLUIR,
									PerfilRecursoPermissoTipo.CONSULTAR,
									PerfilRecursoPermissoTipo.TOTAL,
									PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
									PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
								]
							),
						},
					},
					{
						path: "planos/condicao-pagamento/:id",
						component: CondicaoPagamentoFormComponent,
						canActivate: [PerfilAcessoGuard],
						data: {
							recurso: new PerfilAcessoRecurso(
								PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO,
								[
									PerfilRecursoPermissoTipo.EDITAR,
									PerfilRecursoPermissoTipo.CONSULTAR,
									PerfilRecursoPermissoTipo.TOTAL,
									PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
									PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
								]
							),
						},
					},
					{
						path: "relatorios",
						loadChildren: () =>
							import("./relatorios/relatorios.module").then(
								(m) => m.RelatoriosModule
							),
					},
					{
						path: "clube-vantagens",
						loadChildren: () =>
							import("./clube-vantagens/clube-vantagens.module").then(
								(m) => m.ClubeVantagensModule
							),
					},
					{
						path: "acesso-sistema",
						loadChildren: () =>
							import("./acesso-sitema/acesso-sitema.module").then(
								(m) => m.AcessoSitemaModule
							),
					},
					{
						path: "config-financeiras",
						loadChildren: () =>
							import("./config-financeiras/config-financeiras.module").then(
								(m) => m.ConfigFinanceirasModule
							),
					},
					{
						path: "config-contrato",
						loadChildren: () =>
							import("./config-contrato/config-contrato.module").then(
								(m) => m.ConfigContratoModule
							),
					},
					{
						path: "venda-rapida",
						loadChildren: () =>
							import("./venda-rapida/venda-rapida.module").then(
								(m) => m.VendaRapidaModule
							),
					},
					{
						path: "outras-opcoes",
						loadChildren: () =>
							import("./outras-opcoes/outras-opcoes.module").then(
								(m) => m.OutrasOpcoesModule
							),
					},
					{
						path: "freepass",
						loadChildren: () =>
							import("./freepass/freepass.module").then(
								(m) => m.FreepassModule
							),
					},
					{
						path: "negociacao",
						loadChildren: () =>
							import("./negociacao/negociacao.module").then(
								(m) => m.NegociacaoModule
							),
					},
					{
						path: "venda-avulsa",
						loadChildren: () =>
							import("./venda-avulsa/venda-avulsa.module").then(
								(m) => m.VendaAvulsaModule
							),
					},
					{
						path: "perfil-acesso-unificado",
						loadChildren: () =>
							import("./perfil-acesso/perfil-acesso.module").then(
								(m) => m.PerfilAcessoModule
							),
					},
					{
						path: "bi",
						loadChildren: () =>
							import("./modules/bi/bi.module").then((m) => m.BiModule),
					},
					{
						path: "posicao-estoque",
						loadChildren: () =>
							import("./posicao-estoque/posicao-estoque.module").then(
								(m) => m.PosicaoEstoqueModule
							),
					},
					{
						path: "autorizacao-acesso",
						loadChildren: () =>
							import("./autorizacao-acesso/autorizacao-acesso.module").then(
								(m) => m.AutorizacaoAcessoModule
							),
					},
					{
						path: "adicionar-cliente",
						loadChildren: () =>
							import("./adicionar-cliente/adicionar-cliente.module").then(
								(m) => m.AdicionarClienteModule
							),
					},
				],
			},
		],
	},
	{
		/**
		 * Rota preparada para quando for migrar as configurações do adm
		 */
		path: "config",
		data: {
			moduleId: "config",
		},
		component: MenuAdmV2Component,
		children: [
			{
				path: "",
				loadChildren: () =>
					import("./configuracoes/configuracoes.module").then(
						(m) => m.ConfiguracoesModule
					),
				canActivate: [LoggedinGuard],
			},
		],
	},
];

@NgModule({
	imports: [RouterModule.forRoot(routes)],
	exports: [RouterModule],
	providers: [
		{ provide: PlataformaConfigService, useClass: LayoutConfigService },
		{
			provide: PactoLayoutSearchService,
			useClass: AdmPactoLayoutSearchService,
		},
		{
			provide: PlataformaNavConfigService,
			useClass: LayoutNavigationService,
		},
		{ provide: MENU_I18N, useValue: translations },
	],
})
export class AppRoutingModule {}
