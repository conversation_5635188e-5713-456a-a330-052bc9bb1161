import { Injectable } from "@angular/core";
import { AdmCoreApiBaseService } from "../adm-core-api-base.service";
import { AdmCoreApiModule } from "../adm-core-api.module";
import { ApiResponseList } from "../base.model";
import { Observable } from "rxjs";
import { ModalidadeModel } from "./modalidade.model";

@Injectable({
	providedIn: AdmCoreApiModule,
})
export class AdmCoreApiModalidadeService {
	constructor(private restApi: AdmCoreApiBaseService) {}

	public listModalidade(): Observable<ApiResponseList<ModalidadeModel>> {
		return this.restApi.get<ApiResponseList<ModalidadeModel>>(`modalidade`);
	}

	public list(filtros?: any): Observable<ApiResponseList<ModalidadeModel>> {
		const page = filtros.page ? filtros.page : 0;
		const size = filtros.size ? filtros.size : 10;
		delete filtros.page;
		delete filtros.size;
		const params: any = {
			page,
			size,
			filters: JSON.stringify(filtros),
		};

		return this.restApi.get<ApiResponseList<ModalidadeModel>>(`modalidade`, {
			params,
		});
	}

	public byIdModalidade(
		id: number
	): Observable<ApiResponseList<ModalidadeModel>> {
		return this.restApi.get<ApiResponseList<ModalidadeModel>>(
			`modalidade/${id}`
		);
	}

	public savedModalidade(
		body: ModalidadeModel
	): Observable<ApiResponseList<ModalidadeModel>> {
		return this.restApi.post<ApiResponseList<ModalidadeModel>>(
			`modalidade`,
			body
		);
	}

	public deleteModalidade(
		id: number
	): Observable<ApiResponseList<ModalidadeModel>> {
		return this.restApi.delete<ApiResponseList<ModalidadeModel>>(
			`modalidade/${id}`
		);
	}

	public listTipoModalidade(): Observable<ApiResponseList<ModalidadeModel>> {
		return this.restApi.get<ApiResponseList<ModalidadeModel>>(
			`tipo-modalidade`
		);
	}
}
