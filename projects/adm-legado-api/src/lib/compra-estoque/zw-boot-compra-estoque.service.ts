import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootCompraEstoqueService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(filters: any, params: any): Observable<any> {
		return this.admZwBootApiBaseService.get(`compra-estoque/findAll`, {
			params: {
				filters: JSON.stringify(filters),
				...params,
			},
		});
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(`compra-estoque/by-codigo/${id}`);
	}

	public config(): Observable<any> {
		return this.admZwBootApiBaseService.get(`compra-estoque/config`);
	}

	public save(compraEstoque: any): Observable<any> {
		return this.admZwBootApiBaseService.post(`compra-estoque`, compraEstoque);
	}

	public processarXml(body: any): Observable<any> {
		return this.admZwBootApiBaseService.post(
			`compra-estoque/processar-xml`,
			body
		);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(`compra-estoque/${id}`);
	}

	public fornecedores(): Observable<any> {
		return this.admZwBootApiBaseService.get(`compra-estoque/fornecedores`);
	}

	public produtos(): Observable<any> {
		return this.admZwBootApiBaseService.get(`compra-estoque/produtos`);
	}

	public dataEstoqueProduto(produto: number, empresa: number): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`compra-estoque/data-estoque-produto/${produto}/${empresa}`
		);
	}
}
