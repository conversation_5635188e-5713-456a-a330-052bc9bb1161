export class CompraEstoque {
	codigo?: number;
	fornecedor?: number;
	numeroNotaFiscal?: string;
	valorTotal?: number;
	contato?: string;
	telefoneContato?: string;
	dataEmissao?: Date;
	dataCompra?: Date;
	anexarDocumento?: string;
	observacoes?: string;
	produtos?: CompraEstoqueProduto[];
}

export class CompraEstoqueProduto {
	codigo?: number;
	produto?: number;
	quantidade?: number;
	valorUnitario?: number;
	desconto?: number;
	total?: number;
	pontos?: number;
	opcoes?: string;
}

export interface FiltroCompraEstoque {
	fornecedor?: number;
	numeroNotaFiscal?: string;
	dataInicial?: Date;
	dataFinal?: Date;
}
