export interface FiltroDescontoOcupacao {
	modalidade?: number;
	turma?: number;
	professor?: number;
	ambiente?: number;
	periodoInicial?: Date;
	periodoFinal?: Date;
	diasSemana?: string[];
	horarios?: string[];
}

export interface DescontoOcupacaoItem {
	contrato: number;
	dataContrato: Date;
	aluno: string;
	turma: string;
	professor: string;
	diasDaSemana: string;
	inicio: string;
	fim: string;
	vagas: number;
	percentualDesconto: number;
}

export interface DescontoOcupacaoResponse {
	items: DescontoOcupacaoItem[];
	totalRegistros: number;
}
