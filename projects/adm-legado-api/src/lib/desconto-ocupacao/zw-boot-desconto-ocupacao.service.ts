import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { FiltroDescontoOcupacao } from "./desconto-ocupacao.model";

@Injectable({
	providedIn: "root",
})
export class ZwBootDescontoOcupacaoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {
	}

	public pesquisarDescontoOcupacao(
		filtro: FiltroDescontoOcupacao,
	): Observable<any> {
		const params = {
			filters: JSON.stringify(filtro),
		};
		return this.admZwBootApiBaseService.get(`desconto-ocupacao/pesquisar`, {
			params,
		});
	}

	public consultar(filtro: any, paramsAdd: any): Observable<any> {
		const params = {
			filters: JSON.stringify(filtro),
			...paramsAdd,
		};
		return this.admZwBootApiBaseService.get(`desconto-ocupacao/consultar`, {
			params,
		});
	}

	public modalidades(): Observable<any> {
		return this.admZwBootApiBaseService.get(`desconto-ocupacao/modalidades`);
	}

	public professores(): Observable<any> {
		return this.admZwBootApiBaseService.get(`desconto-ocupacao/professores`);
	}

	public ambientes(): Observable<any> {
		return this.admZwBootApiBaseService.get(`desconto-ocupacao/ambientes`);
	}

	public turmas(modalidadeId: string): Observable<any> {
		const params = {
			modalidadeId,
		};
		return this.admZwBootApiBaseService.get(`desconto-ocupacao/turmas`, {
			params,
		});
	}
}
