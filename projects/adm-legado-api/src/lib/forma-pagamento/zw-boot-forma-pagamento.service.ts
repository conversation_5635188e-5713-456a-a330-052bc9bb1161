import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { FormaPagamento } from "./forma-pagamento.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootFormaPagamentoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(): Observable<any> {
		return this.admZwBootApiBaseService.get(`forma-pagamento`);
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(`forma-pagamento/${id}`);
	}

	public save(formaPagamento: FormaPagamento): Observable<any> {
		return this.admZwBootApiBaseService.post(`forma-pagamento`, formaPagamento);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(`forma-pagamento/${id}`);
	}

	public tiposFormaPagamento(): Observable<any> {
		return this.admZwBootApiBaseService.get(`forma-pagamento/tipos`);
	}

	public convenios(): Observable<any> {
		return this.admZwBootApiBaseService.get(`forma-pagamento/convenios`);
	}
}
