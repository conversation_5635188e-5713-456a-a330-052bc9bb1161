import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";

@Injectable({
	providedIn: "root",
})
export class ZwBootPedidosPinpadService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public consultar(filtro: any, paramsAdd: any): Observable<any> {
		const params = {
			filters: JSON.stringify(filtro),
			...paramsAdd,
		};
		return this.admZwBootApiBaseService.get(`pedidos-pinpad/consultar`, {
			params,
		});
	}

	public consultarPorCodigo(codigo: number): Observable<any> {
		return this.admZwBootApiBaseService.get(`pedidos-pinpad/find/${codigo}`);
	}

	public atualizar(body: any): Observable<any> {
		return this.admZwBootApiBaseService.post(`pedidos-pinpad/atualizar`, body);
	}
}
