export class TaxaComissao {
	codigo: number;
	empresa: {
		codigo: number;
		nome: string;
	};
	situacao: string; // MA, RE, RN
	vigenciaInicial: string; // MM/yyyy
	vigenciaFinal: string; // MM/yyyy
	duracao: number; // meses
	valorFixoEspontaneo: number;
	valorFixoAgendado: number;
	porcentagemEspontaneo: number;
	porcentagemAgendado: number;
	comissaoMetas?: ComissaoMeta[];
	ativo: boolean;
}

export class ComissaoMeta {
	nome: string; // "1ª Meta", "2ª Meta", etc.
	valorEspontaneo: number;
	valorAgendado: number;
	porcentagemEspontaneo: number;
	porcentagemAgendado: number;
}
