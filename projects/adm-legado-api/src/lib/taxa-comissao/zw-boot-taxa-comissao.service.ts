import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { TaxaComissao } from "./taxa-comissao.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootTaxaComissaoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(): Observable<any> {
		return this.admZwBootApiBaseService.get(`taxa-comissao`);
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(`taxa-comissao/${id}`);
	}

	public save(taxaComissao: TaxaComissao): Observable<any> {
		return this.admZwBootApiBaseService.post(`taxa-comissao`, taxaComissao);
	}

	public saveMultiple(taxasComissao: TaxaComissao[]): Observable<any> {
		return this.admZwBootApiBaseService.post(
			`taxa-comissao/multiple`,
			taxasComissao
		);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(`taxa-comissao/${id}`);
	}

	public verificarComissaoMeta(empresaId: number): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`taxa-comissao/verificar-meta/${empresaId}`
		);
	}

	public empresas(): Observable<any> {
		return this.admZwBootApiBaseService.get(`taxa-comissao/empresas`);
	}

	public duracoesPossiveis(): Observable<any> {
		return this.admZwBootApiBaseService.get(`taxa-comissao/duracoes`);
	}
}
