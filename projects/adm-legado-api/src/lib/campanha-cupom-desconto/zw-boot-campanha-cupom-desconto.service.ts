import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { Observable } from "rxjs";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootCampanhaCupomDescontoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(): Observable<any> {
		return this.admZwBootApiBaseService.get(`campanha-cupom-desconto`);
	}

	public parcelas(): Observable<any> {
		return this.admZwBootApiBaseService.get(`campanha-cupom-desconto/parcelas`);
	}

	public planos(): Observable<any> {
		return this.admZwBootApiBaseService.get(`campanha-cupom-desconto/planos`);
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(`campanha-cupom-desconto/${id}`);
	}

	public save(cupomDesconto: any): Observable<any> {
		return this.admZwBootApiBaseService.post(`campanha-cupom-desconto`, cupomDesconto);
	}

	public salvarLoteCupom(novoCupom: any): Observable<any> {
		return this.admZwBootApiBaseService.post(`campanha-cupom-desconto/novo-lote-cupom`, novoCupom);
	}

	public historicoCupom(dtoConsulta: any): Observable<any> {
		return this.admZwBootApiBaseService.post(`campanha-cupom-desconto/historico-cupom`, dtoConsulta);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(`campanha-cupom-desconto/${id}`);
	}

	public convenios(): Observable<any> {
		return this.admZwBootApiBaseService.get(`campanha-cupom-desconto/convenios`);
	}
}
