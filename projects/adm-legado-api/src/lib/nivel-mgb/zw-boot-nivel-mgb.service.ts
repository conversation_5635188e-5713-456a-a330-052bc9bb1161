import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootNivelMgbService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public consultar(): Observable<any> {
		return this.admZwBootApiBaseService.get(`nivel-mgb/consultar`);
	}
}
