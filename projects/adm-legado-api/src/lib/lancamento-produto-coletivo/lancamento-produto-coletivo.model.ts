import { Modalidade } from "../models/modalidade.model";
import { Plano } from "../models/plano.model";
import { Empresa, User } from "../models/usuario.model";
import { Produto } from "../produto/produto.model";

export class LancamentoProdutoColetivo {
	codigo: number;
	descricao: string;
	produto: Produto;
	empresa: Empresa;
	valor: number;
	tipoLancamento: TipoLancamentoProdutoColetivoEnum;
	dataEspecifica: Date;
	mes: number;
	parcela: number;
	plano: Plano;
	vigenciaContratoInicio: Date;
	vigenciaContratoFim: Date;
	modalidade: Modalidade;
	nrVezesParcelar: number;
	dataFim: Date;
	matriculas: string;
	lancarAoGravar: boolean;
	ignorarJaLancados: boolean;
	usuario: User;
	jaFoiLancado: boolean;
	codigoUsuarioAprovou: number;
}

export enum TipoLancamentoProdutoColetivoEnum {
	DATA_ESPECIFICA = "DATA_ESPECIFICA",
	MES = "MES",
	POR_PARCELA = "POR_PARCELA",
	CONTRATO_SEM_PRODUTO = "CONTRATO_SEM_PRODUTO",
}

export interface TipoLancamentoProdutoColetivo {
	value: string;
	label: string;
	labelId: string;
}

export const tipoLancamentoOptions =
	(): Array<TipoLancamentoProdutoColetivo> => [
		{
			value: TipoLancamentoProdutoColetivoEnum.DATA_ESPECIFICA,
			label: "Data Específica",
			labelId: "data-especifica",
		},
		{
			value: TipoLancamentoProdutoColetivoEnum.MES,
			label: "Mês",
			labelId: "mes",
		},
		{
			value: TipoLancamentoProdutoColetivoEnum.POR_PARCELA,
			label: "Por Parcela",
			labelId: "por-parcela",
		},
		{
			value: TipoLancamentoProdutoColetivoEnum.CONTRATO_SEM_PRODUTO,
			label: "Contrato sem Produto",
			labelId: "contrato-sem-produto",
		},
	];
