import { HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { ApiResponseList } from "../base/base.model";
import { AmostraCliente } from "../models/amostra-cliente.model";
import { LancamentoProdutoColetivo } from "./lancamento-produto-coletivo.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootLancamentoProdutoColetivoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(): Observable<any> {
		return this.admZwBootApiBaseService.get(`lancamento-produto-coletivo`);
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`lancamento-produto-coletivo/${id}`
		);
	}

	public save(
		lancamentoProdutoColetivo: LancamentoProdutoColetivo
	): Observable<any> {
		return this.admZwBootApiBaseService.post(
			`lancamento-produto-coletivo`,
			lancamentoProdutoColetivo
		);
	}

	public update(
		lancamentoProdutoColetivo: LancamentoProdutoColetivo
	): Observable<any> {
		return this.admZwBootApiBaseService.put(
			`lancamento-produto-coletivo/${lancamentoProdutoColetivo.codigo}`,
			lancamentoProdutoColetivo
		);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(
			`lancamento-produto-coletivo/${id}`
		);
	}

	public amostraClientes(
		lancamento: LancamentoProdutoColetivo,
		params: {
			ignorarJalancados?: boolean;
			page?: number;
			size?: number;
		}
	): Observable<ApiResponseList<AmostraCliente>> {
		if (!params.page) {
			params.page = 1;
		}
		if (!params.size) {
			params.size = 10;
		}

		if (params.ignorarJalancados === undefined) {
			params.ignorarJalancados = false;
		}

		const httpParams: HttpParams = new HttpParams()
			.set("page", `${params.page}`)
			.set("size", `${params.size}`)
			.set("ignorarJaLancados", `${params.ignorarJalancados}`);

		return this.admZwBootApiBaseService.post(
			`lancamento-produto-coletivo/amostra-clientes`,
			lancamento,
			{
				params: httpParams,
			}
		);
	}

	public clientesJaAlcancados(
		codigoLancamento: number,
		params: {
			page?: number;
			size?: number;
		}
	): Observable<ApiResponseList<AmostraCliente>> {
		if (!params.page) {
			params.page = 1;
		}
		if (!params.size) {
			params.size = 10;
		}

		const httpParams: HttpParams = new HttpParams()
			.set("page", `${params.page}`)
			.set("size", `${params.size}`);

		return this.admZwBootApiBaseService.get(
			`lancamento-produto-coletivo/${codigoLancamento}/clientes-ja-alcancados`,
			{
				params: httpParams,
			}
		);
	}

	public estornarProdutosLancados(
		codigoLancamento: number,
		lancamentoProdutoColetivo: LancamentoProdutoColetivo
	): Observable<ApiResponseList<AmostraCliente>> {
		return this.admZwBootApiBaseService.post(
			`lancamento-produto-coletivo/${codigoLancamento}/estornar-produtos`,
			lancamentoProdutoColetivo
		);
	}
}
