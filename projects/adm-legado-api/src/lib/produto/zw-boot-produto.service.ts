import { HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { ApiResponseList } from "../base/base.model";
import { Produto, ProdutoFiltro } from "./produto.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootProdutoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(): Observable<any> {
		return this.admZwBootApiBaseService.get(`produto`);
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(`produto/${id}`);
	}

	public save(produto: Produto): Observable<any> {
		return this.admZwBootApiBaseService.post(`produto`, produto);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(`produto/${id}`);
	}

	public empresas(): Observable<any> {
		return this.admZwBootApiBaseService.get(`produto/empresas`);
	}

	public convenios(): Observable<any> {
		return this.admZwBootApiBaseService.get(`produto/convenios`);
	}

	public categoriasProduto(): Observable<any> {
		return this.admZwBootApiBaseService.get(`produto/categorias`);
	}

	public tiposProduto(): Observable<any> {
		return this.admZwBootApiBaseService.get(`produto/tipos`);
	}

	/**
	 * Consulta produtos com paginação
	 * @param params Filtros e dados de paginação
	 * @return Lista paginada de produtos com dados mínimos para ser utilizado em combos
	 */
	public consultarMinimal(params: {
		filters: ProdutoFiltro;
		page?: number;
		size?: number;
		orderBy?: string;
		orderDirection?: "ASC" | "DESC";
	}): Observable<ApiResponseList<Produto>> {
		if (!params.page) {
			params.page = 1;
		}
		if (!params.size) {
			params.size = 10;
		}
		let httpParams: HttpParams = new HttpParams()
			.set("filters", JSON.stringify(params.filters))
			.set("page", `${params.page}`)
			.set("size", `${params.size}`);

		if (params.orderBy && params.orderDirection) {
			httpParams = httpParams.set(
				"sort",
				`${params.orderBy},${params.orderDirection}`
			);
		}

		return this.admZwBootApiBaseService.get<ApiResponseList<Produto>>(
			"produto/minimal",
			{
				params: httpParams,
			}
		);
	}
}
