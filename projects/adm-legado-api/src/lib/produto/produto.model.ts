export class Produto {
	codigo: number;
	descricao: string;
	tipoProduto: string;
	categoriaProduto: {
		codigo: number;
		descricao: string;
	};
	situacao: string;
	empresa: {
		codigo: number;
		nome: string;
	};
	convenio: {
		codigo: number;
		descricao: string;
	};
	valorAlunoDiarista: number;
	valorAlunoEmpresa: number;
	valorAlunoFinal: number;
	percentualMunicipal: number;
	percentualEstadual: number;
	percentualFederal: number;
	vigenciaInicial: Date;
	vigenciaFinal: Date;
	limitarAliquotaPisAoJuridico: boolean;
	entrarAliquotaCofinsAoJuridico: boolean;
	acrescentarNoFechaPacote: boolean;
	apresentarNoVendaOnline: boolean;
	quantidadeMaximaParcelas: number;
	mensagem: string;
}

export interface ProdutoFiltro {
	codigo?: number;
	descricao?: string;
	tipoProduto?: string;
	desativado?: boolean;
	categoriaProduto?: number;
	tipoVigencia?: string;
	dataInicioVigencia?: Date | string;
	dataFinalVigencia?: Date | string;
	codigoBarras?: string;
	valorMinimo?: number;
	valorMaximo?: number;
	unidadeMedida?: string;
	aparecerAulaCheia?: boolean;
	empresaId?: number;
	quicksearchValue?: string;
	tiposDesconsiderar?: string[];
	somenteComEstoque?: boolean;
	codigoEmpresa?: number;
}
