export interface FechamentoAcessos {
	totalAcessos: number;
	percentualClientesColaboradores: PercentualClientesColaboradores;
	estatisticasLiberacoes: EstatisticasLiberacoes;
	percentualJustificativas: number;
	detalhesAcessos: DetalheAcesso[];
}

export interface PercentualClientesColaboradores {
	percentualClientes: number;
	totalClientes: number;
	percentualColaboradores: number;
	totalColaboradores: number;
}

export interface EstatisticasLiberacoes {
	percentualLiberacoes: number;
	tiposLiberacao: TipoLiberacao[];
	totalBVs?: number;
}

export interface TipoLiberacao {
	tipo: string;
	percentualAcessos: number;
	totalAcessos: number;
	quantidadeJustificada: number;
	quantidadeFaltaJustificar: number;
}

export interface DetalheAcesso {
	codigo: number;
	dataHora: Date;
	local: string;
	coletor: string;
	sentido: string;
	tipoLiberacao: string;
	usuarioLiberou: string;
	pessoaAcesso: string;
	justificativa?: string;
	dataJustificativa?: Date;
	usuarioJustificou?: string;
}

export interface FiltroFechamentoAcessos {
	empresaId?: number;
	dataInicial: Date;
	dataFinal: Date;
	horaInicial?: string;
	horaFinal?: string;
}

export interface JustificativaAcesso {
	codigoAcesso: number;
	justificativa: string;
	codigoPessoa?: number;
}
