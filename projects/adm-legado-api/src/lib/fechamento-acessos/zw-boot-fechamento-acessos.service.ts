import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import {
	FechamentoAcessos,
	FiltroFechamentoAcessos,
	DetalheAcesso,
	JustificativaAcesso,
} from "./fechamento-acessos.model";

@Injectable({
	providedIn: "root",
})
export class ZwBootFechamentoAcessosService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public pesquisarFechamentoAcessos(
		filtro: FiltroFechamentoAcessos
	): Observable<any> {
		const params = {
			filters: JSON.stringify(filtro),
		};
		return this.admZwBootApiBaseService.get(`fechamento-acessos/pesquisar`, {
			params,
		});
	}

	public enviarEmail(filtro: FiltroFechamentoAcessos): Observable<any> {
		const params = {
			filters: JSON.stringify(filtro),
		};
		return this.admZwBootApiBaseService.get(`fechamento-acessos/enviar-email`, {
			params,
		});
	}

	imprimir(filters): Observable<any> {
		const params = {
			filters: JSON.stringify(filters),
		};
		return this.admZwBootApiBaseService.get(`fechamento-acessos/imprimir`, {
			params,
		});
	}

	public emails(): Observable<any> {
		return this.admZwBootApiBaseService.get(`fechamento-acessos/emails`);
	}

	public alterarEmails(emails): Observable<any> {
		return this.admZwBootApiBaseService.post(
			`fechamento-acessos/emails`,
			emails
		);
	}
}
