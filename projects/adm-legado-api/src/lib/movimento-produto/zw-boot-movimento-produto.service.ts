import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { MovimentoProduto } from "./movimento-produto.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootMovimentoProdutoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(): Observable<any> {
		return this.admZwBootApiBaseService.get(`movimento-produto`);
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(`movimento-produto/${id}`);
	}

	public save(movimentoProduto: MovimentoProduto): Observable<any> {
		return this.admZwBootApiBaseService.post(
			`movimento-produto`,
			movimentoProduto
		);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(`movimento-produto/${id}`);
	}

	public clientes(quickSearch: string): Observable<any> {
		const params = { quickSearch };
		return this.admZwBootApiBaseService.get(`movimento-produto/clientes`, {
			params,
		});
	}

	public produtos(): Observable<any> {
		return this.admZwBootApiBaseService.get(`movimento-produto/produtos`);
	}
}
