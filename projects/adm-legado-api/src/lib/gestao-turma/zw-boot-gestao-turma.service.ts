import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootGestaoTurmaService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	/**
	 * Lista turmas com filtros
	 * GET /gestao-turmas/turmas
	 */
	listaTurmas(filters: any, configs: any = {}): Observable<any> {
		const params = {
			filters: JSON.stringify(filters),
			configs: JSON.stringify(configs),
		};
		return this.admZwBootApiBaseService.get(`gestao-turmas/turmas`, {
			params,
		});
	}

	/**
	 * Lista alunos com filtros
	 * GET /gestao-turmas/alunos
	 */
	listaAlunos(filters: any, configs: any = {}): Observable<any> {
		const params = {
			filters: JSON.stringify(filters),
			configs: JSON.stringify(configs),
		};
		return this.admZwBootApiBaseService.get(`gestao-turmas/alunos`, {
			params,
		});
	}

	/**
	 * Transferir alunos
	 * POST /gestao-turmas/transferir?tipo=alunos
	 */
	transferirAlunos(body: any): Observable<any> {
		const params = { tipo: "alunos" };
		return this.admZwBootApiBaseService.post(`gestao-turmas/transferir`, body, {
			params,
		});
	}

	/**
	 * Transferir turmas
	 * POST /gestao-turmas/transferir?tipo=turmas
	 */
	transferirTurmas(body: any): Observable<any> {
		const params = { tipo: "turmas" };
		return this.admZwBootApiBaseService.post(`gestao-turmas/transferir`, body, {
			params,
		});
	}

	/**
	 * Lista modalidades
	 * GET /gestao-turma/modalidades
	 */
	modalidades(): Observable<any> {
		return this.admZwBootApiBaseService.get(`gestao-turma/modalidades`);
	}

	/**
	 * Lista turmas por modalidade
	 * GET /gestao-turma/turmas/{codModalidade}
	 */
	turmasByModalidade(codModalidade: number): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`gestao-turma/turmas/${codModalidade}`
		);
	}

	/**
	 * Lista horários por turma
	 * GET /gestao-turma/horarios/{codTurma}
	 */
	horariosByTurma(codTurma: number): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`gestao-turma/horarios/${codTurma}`
		);
	}

	/**
	 * Lista professores por modalidade
	 * GET /gestao-turma/professores/{codModalidade}
	 */
	professoresByModalidade(codModalidade: number): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`gestao-turma/professores/${codModalidade}`
		);
	}
}
