import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { Observable } from "rxjs";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootCampanhaClubeVantagensService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public findAll(filters: any, params: any): Observable<any> {
		return this.admZwBootApiBaseService.get(`campanha-clube-vantagens`, {
			params: {
				filters: JSON.stringify(filters),
				...params,
			},
		});
	}

	public find(id: number | string): Observable<any> {
		return this.admZwBootApiBaseService.get(`campanha-clube-vantagens/${id}`);
	}

	public save(campanha: any): Observable<any> {
		return this.admZwBootApiBaseService.post(
			`campanha-clube-vantagens`,
			campanha
		);
	}

	public delete(id: number): Observable<any> {
		return this.admZwBootApiBaseService.delete(
			`campanha-clube-vantagens/${id}`
		);
	}

	public tiposItemCampanha(): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`campanha-clube-vantagens/tipos-item-campanha`
		);
	}
}
