import { HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { ApiResponseList } from "../base/base.model";
import { Modalidade, ModalidadeFiltro } from "../models/modalidade.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootModalidadeService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	/**
	 * Consulta modalidades com paginação
	 * @param params Filtros e dados de paginação
	 * @return Lista paginada de modalidades com dados mínimos para ser utilizado em combos
	 */
	public consultarMinimal(params: {
		filters: ModalidadeFiltro;
		page?: number;
		size?: number;
		orderBy?: string;
		orderDirection?: "ASC" | "DESC";
	}): Observable<ApiResponseList<Modalidade>> {
		if (!params.page) {
			params.page = 1;
		}
		if (!params.size) {
			params.size = 10;
		}
		let httpParams: HttpParams = new HttpParams()
			.set("filters", JSON.stringify(params.filters))
			.set("page", `${params.page}`)
			.set("size", `${params.size}`);

		if (params.orderBy && params.orderDirection) {
			httpParams = httpParams.set(
				"sort",
				`${params.orderBy},${params.orderDirection}`
			);
		}

		return this.admZwBootApiBaseService.get<ApiResponseList<Modalidade>>(
			"modalidade/minimal",
			{
				params: httpParams,
			}
		);
	}
}
