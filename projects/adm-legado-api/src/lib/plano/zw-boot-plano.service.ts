import { HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import { ApiResponseList } from "../base/base.model";
import { Plano, PlanoFiltro } from "../models/plano.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootPlanoService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	/**
	 * Consulta planos com paginação
	 * @param params Filtros e dados de paginação
	 * @return Lista paginada de planos com dados mínimos para ser utilizado em combos
	 */
	public consultarMinimal(params: {
		filters: PlanoFiltro;
		page?: number;
		size?: number;
		orderBy?: string;
		orderDirection?: "ASC" | "DESC";
	}): Observable<ApiResponseList<Plano>> {
		if (!params.page) {
			params.page = 1;
		}
		if (!params.size) {
			params.size = 10;
		}
		let httpParams: HttpParams = new HttpParams()
			.set("filters", JSON.stringify(params.filters))
			.set("page", `${params.page}`)
			.set("size", `${params.size}`);

		if (params.orderBy && params.orderDirection) {
			httpParams = httpParams.set(
				"sort",
				`${params.orderBy},${params.orderDirection}`
			);
		}

		return this.admZwBootApiBaseService.get<ApiResponseList<Plano>>(
			"plano/minimal",
			{
				params: httpParams,
			}
		);
	}
}
