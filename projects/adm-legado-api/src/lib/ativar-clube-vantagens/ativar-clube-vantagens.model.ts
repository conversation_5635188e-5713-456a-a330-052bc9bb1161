export interface AtivarClubeVantagensResponsavel {
	codigo: number;
	nome: string;
	email: string;
	userOamd: string;
}

export interface AtivarClubeVantagensRequest {
	responsavel: AtivarClubeVantagensResponsavel;
	nomeEmpresa: string;
	codigoEmpresa: number;
	podeAtivar: boolean;
}

export interface AtivarClubeVantagensResponse {
	meta: AtivarClubeVantagensMetaError | null;
	content: AtivarClubeVantagensRequest | null;
}

export interface AtivarClubeVantagensMetaError {
	erro: string;
	errorMessage: string;
}

export interface AbrirModalResponse {
	meta: null;
	content: null;
}
