import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import {
	AtivarClubeVantagensRequest,
	AtivarClubeVantagensResponse,
	AbrirModalResponse,
} from "./ativar-clube-vantagens.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootAtivarClubeVantagensService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	abrirModal(): Observable<AbrirModalResponse> {
		return this.admZwBootApiBaseService.post<AbrirModalResponse>(
			"ativar-clube-vantagens/abrir-modal",
			{}
		);
	}

	ativar(
		request: AtivarClubeVantagensRequest
	): Observable<AtivarClubeVantagensResponse> {
		return this.admZwBootApiBaseService.post<AtivarClubeVantagensResponse>(
			"ativar-clube-vantagens/ativar",
			request
		);
	}
}
