import { Injectable } from "@angular/core";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { Observable } from "rxjs";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class ZwBootComissaoConsultorService {
	constructor(private admZwBootApiBaseService: AdmZwBootApiBaseService) {}

	public gerarRelatorio(tipo: string, filtro: any): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`comissao-consultor/gerar-relatorio/${tipo}`,
			{
				params: {
					filters: JSON.stringify(filtro),
				},
			}
		);
	}

	public periodicidades(): Observable<any> {
		return this.admZwBootApiBaseService.get(
			`comissao-consultor/periodicidades`
		);
	}

	public configuracoes(): Observable<any> {
		return this.admZwBootApiBaseService.get(`comissao-consultor/configuracoes`);
	}

	public colaboradores(): Observable<any> {
		return this.admZwBootApiBaseService.get(`comissao-consultor/colaboradores`);
	}

	public consultores(): Observable<any> {
		return this.admZwBootApiBaseService.get(`comissao-consultor/consultores`);
	}
}
