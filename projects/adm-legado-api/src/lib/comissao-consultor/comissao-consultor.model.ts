export interface FiltroComissaoConsultor {
	empresa?: number;
	tipoDados?: string;
	dataInicio?: Date;
	dataFim?: Date;
	competencia?: string;
	somenteRecebimentoAPartir?: Date;
	somenteContratosLancadosAPartir?: Date;
	operadorResponsavel?: string;
	consultor?: number;
	atendente?: number;
	duracoes?: number[];
	tiposPagamento?: string[];
	impressaoPor?: string;
	modoVisualizacao?: string;
	valorComissoes?: string;
	considerarDataCompensacaoOriginal?: boolean;
	desconsiderarChequesEmPendencia?: boolean;
}

export interface Consultor {
	codigo: number;
	nome: string;
}

export interface Atendente {
	codigo: number;
	nome: string;
}

export interface Duracao {
	codigo: number;
	descricao: string;
	meses: number;
}

export interface ConfiguracaoEmpresa {
	temMetaFinanceira: boolean;
	temComissaoProdutos: boolean;
	mensagemMetaFinanceira?: string;
	mensagemComissaoProdutos?: string;
}
