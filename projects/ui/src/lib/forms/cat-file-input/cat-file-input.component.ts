import {
	Component,
	OnInit,
	Input,
	HostListener,
	ViewChild,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { Observable } from "rxjs";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-cat-file-input",
	templateUrl: "./cat-file-input.component.html",
	styleUrls: ["./cat-file-input.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatFileInputComponent implements OnInit {
	@Input() control: FormControl;
	@Input() nomeControl: FormControl;

	@Input() formatos = "jpeg, jpg, png, pdf, txt, xml, doc e docx";
	@Input() formatosValidos = new RegExp(
		"(jpeg|jpg|png|pdf|txt|doc|docx|xml)$",
		"i"
	);

	@ViewChild("fileInput", { static: true }) fileInput;
	@Input() id: string;

	@Input() urlImage: string;
	@Input() imgAlt = "Imagem selecionada";
	@Input() imageHeight;
	@Input() imageWidth;

	controlFc: FormControl = new FormControl();
	filename: string;

	constructor(
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.setId();
		if (typeof this.formatosValidos === "string") {
			this.formatosValidos = new RegExp(this.formatosValidos, "i");
		}
		this.controlFc.valueChanges.subscribe(() => {
			if (this.files && this.files.length) {
				if (this.verificarExtensaoUpload(this.files.item(0).name)) {
					this.filename = this.files.item(0).name;
					this.nomeControl.setValue(this.files.item(0).name);
					this.getFileAsText(this.files[0]).subscribe((value) => {
						this.control.setValue(value);
					});
				}
			}
		});
		this.cd.detectChanges();
	}

	verificarExtensaoUpload(dto) {
		const formatoArquivo = dto.split(".");
		if (formatoArquivo[formatoArquivo.length - 1].match(this.formatosValidos)) {
			return true;
		} else {
			this.snotifyService.warning(
				"Arquivo inválido - Verifique a lista de formatos permitidos e tente novamente"
			);
			this.filename = null;
			this.controlFc.reset();
			this.control.reset();
			this.nomeControl.reset();
			return false;
		}
	}

	private setId() {
		if (!this.id) {
			const rdm = Math.trunc(Math.random() * 1000);
			this.id = `pacto-file-input-${rdm}`;
		}
	}

	@HostListener("dragover", ["$event"]) onDragOver(evt) {
		evt.preventDefault();
		evt.stopPropagation();
	}

	@HostListener("dragleave", ["$event"])
	public onDragLeave(evt) {
		evt.preventDefault();
		evt.stopPropagation();
	}

	@HostListener("drop", ["$event"])
	public ondrop(evt) {
		evt.preventDefault();
		evt.stopPropagation();
		const files = evt.dataTransfer.files;
		if (files && files.length) {
			this.filename = files.item(0).name;
			this.nomeControl.setValue(files.item(0).name);
			this.getFileAsText(files.item(0)).subscribe((text) => {
				this.control.setValue(text);
			});
		}
	}

	get hasFile() {
		return (this.control && this.control.value) || this.urlImage;
	}

	get files(): FileList {
		return this.fileInput.nativeElement.files;
	}

	private getFileAsText(file): Observable<string> {
		const reader = new FileReader();
		return new Observable((observer) => {
			reader.readAsDataURL(file);
			reader.onload = (e: any) => {
				observer.next(e.target.result);
				observer.complete();
				this.cd.detectChanges();
			};
		});
	}

	removeHandler($event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();
		this.filename = null;
		this.urlImage = null;
		this.controlFc.reset();
		this.control.reset();
		this.nomeControl.reset();
	}

	reset() {
		this.filename = null;
		this.urlImage = null;
		this.controlFc.reset();
		this.control.reset();
		this.nomeControl.reset();
		this.cd.detectChanges();
	}
}
