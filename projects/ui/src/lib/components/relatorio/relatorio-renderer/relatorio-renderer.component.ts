import {
	Component,
	ChangeDetectionStrategy,
	Input,
	Output,
	EventEmitter,
	ViewChild,
	ChangeDetectorRef,
} from "@angular/core";

import {
	PactoDataGridColumnConfig,
	PactoDataGridOrdenacaoDirecao,
	PactoDataGridConfig,
	PactoActionConfig,
} from "../data-grid.model";

import { trigger } from "@angular/animations";
import { fadeIn } from "../../../pacto-animations";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import { Observable } from "rxjs";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";

declare var $;

@Component({
	// tslint:disable-next-line:component-selector
	selector: "pacto-relatorio-renderer",
	templateUrl: "./relatorio-renderer.component.html",
	styleUrls: ["./relatorio-renderer.component.scss"],
	changeDetection: ChangeDetectionStrategy.Default,
	animations: [trigger("fadeIn", fadeIn(":enter"))],
})
export class RelatorioRendererComponent {
	@Input() dataGridConfig: PactoDataGridConfig;
	@Input() data: Array<any>;
	@Input() loading = false;
	@Input() customContent;
	@Input() customEmptyContent;
	@Input() rawDataIcons: Array<Array<PactoActionConfig>>;
	@Input() rawDropdownActionItems: Array<Array<PactoActionConfig>>;
	@Input() emptyStateMessage: string;
	@Input() emptyStateDescription: string;
	@Input() alternatingColors: "first" | "second" | "none" = "none";
	@Input() actionTitulo: string;
	@Input() dropDownActionTitulo: string;
	@Input() enableZebraStyle = false;
	@Input() enableDs3 = false;
	@Input() nameFieldHeader: string;
	@Input() nameEnumHeaderField: string;
	@Input() iconEnumHeaderField: string;
	@Input() iconFieldHeader: string;
	@Input() nameFirstFieldContent: string;
	@Input() nameSecondFieldContent: string;
	@Input() iconFieldfooterleft: string;
	@Input() nameFieldfooterleft: string;
	@Input() iconFieldFirstfooterright: string;
	@Input() nameFieldFirstfooterright: string;
	@Input() nameFieldFirstObjectfooterright: string;
	@Input() iconFieldSecondfooterright: string;
	@Input() nameFieldSecondfooterright: string;
	@Input() customRows = false;
	@Input() idSuffix;

	@Output() sort: EventEmitter<{
		column: string;
		direction: string;
	}> = new EventEmitter();
	@Output() cellClick: EventEmitter<{
		row: any;
		column: any;
	}> = new EventEmitter();
	@Output() iconClick: EventEmitter<{
		row: any;
		iconName: string;
		rowIndex: any;
	}> = new EventEmitter();
	@Output() rowClick: EventEmitter<any> = new EventEmitter();
	@Output() rowCheck: EventEmitter<{
		row: any;
		checked: boolean;
	}> = new EventEmitter();
	@Output() reload: EventEmitter<string> = new EventEmitter();

	@Output() ds3SelectAllEvent: EventEmitter<boolean> = new EventEmitter();

	@ViewChild("dropdownActions", { static: false }) dropdownActions: NgbDropdown;

	constructor(private cd: ChangeDetectorRef) {}

	get dados() {
		if (!this.data) {
			this.data = [];
		}
		return this.data;
	}

	get visibleColumns() {
		return this.dataGridConfig.columns.filter((column) => {
			if (column.visible != null) {
				return column.visible;
			} else {
				return column.defaultVisible;
			}
		});
	}

	rowChecked(id): boolean {
		return (
			this.dataGridConfig.allCheck === true ||
			this.dataGridConfig.checkeds.includes(id)
		);
	}

	onDrop($event: CdkDragDrop<string[]>) {
		if (this.dataGridConfig.ordenable) {
			moveItemInArray(this.data, $event.previousIndex, $event.currentIndex);
		}

		if (this.dataGridConfig.ordenable && this.dataGridConfig.onDragEnd) {
			this.dataGridConfig.onDragEnd($event, this.data);
		}
	}

	checkRow(row) {
		const id = row[this.dataGridConfig.valueRowCheck];
		if (this.dataGridConfig.checkeds.includes(id)) {
			const index = this.dataGridConfig.checkeds.indexOf(id, 0);
			if (index > -1) {
				this.dataGridConfig.checkeds.splice(index, 1);
				this.rowCheck.emit({
					row,
					checked: false,
				});
			}
		} else {
			this.dataGridConfig.checkeds.push(id);
			this.rowCheck.emit({
				row,
				checked: true,
			});
		}
		this.cd.detectChanges();
	}

	rowClickHandler(row, $event, rowIndex) {
		const $target = $($event.target);
		const actionClick = $target.closest(".action-cell").length;
		if (!actionClick && this.dataGridConfig.rowClick) {
			row.rowIndex = rowIndex;
			this.rowClick.emit(row);
		}
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else if (type === "string") {
			return false;
		} else {
			return true;
		}
	}

	getSortIconClass(column: string) {
		const ordenada = this.dataGridConfig.state.ordenacaoColuna === column;
		const asc =
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.ASC;
		const desc =
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.DESC;
		const ordenavel = this.dataGridConfig.columns.find((c) =>
			c.orderColumn ? c.orderColumn === column : c.nome === column
		).ordenavel;
		if (ordenada && asc) {
			return "pct pct-caret-up";
		} else if (ordenada && desc) {
			return "pct pct-caret-down";
		} else if (ordenavel) {
			return "pct pct-drop-down";
		}
	}

	iconClickHandler(row: any, icon: PactoActionConfig, rowIndex) {
		const rowInfo = {
			row,
			iconName: icon.nome,
			rowIndex,
		};
		this.iconClick.emit(rowInfo);

		if (icon.actionFn) {
			const returnAction = icon.actionFn(rowInfo);
			if (returnAction instanceof Observable) {
				returnAction.subscribe((reload) => {
					if (reload) {
						this.reload.emit("actionFn");
					}
				});
			}
		}
	}

	isSortable(column: string) {
		return this.dataGridConfig.columns.find((i) =>
			i.orderColumn ? i.orderColumn === column : i.nome === column
		).ordenavel;
	}

	sortClick(column: PactoDataGridColumnConfig) {
		if (
			this.isSortable(column.orderColumn ? column.orderColumn : column.nome)
		) {
			this.updateSortState(column);
			this.sort.emit({
				column: this.dataGridConfig.state.ordenacaoColuna,
				direction: this.dataGridConfig.state.ordenacaoDirecao,
			});
		}
	}

	columnCellClickHandler(row, column) {
		this.cellClick.emit({ row, column });
	}

	private updateSortState(column: PactoDataGridColumnConfig) {
		if (!column.ordenavel) {
			return false;
		}
		const colunaOrdernar = column.orderColumn
			? column.orderColumn
			: column.nome;
		if (this.dataGridConfig.state.ordenacaoColuna !== colunaOrdernar) {
			this.dataGridConfig.state.ordenacaoColuna = colunaOrdernar;
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.ASC;
		} else if (
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.ASC
		) {
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.DESC;
		} else if (
			this.dataGridConfig.state.ordenacaoDirecao ===
				PactoDataGridOrdenacaoDirecao.DESC &&
			this.enableDs3
		) {
			this.dataGridConfig.state.ordenacaoDirecao = undefined;
		} else {
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.ASC;
		}
	}

	isUndefinedOrNullOrEmpty(value: any): boolean {
		return value === undefined || value === null || value === "";
	}

	checkHtmlTags(value: string): boolean {
		const regExp = new RegExp(/<\/?[a-z][\s\S]*>/i);
		return regExp.test(value);
	}

	ds3SelectAll() {
		this.ds3SelectAllEvent.emit(true);
	}
	ds3ClearAll() {
		this.ds3SelectAllEvent.emit(false);
	}
}
