import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { CdkPortal } from "@angular/cdk/portal";
import { HttpClient } from "@angular/common/http";
import {
	AfterViewChecked,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
	forwardRef,
	OnChanges,
	SimpleChanges,
	ChangeDetectorRef,
} from "@angular/core";
import {
	ControlValueAccessor,
	FormControl,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { Observable, Subscription, of } from "rxjs";
import { catchError, debounceTime, map } from "rxjs/operators";

export interface Ds3SelectOptionModel {
	value: string;
	name: string;
}
export type Ds3SelectFilterParamBuilder = (filter: string) => {
	[param: string]: string | string[];
};

export type Ds3SelectFilterResponseParser = (result: any) => any[];

@Component({
	selector: "ds3-select",
	templateUrl: "./ds3-select.component.html",
	styleUrls: ["./ds3-select.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3SelectComponent),
			multi: true,
		},
	],
})
export class Ds3SelectComponent
	implements
		OnInit,
		AfterViewChecked,
		OnChanges,
		OnDestroy,
		ControlValueAccessor
{
	selectedValue: any = "";

	public isOpen: boolean = false;
	public searchControl = new FormControl();
	public tempOptions;

	public isLoading;

	public onChanged: Function;
	public onTouched: Function;

	private overlayRef!: OverlayRef;

	@ViewChild(CdkPortal, { static: true }) portal!: CdkPortal;

	@ViewChild("ds3Select", { static: true })
	selectElement: ElementRef;

	@ViewChild("ds3SelectSearch", { static: true })
	searchElement: ElementRef;

	@Input()
	public placeholder?: string = "Selecione um item";

	@Input()
	public options: any[] = [];

	@Input()
	public valueKey: string = "value";

	@Input()
	public emptyMsg: string = "Nenhuma opção encontrada.";

	@Input()
	public nameKey: string = "name";

	@Input()
	public disabled: boolean;

	@Input()
	public remove: boolean = false;

	@Input()
	endpointUrl;

	@Input()
	paramBuilder: Ds3SelectFilterParamBuilder;

	@Input()
	responseParser: Ds3SelectFilterResponseParser;

	@Input()
	additionalFilters: any;

	@Input()
	initOption: any;

	@Input()
	addEmptyOption = false;

	@Input()
	useFullOption = false;

	@Output()
	valueChanges = new EventEmitter();

	@Output() opened: EventEmitter<any> = new EventEmitter<any>();

	@Output()
	searchEvent: EventEmitter<{ term: string }> = new EventEmitter();

	private searchSubscription: Subscription;

	constructor(
		private http: HttpClient,
		private overlay: Overlay,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.updateTempOptions();
		this.searchControl.valueChanges
			.pipe(debounceTime(300))
			.subscribe((term) => this.search(term));
		this.search(null, this.selectedValue);
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.options && changes.options.currentValue) {
			const options = changes.options;
			if (!options.firstChange) {
				this.options = options.currentValue;
				this.updateTempOptions();
			}
		}
	}

	ngOnDestroy(): void {
		if (this.isOpen) {
			this.hide();
		}
	}

	private updateTempOptions() {
		if (this.options) {
			this.tempOptions = [...this.options];
		}
	}

	toggleDropdown(): void {
		if (this.isOpen) {
			this.hide();
			return;
		}
		if (this.remove && this.selectedValue) {
			this.selectValue(null);
			return;
		}
		this.showDropdown();
	}

	showDropdown(): void {
		if (this.disabled || this.isOpen) {
			return;
		}
		if (!this.overlayRef) {
			this.overlayRef = this.overlay.create(this.getOverlayConfig());
		}
		this.overlayRef.attach(this.portal);
		this.syncWidth();
		this.isOpen = true;
		this.opened.emit(this.isOpen);
	}

	private hide(): void {
		this.overlayRef.detach();
		this.isOpen = false;
		this.opened.emit(this.isOpen);
	}

	@HostListener("document:click", ["$event", "$event.target"])
	clickedOutside(e: MouseEvent, targetElement: HTMLElement) {
		if (!this.isOpen) {
			return;
		}

		if (!this.isDescendant(this.selectElement.nativeElement, targetElement)) {
			this.hide();
		}
	}

	ngAfterViewChecked() {
		if (this.isOpen) {
			this.searchElement.nativeElement.focus();
		}
	}

	private getOverlayConfig(): OverlayConfig {
		const positionStrategy = this.overlay
			.position()
			.flexibleConnectedTo(this.selectElement.nativeElement)
			.withPush(true)
			.withPositions([
				{
					originX: "start",
					originY: "bottom",
					overlayX: "start",
					overlayY: "top",
					offsetY: 4,
				},
				{
					originX: "end",
					originY: "bottom",
					overlayX: "end",
					overlayY: "top",
					offsetY: 4,
				},
				{
					originX: "start",
					originY: "top",
					overlayX: "start",
					overlayY: "bottom",
					offsetY: -4,
				},
				{
					originX: "end",
					originY: "top",
					overlayX: "end",
					overlayY: "bottom",
					offsetY: -4,
				},
			]);

		const scrollStrategy = this.overlay.scrollStrategies.reposition();
		return new OverlayConfig({
			positionStrategy: positionStrategy,
			scrollStrategy: scrollStrategy,
			hasBackdrop: false,
			backdropClass: "cdk-overlay-transparent-backdrop",
		});
	}

	private syncWidth(): void {
		if (!this.overlayRef) {
			return;
		}
		const refRectWidth =
			this.selectElement.nativeElement.getBoundingClientRect().width;
		this.overlayRef.updateSize({ width: refRectWidth });
	}

	search(term, initValue = null, init = false) {
		if (this.endpointUrl) {
			this.isLoading = true;
			this.searchSubscription = this.fetchData(term).subscribe((result) => {
				this.isLoading = false;
				if (result.content) {
					this.options = result.content;
				} else {
					this.options = result;
				}

				if (this.initOption && this.options && Array.isArray(this.options)) {
					const existOption = this.options.some(
						(option) => option[this.valueKey] === this.initOption[this.valueKey]
					);
					if (!existOption) {
						this.options.push(this.initOption);
					}
				}

				if (this.addEmptyOption) {
					const emptyOption: any = {};
					emptyOption[this.valueKey] = "";
					emptyOption[this.nameKey] = "-";
					this.options.unshift(emptyOption);
				}
				if (initValue) {
					if (initValue instanceof Object) {
						initValue = initValue[this.valueKey];
					}
					this.selectValue(
						this.options.find((option) => option[this.valueKey] === initValue)
					);
				}
				this.cd.detectChanges();
			});
			return;
		}

		if (this.tempOptions) {
			const temp = this.tempOptions.filter((x) => {
				const lowercaseTerm = term ? term.toLowerCase() : term;

				if (
					x[this.nameKey].toLowerCase().indexOf(lowercaseTerm) !== -1 ||
					!lowercaseTerm
				) {
					return x;
				}
			});
			this.options = temp;
		}

		if (!this.endpointUrl && (!this.options || this.options.length === 0)) {
			this.searchEvent.emit({ term });
		}
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	private fetchData(term: string): Observable<any> {
		const url = this.endpointUrl;
		const params = this.paramBuilder ? this.paramBuilder(term) : {};

		if (this.additionalFilters) {
			const filters = JSON.parse(params.filters.toString());
			Object.keys(this.additionalFilters).forEach((key) => {
				filters[key] = this.additionalFilters[key];
			});
			params.filters = JSON.stringify(filters);
		}

		const data = this.http.get(url, { params });

		return data.pipe(
			map((result) => {
				if (this.responseParser) {
					return this.responseParser(result);
				} else {
					return result;
				}
			}),
			catchError(() => {
				if (this.responseParser) {
					return of(this.responseParser([]));
				} else {
					return of([]);
				}
			})
		);
	}

	selectValue(value: any): void {
		if (value && value.disabled) {
			return;
		}
		this.writeValue(value);
		this.onChanged(this.selectedValue);
		this.hide();
		this.onTouched();
		this.valueChanges.emit(value);
	}

	registerOnChange(angularProvidedFunction: any): void {
		this.onChanged = angularProvidedFunction;
	}

	registerOnTouched(angularProvidedFunction: any): void {
		this.onTouched = angularProvidedFunction;
	}

	setDisabledState(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}

	writeValue(value: any): void {
		if (value === undefined || value === "") {
			return;
		}
		if (this.useFullOption) {
			if (typeof value === "object") {
				this.selectedValue = value;
				return;
			}
			const foundOption = this.options.find((x) => x[this.valueKey] === value);
			this.selectedValue = foundOption;
			return;
		}
		this.selectedValue = value;
	}

	isValueSelected(value: any) {
		return this.selectedValue == value;
	}

	get valueSearch(): boolean {
		return (
			this.searchControl &&
			this.searchControl.value &&
			this.searchControl.value !== ""
		);
	}
}
