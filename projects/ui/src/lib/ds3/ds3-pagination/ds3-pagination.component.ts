import {
	Component,
	EventEmitter,
	Input,
	OnChanges,
	Output,
	SimpleChanges,
} from "@angular/core";

@Component({
	selector: "ds3-pagination",
	templateUrl: "./ds3-pagination.component.html",
	styleUrls: ["./ds3-pagination.component.scss"],
})
export class Ds3PaginationComponent implements OnChanges {
	@Input() length: number = 0;
	@Input() pageSize: number = 5;
	@Input() pageIndex: number = 1;
	@Input() showFirstLastButtons: boolean = true;
	@Input() pageSizeOptions: number[] = [5, 10, 25, 50];
	@Output() pageChange = new EventEmitter<number>();
	@Output() pageSizeChange = new EventEmitter<number>();

	pageSizeFormOptions = [];

	constructor() {}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.pageSizeOptions) {
			this.pageSizeFormOptions = this.getPageSizeFormOptions(
				changes.pageSizeOptions.currentValue
			);
		}
	}

	getPageSizeFormOptions(pageSizeOptions: number[]) {
		return pageSizeOptions.map((item) => {
			return {
				value: item,
				name: item.toString(),
			};
		});
	}

	/**
	 * Método chamado quando ocorre uma mudança de página.
	 * Emite o novo índice da página através do evento 'pageChange'.
	 * @param pageIndex O novo índice da página.
	 */
	onPageChange(pageIndex: number): void {
		this.pageIndex = pageIndex;
		this.pageChange.emit(pageIndex);
	}

	/**
	 * Método chamado para navegar para a página anterior, se possível.
	 */
	previousPage(): void {
		if (this.pageIndex > 0) {
			this.onPageChange(this.pageIndex - 1);
		}
	}

	/**
	 * Método chamado para navegar para a próxima página, se possível.
	 */
	nextPage(): void {
		const maxPageIndex = Math.ceil(this.length / this.pageSize);
		if (this.pageIndex < maxPageIndex) {
			this.onPageChange(this.pageIndex + 1);
		}
	}

	/**
	 * Método chamado para navegar para a primeira página.
	 */
	firstPage(): void {
		if (this.pageIndex > 0) {
			this.onPageChange(1);
		}
	}

	/**
	 * Método chamado para navegar para a última página.
	 */
	lastPage(): void {
		const maxPageIndex = Math.ceil(this.length / this.pageSize);
		if (this.pageIndex < maxPageIndex) {
			this.onPageChange(maxPageIndex);
		}
	}

	/**
	 * Método chamado quando ocorre uma mudança no tamanho da página.
	 * Atualiza o tamanho da página, emite o novo tamanho através do evento 'pageSizeChange'
	 * e navega para a primeira página.
	 * @param value O valor do novo tamanho da página.
	 */
	onPageSizeChange(value: any): void {
		this.pageSize = value;
		this.pageSizeChange.emit(this.pageSize);
		this.firstPage();
	}

	/**
	 * Calcula o número total de páginas com base na quantidade total de itens e no tamanho da página.
	 * @returns O número total de páginas.
	 */
	calculateTotalPages(): number {
		return Math.ceil(this.length / this.pageSize);
	}

	/**
	 * Retorna os números de página a serem exibidos na paginação com base no índice atual da página.
	 * O número de páginas exibidas é calculado dinamicamente para garantir que a página atual esteja centralizada,
	 * se possível, e que um máximo de 3 números de página seja exibido de cada vez.
	 * @returns Um array contendo os números de página a serem exibidos na paginação.
	 */
	getDisplayedNumbers(): number[] {
		// Calcula o número total de páginas
		const totalPages = this.calculateTotalPages();

		// Calcula o índice inicial para os números de página a serem exibidos
		let startIndex = Math.max(0, this.pageIndex - 2);

		// Calcula o índice final para os números de página a serem exibidos
		const endIndex = Math.min(startIndex + 3, totalPages);

		// Se o número de páginas exibidas for menor que 3, ajusta o início para manter 3 números de página exibidos
		if (endIndex - startIndex < 3) {
			startIndex = Math.max(0, endIndex - 3);
		}

		// Inicializa um array para armazenar os números de página a serem exibidos
		const displayedNumbers: number[] = [];

		// Preenche o array com os números de página a serem exibidos
		for (let i = startIndex; i < endIndex; i++) {
			displayedNumbers.push(i + 1);
		}

		// Retorna o array contendo os números de página a serem exibidos na paginação
		return displayedNumbers;
	}

	/**
	 * Determina se os controles de navegação para a esquerda devem ser habilitados.
	 * Os controles de navegação para a esquerda são habilitados se showFirstLastButtons for verdadeiro
	 * e a página atual não for a primeira.
	 * @returns Verdadeiro se os controles de navegação para a esquerda devem ser habiltados, falso caso contrário.
	 */
	get controlsLeft(): boolean {
		return this.showFirstLastButtons && this.pageIndex > 1;
	}

	/**
	 * Determina se os controles de navegação para a direita devem ser habilitados.
	 * Os controles de navegação para a direita são habilitados se showFirstLastButtons for verdadeiro,
	 * a página atual não for a última e o número total de páginas for maior que 1.
	 * @returns Verdadeiro se os controles de navegação para a direita devem ser habilitados, falso caso contrário.
	 */
	get controlsRight(): boolean {
		return (
			this.showFirstLastButtons &&
			this.pageIndex < this.calculateTotalPages() &&
			this.calculateTotalPages() > 1
		);
	}
}
