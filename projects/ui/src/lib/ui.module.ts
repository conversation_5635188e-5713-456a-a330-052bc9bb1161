import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { RouterModule } from "@angular/router";

import { TextMaskModule } from "angular2-text-mask";

import { PactoChartsModule } from "./charts/charts.module";
import { ComponentsModule } from "./components/components.module";
import { PactoFormsModule } from "./forms/pacto-forms.module";
import { LayoutModule } from "./layout/layout.module";

import { TranslateLoader, TranslateModule } from "@ngx-translate/core";
import { NgxCurrencyModule } from "ngx-currency";
import { AutorizacaoAcessoComponent } from "./autorizacao-acesso/autorizacao-acesso.component";
import { CatStepperModule } from "./components/cat-stepper/cat-stepper.module";
import { DialogModule } from "./dialog/dialog.module";
import { OnlyNumberDirective } from "./directives/only-number.directive";
import { CaptalizePipe } from "./pipes/captalize.pipe";
import { MarkPipe } from "./pipes/mark.pipe";
import { TranslationLoaderService } from "./translation-loader.service";
import { UiCommomModule } from "./ui-commom/ui-commom.module";
import { Uiv2Module } from "./uiv2/uiv2.module";
import { Ds3Module } from "./ds3/ds3.module";
import { DialogAutorizacaoAcessoComponent } from "./dialog-autorizacao-acesso/dialog-autorizacao-acesso.component";
import { DateRangePipe } from "./pipes/date-range/date-range.pipe";
import { ModalObrigatorioModule } from "./modal-obrigatorio/modal-obrigatorio.module";

@NgModule({
	imports: [
		CommonModule,
		FormsModule,
		TextMaskModule,
		MatDatepickerModule,
		ReactiveFormsModule,
		RouterModule.forChild([]),
		PactoFormsModule,
		LayoutModule,
		ComponentsModule,
		PactoChartsModule,
		TranslateModule.forRoot({
			defaultLanguage: "pt",
			loader: {
				provide: TranslateLoader,
				useClass: TranslationLoaderService,
			},
			isolate: true,
		}),
		DialogModule,
		CatStepperModule,
		NgxCurrencyModule,
		Uiv2Module,
		UiCommomModule,
		Ds3Module,
		ModalObrigatorioModule,
	],
	exports: [
		PactoFormsModule,
		LayoutModule,
		ComponentsModule,
		PactoChartsModule,
		DialogModule,
		CatStepperModule,
		OnlyNumberDirective,
		Uiv2Module,
		UiCommomModule,
		MarkPipe,
		CaptalizePipe,
		Ds3Module,
		DateRangePipe,
		ModalObrigatorioModule,
		NgxCurrencyModule,
	],
	entryComponents: [
		AutorizacaoAcessoComponent,
		DialogAutorizacaoAcessoComponent,
	],
	providers: [],
	declarations: [
		AutorizacaoAcessoComponent,
		DialogAutorizacaoAcessoComponent,
		MarkPipe,
		CaptalizePipe,
		DateRangePipe,
	],
})
export class UiModule {}
