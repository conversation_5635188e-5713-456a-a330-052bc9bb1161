import { NavModuleI18n } from "ui-kit";

const cadastroAdquirenteEn: NavModuleI18n = {
	cadastroAdquirente: {
		name: "Adquirente",
		description: "Cadastrar adquirente",
		searchTokens: "cadastro, adquirente",
	},
};

const cadastroImpostoProdutoEn: NavModuleI18n = {
	cadastroImpostoProduto: {
		name: "Imposto Produto",
		description: "Cadastrar imposto",
		searchTokens: "cadastro, imposto",
	},
};

const cadastroTamanhoArmarioEn: NavModuleI18n = {
	cadastroTamanhoArmario: {
		name: "<PERSON><PERSON><PERSON> Armário",
		description: "<PERSON><PERSON><PERSON> de Armário",
		searchTokens: "cadastro, tamanho, armário",
	},
};

const cadastroAmbienteEn: NavModuleI18n = {
	cadastroAmbiente: {
		name: "Ambiente",
		description: "Ambiente",
		searchTokens: "ambiente",
	},
};

const cadastroAutorizacaoAcessoEn: NavModuleI18n = {
	cadastroAutorizacaoAcesso: {
		name: "Autorização de Acesso",
		description: "Autorização de acesso",
		searchTokens: "autorização, acesso",
	},
};

const cadastroBalancoEn: NavModuleI18n = {
	cadastroBalanco: {
		name: "Balanço",
		description: "Balanço",
		searchTokens: "balanço, balanco",
	},
};

const cadastroBancoEn: NavModuleI18n = {
	cadastroBanco: {
		name: "Banco",
		description: "Banco",
		searchTokens: "banco",
	},
};

const cadastroBrindesEn: NavModuleI18n = {
	cadastroBrindes: {
		name: "Brindes",
		description: "Cadastro de brindes",
		searchTokens: "brindes, cadastro",
	},
};

const cadastroCampanhasEn: NavModuleI18n = {
	cadastroCampanhas: {
		name: "Campanha do Clube de Vantagens",
		description: "Campanha do Clube de Vantagens",
		searchTokens: "campanhas, cadastro, clube de vantagens",
	},
};

const cadastroPinPadEn: NavModuleI18n = {
	cadastroPinPad: {
		name: "Pinpad",
		description: "Pinpad",
		searchTokens: "pinpad, cadastro",
	},
};

const cadastroCardexEn: NavModuleI18n = {
	cadastroCardex: {
		name: "Cardex",
		description: "Cardex",
		searchTokens: "cardex",
	},
};

const cadastroCategoriaClienteEn: NavModuleI18n = {
	cadastroCategoriaCliente: {
		name: "Categoria de Clientes",
		description: "Categoria de clientes",
		searchTokens: "categoria, cliente, clientes",
	},
};

const grupoComDescontoEn: NavModuleI18n = {
	grupoComDesconto: {
		name: "Grupo Com Desconto",
		description: "Grupo Com Desconto",
		searchTokens: "grupo, desconto",
	},
};

const cadastroCategoriaProdutoEn: NavModuleI18n = {
	cadastroCategoriaProduto: {
		name: "Categoria de Produtos",
		description: "Categoria de produtos",
		searchTokens: "categoria, produto, produtos",
	},
};

const cadastroCidadeEn: NavModuleI18n = {
	cadastroCidade: {
		name: "Cidade",
		description: "Cadastro de cidades",
		searchTokens: "cidade, cadastro",
	},
};

const cadastroClassificacaoEn: NavModuleI18n = {
	cadastroClassificacao: {
		name: "Classificação",
		description: "Classificação",
		searchTokens: "classificação",
	},
};

const cadastroClienteEn: NavModuleI18n = {
	cadastroCliente: {
		name: "Cliente",
		description: "Cliente",
		searchTokens: "cliente",
	},
};

const cadastroColaboradorEn: NavModuleI18n = {
	cadastroColaborador: {
		name: "Colaborador",
		description: "Cadastro de colaboradores",
		searchTokens: "colaborador, colaborador, cadastro",
	},
};

const cadastroCompraEn: NavModuleI18n = {
	cadastroCompra: {
		name: "Compra",
		description: "Compra",
		searchTokens: "compra",
	},
};

const cadastroCondicaoPagamentoEn: NavModuleI18n = {
	cadastroCondicaoPagamento: {
		name: "Condição de Pagamento",
		description: "Condição de pagamento",
		searchTokens: "condição, pagamento, condição de pagamento",
	},
};

const cadastroConfigNotaFiscalEn: NavModuleI18n = {
	cadastroConfigNotaFiscal: {
		name: "Config. Nota Fiscal",
		description: "Configuração de nota fiscal",
		searchTokens: "configuração, nota, fiscal, nf, nota fiscal",
	},
};

const solicitacaoCompraMenuItemEn: NavModuleI18n = {
	solicitacaoCompraMenuItem: {
		name: "Solicitação de Compra",
		description: "Solicitação de compra",
		searchTokens: "solicitação, compra, solicitacao, soli",
	},
};

const cadastroConfigurarProdutoEstoqueEn: NavModuleI18n = {
	cadastroConfigurarProdutoEstoque: {
		name: "Configurar Produto Estoque",
		description: "Configurar produto estoque",
		searchTokens: "configurar, produto, estoque",
	},
};

const cadastroConfiguracaoClubeVantagensEn: NavModuleI18n = {
	cadastroConfiguracaoClubeVantagens: {
		name: "Configuração do Clube de Vantagens",
		description: "Config. do Clube de Vantagens",
		searchTokens: "configuração, clube, vantagens",
	},
};

const cadastroContaCorrenteEn: NavModuleI18n = {
	cadastroContaCorrente: {
		name: "Conta Corrente",
		description: "Conta corrente",
		searchTokens: "conta, corrente",
	},
};

const cadastroControleLogEn: NavModuleI18n = {
	cadastroControleLog: {
		name: "Controle de Log",
		description: "Controle de log",
		searchTokens: "controle, log, logs",
	},
};

const cadastroConvenioCobrancaEn: NavModuleI18n = {
	cadastroConvenioCobranca: {
		name: "Convênio de Cobrança",
		description: "Convênio de cobrança",
		searchTokens: "convênio, cobrança, convênio de cobrança",
	},
};

const cadastroConvenioDescontoEn: NavModuleI18n = {
	cadastroConvenioDesconto: {
		name: "Convênio de Desconto",
		description: "Convênio de desconto",
		searchTokens: "convênio, desconto, convênio de desconto",
	},
};

const cadastroDepartamentoEn: NavModuleI18n = {
	cadastroDepartamento: {
		name: "Departamento",
		description: "Departamento",
		searchTokens: "departamento",
	},
};

const cadastroDescontoEn: NavModuleI18n = {
	cadastroDesconto: {
		name: "Desconto",
		description: "Desconto",
		searchTokens: "desconto",
	},
};

const cadastroEmpresaEn: NavModuleI18n = {
	cadastroEmpresa: {
		name: "Empresa",
		description: "Empresa",
		searchTokens: "empresa",
	},
};

const cadastroFormaPagamentoEn: NavModuleI18n = {
	cadastroFormaPagamento: {
		name: "Forma de Pagamento",
		description: "Forma de Pagamento",
		searchTokens: "forma, pagamento, forma de pagamento",
	},
};

const cadastroGrauInstrucaoEn: NavModuleI18n = {
	cadastroGrauInstrucao: {
		name: "Grau de Instrução",
		description: "Grau de instrução",
		searchTokens: "grau, instrução",
	},
};

const cadastroGrupoDescontoEn: NavModuleI18n = {
	cadastroGrupoDesconto: {
		name: "Grupo com desconto",
		description: "Grupo de desconto",
		searchTokens: "grupo, desconto, grupo com, grupo com desconto",
	},
};

const cadastroHorarioEn: NavModuleI18n = {
	cadastroHorario: {
		name: "Horário",
		description: "Horário",
		searchTokens: "horário",
	},
};

const cadastroImportacaoEn: NavModuleI18n = {
	cadastroImportacao: {
		name: "Importação",
		description: "Importação",
		searchTokens: "importação, importacao",
	},
};

const cadastroImprimeReciboEmBrancoEn: NavModuleI18n = {
	cadastroImprimeReciboEmBranco: {
		name: "Imprime Recibo em Branco",
		description: "Imprime Recibo em Branco",
		searchTokens: "imprime, imprimir, recibo",
	},
};

const cadastroIntegracaoAcessoEn: NavModuleI18n = {
	cadastroIntegracaoAcesso: {
		name: "Integração de Acesso",
		description: "Integração de acesso",
		searchTokens: "integração, acesso, integração de acesso",
	},
};

const cadastroJustificativaOperacaoEn: NavModuleI18n = {
	cadastroJustificativaOperacao: {
		name: "Justificativa de Operação",
		description: "Justificativa de operação ",
		searchTokens: "justificativa, oeperacao",
	},
};

const cadastroLancamentoProdutoColetivoEn: NavModuleI18n = {
	cadastroLancamentoProdutoColetivo: {
		name: "Collective Product Launch",
		description: "Collective Product Launch",
		searchTokens: "launch, product, collective, collective operations",
	},
};

const cadastroLocalAcessoEn: NavModuleI18n = {
	cadastroLocalAcesso: {
		name: "Local de Acesso",
		description: "Local de acesso",
		searchTokens: "local, acesso",
	},
};

const cadastroLocalImpressaoEn: NavModuleI18n = {
	cadastroLocalImpressao: {
		name: "Local de Impressão",
		description: "Local de impressão",
		searchTokens: "local, impressão",
	},
};

const cadastroMetasFinanceiroEn: NavModuleI18n = {
	cadastroMetasFinanceiro: {
		name: "Metas Financeiras de Vendas",
		description: "Metas do financeiro",
		searchTokens: "metas, financeiro",
	},
};

const cadastroModalidadeEn: NavModuleI18n = {
	cadastroModalidade: {
		name: "Modalidade",
		description: "Modalidade",
		searchTokens: "modalidade",
	},
};

const cadastroModeloContratoEn: NavModuleI18n = {
	cadastroModeloContrato: {
		name: "Modelo de Contrato",
		description: "Modelo de contrato",
		searchTokens: "modelo, contrato",
	},
};

const cadastroModeloOrcamentoEn: NavModuleI18n = {
	cadastroModeloOrcamento: {
		name: "Modelo de Orçamento",
		description: "Modelo de Orçamento",
		searchTokens: "modelo de orçamento, modelo, orçamento",
	},
};

const cadastroMovContaCorrenteClienteEn: NavModuleI18n = {
	cadastroMovContaCorrenteCliente: {
		name: "Mov. CC do Cliente",
		description: "Mov. CC do cliente",
		searchTokens: "movimentação, conta, corrente, conta corrente, cliente",
	},
};

const cadastroMovimentoProdutoEn: NavModuleI18n = {
	cadastroMovimentoProduto: {
		name: "Movimento do Produto",
		description: "Movimento do Produto",
		searchTokens: "movimento, produto",
	},
};

const cadastroNivelTurmaEn: NavModuleI18n = {
	cadastroNivelTurma: {
		name: "Nível de Turma",
		description: "Nível de Turma",
		searchTokens: "nível, turma",
	},
};

const cadastroOperadoraCartaoEn: NavModuleI18n = {
	cadastroOperadoraCartao: {
		name: "Operadora de Cartão",
		description: "Operadora de cartão",
		searchTokens: "operadora, cartão",
	},
};

const cadastroPacoteEn: NavModuleI18n = {
	cadastroPacote: {
		name: "Pacote",
		description: "Pacote",
		searchTokens: "pacote",
	},
};

const cadastroParentescoEn: NavModuleI18n = {
	cadastroParentesco: {
		name: "Parentesco",
		description: "Parentesco",
		searchTokens: "parentesco",
	},
};

const cadastroPaisEn: NavModuleI18n = {
	cadastroPais: {
		name: "País",
		description: "País",
		searchTokens: "país",
	},
};

const cadastroPerfilAcessoEn: NavModuleI18n = {
	cadastroPerfilAcesso: {
		name: "Perfil de Acesso (ADM)",
		description: "Perfil de acesso (ADM)",
		searchTokens: "perfil, acesso",
	},
};

const cadastroNpsEn: NavModuleI18n = {
	cadastroNps: {
		name: "Pesquisa de Satisfação / NPS",
		description: "Pesquisa de satisfação / NPS",
		searchTokens: "pesquisa, satisfação, nps",
	},
};

const cadastroPlanoEn: NavModuleI18n = {
	cadastroPlano: {
		name: "Plano",
		description: "Plano",
		searchTokens: "plano",
	},
};

const cadastroPosicaoEstoqueEn: NavModuleI18n = {
	cadastroPosicaoEstoque: {
		name: "Posição do Estoque",
		description: "Posição do estoque",
		searchTokens: "posição, estoque",
	},
};

const cadastroProdutoEn: NavModuleI18n = {
	cadastroProduto: {
		name: "Produto",
		description: "Produto",
		searchTokens: "produto",
	},
};

const cadastroProfissaoEn: NavModuleI18n = {
	cadastroProfissao: {
		name: "Profissão",
		description: "Profissão",
		searchTokens: "profissão",
	},
};

const cadastroServidorFacialEn: NavModuleI18n = {
	cadastroServidorFacial: {
		name: "Servidor Facial",
		description: "Servidor facial",
		searchTokens: "servidor, facial",
	},
};

const cadastroTaxaComissaoEn: NavModuleI18n = {
	cadastroTaxaComissao: {
		name: "Taxas de Comissão",
		description: "Taxas de comissão",
		searchTokens: "taxas, comissão",
	},
};

const cadastroTipoModalidadeEn: NavModuleI18n = {
	cadastroTipoModalidade: {
		name: "Tipo de Modalidade",
		description: "Tipo de modalidade",
		searchTokens: "tipo, modalidade",
	},
};

const cadastroTipoPlanoEn: NavModuleI18n = {
	cadastroTipoPlano: {
		name: "Tipo de Plano",
		description: "Tipo de Plano",
		searchTokens: "tipo, plano",
	},
};

const cadastroTipoRemessaEn: NavModuleI18n = {
	cadastroTipoRemessa: {
		name: "Tipo de Remessa",
		description: "Tipo de Remessa",
		searchTokens: "tipo, remessa",
	},
};

const cadastroTipoRetornoEn: NavModuleI18n = {
	cadastroTipoRetorno: {
		name: "Tipo de Retorno",
		description: "Tipo de retorno",
		searchTokens: "tipo, retorno",
	},
};

const cadastroUsuarioEn: NavModuleI18n = {
	cadastroUsuario: {
		name: "Usuário",
		description: "Usuário",
		searchTokens: "usuario",
	},
};

const cadastroVendaConsumidorEn: NavModuleI18n = {
	cadastroVendaConsumidor: {
		name: "Venda de Consumidor",
		description: "Venda de consumidor",
		searchTokens: "venda, consumidor",
	},
};

const cadastroIndiceFinanceiroEn: NavModuleI18n = {
	cadastroIndiceFinanceiro: {
		name: "Índice Finaceiro - Reajuste de preço",
		description: "Índice Finaceiro - Reajuste de preço",
		searchTokens: "índice, financeiro, preço",
	},
};

const cadastroCampanhaCupomDescontoEn: NavModuleI18n = {
	cadastroCampanhaCupomDesconto: {
		name: "Campanha Cupom de Desconto",
		description: "Campanha Cupom de Desconto",
		searchTokens: "campanha, cupom, desconto",
	},
};

const cadastroPerguntaEn: NavModuleI18n = {
	cadastroPergunta: {
		name: "Pergunta",
		description: "Lista de perguntas",
		searchTokens: "pergunta",
	},
};

const cadastroQuestionarioEn: NavModuleI18n = {
	cadastroQuestionario: {
		name: "Questionário",
		description: `
        No campo de cadastro de QUESTIONÁRIOS poderão ser criados vários Boletins de Visitas diferentes.
        O colaborador poderá criar o questionário de acordo com as necessidades da academia.
        De forma simplificada o colaborador conseguirá definir um nome e
        escolher as perguntas que farão parte do seu boletim e pronto`,
		searchTokens: "questionario",
	},
};

const cadastroAcessoMenuEn: NavModuleI18n = {
	"cadastro-acesso": {
		name: "Cadastro de Acessos",
	},
};

const cadastroClubeVantagemMenuEn: NavModuleI18n = {
	"cadastro-clube-vantagens": {
		name: "Cadastros Clube de Vantagens",
	},
};

const cadastroContratoMenuEn: NavModuleI18n = {
	"cadastro-contrato": {
		name: "Cadastros de Contrato",
	},
};

const cadastroFinanceiroMenuEn: NavModuleI18n = {
	"cadastro-financeiro": {
		name: "Cadastros Financeiros",
	},
};

const cadastroPlanoMenuEn: NavModuleI18n = {
	"cadastro-plano": {
		name: "Cadastros de Planos",
	},
};

const cadastroProdutoParentMenuEn: NavModuleI18n = {
	"cadastro-produto": {
		name: "Cadastros de Produtos",
	},
};

const cadastroOutrosMenuEn: NavModuleI18n = {
	"cadastro-outros": {
		name: "Outros Cadastros",
	},
};

const cadastroPerfilAcessoUnificadoEn: NavModuleI18n = {
	cadastroPerfilAcessoUnificado: {
		name: "Perfil de Acesso (Unificado)",
		description: "Perfil de acesso Unificado",
		searchTokens: "perfil, acesso, perfil de acesso, perfil acesso",
	},
};

export const cadastroMenuEn: NavModuleI18n = {
	cadastros: {
		name: "Cadastros",
		description: `Cadastros`,
		searchTokens: "cadastro, cadastros",
	},
	...cadastroPerfilAcessoUnificadoEn,
	...cadastroAcessoMenuEn,
	...cadastroClubeVantagemMenuEn,
	...cadastroContratoMenuEn,
	...cadastroFinanceiroMenuEn,
	...cadastroPlanoMenuEn,
	...cadastroProdutoParentMenuEn,
	...cadastroOutrosMenuEn,
	...cadastroAdquirenteEn,
	...cadastroAmbienteEn,
	...cadastroAutorizacaoAcessoEn,
	...cadastroBalancoEn,
	...cadastroBancoEn,
	...cadastroBrindesEn,
	...cadastroCampanhasEn,
	...cadastroCardexEn,
	...cadastroCategoriaClienteEn,
	...grupoComDescontoEn,
	...cadastroCategoriaProdutoEn,
	...cadastroCidadeEn,
	...cadastroClassificacaoEn,
	...cadastroClienteEn,
	...cadastroColaboradorEn,
	...cadastroCompraEn,
	...cadastroCondicaoPagamentoEn,
	...cadastroConfigNotaFiscalEn,
	...cadastroConfigurarProdutoEstoqueEn,
	...solicitacaoCompraMenuItemEn,
	...cadastroConfiguracaoClubeVantagensEn,
	...cadastroContaCorrenteEn,
	...cadastroControleLogEn,
	...cadastroConvenioCobrancaEn,
	...cadastroConvenioDescontoEn,
	...cadastroDepartamentoEn,
	...cadastroDescontoEn,
	...cadastroEmpresaEn,
	...cadastroFormaPagamentoEn,
	...cadastroGrauInstrucaoEn,
	...cadastroGrupoDescontoEn,
	...cadastroHorarioEn,
	...cadastroImportacaoEn,
	...cadastroImpostoProdutoEn,
	...cadastroImprimeReciboEmBrancoEn,
	...cadastroIntegracaoAcessoEn,
	...cadastroJustificativaOperacaoEn,
	...cadastroLancamentoProdutoColetivoEn,
	...cadastroLocalAcessoEn,
	...cadastroLocalImpressaoEn,
	...cadastroMetasFinanceiroEn,
	...cadastroModalidadeEn,
	...cadastroModeloContratoEn,
	...cadastroModeloOrcamentoEn,
	...cadastroMovContaCorrenteClienteEn,
	...cadastroMovimentoProdutoEn,
	...cadastroNivelTurmaEn,
	...cadastroOperadoraCartaoEn,
	...cadastroPacoteEn,
	...cadastroPaisEn,
	...cadastroParentescoEn,
	...cadastroPerfilAcessoEn,
	...cadastroPerguntaEn,
	...cadastroNpsEn,
	...cadastroPlanoEn,
	...cadastroPosicaoEstoqueEn,
	...cadastroProdutoEn,
	...cadastroProfissaoEn,
	...cadastroQuestionarioEn,
	...cadastroServidorFacialEn,
	...cadastroTaxaComissaoEn,
	...cadastroTipoModalidadeEn,
	...cadastroTipoPlanoEn,
	...cadastroTipoRemessaEn,
	...cadastroTipoRetornoEn,
	...cadastroUsuarioEn,
	...cadastroVendaConsumidorEn,
	...cadastroIndiceFinanceiroEn,
	...cadastroPinPadEn,
	...cadastroCampanhaCupomDescontoEn,
	...cadastroTamanhoArmarioEn,
};
