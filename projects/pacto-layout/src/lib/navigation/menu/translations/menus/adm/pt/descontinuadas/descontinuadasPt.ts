import { NavModuleI18n } from "ui-kit";

export const funcionalidadesDescontinuadasPt: NavModuleI18n = {
	funcionalidades_descontinuadas: {
		name: "Funcionalidades descontinuadas",
	},
	cadastroPosicaoEstoque_descontinuado: {
		name: "Posição de estoque",
	},
	diaria_descontinuadas: {
		name: "Venda de diária",
	},
	plano_descontinuado: {
		name: "Plano",
	},
	turma_descontinuado: {
		name: "<PERSON>rma",
	},
	fechamento_caixa_descontinuado: {
		name: "Fechamento de caixa",
	},
	pinpad_descontinuado: {
		name: "Pinpad",
	},
	ambiente_descontinuado: {
		name: "Ambiente",
	},
	gestaoTurma_descontinuado: {
		name: "Gestão de Turma",
	},
	cadastroTipoModalidade_descontinuado: {
		name: "Tipo de Modalidade",
	},
	cadastroTipoRetorno_descontinuado: {
		name: "Tipo de Retorno",
	},
	cadastroTipoRemessa_descontinuado: {
		name: "Tipo de Remessa",
	},
	cadastroMovimentoProduto_descontinuado: {
		name: "Movimento de produto",
	},
	cadastroModeloOrcamento_descontinuado: {
		name: "Modelo de Orçamento",
	},
	formaPagamento_descontinuado: {
		name: "Forma de Pagamento",
	},
	nivelTurma_descontinuado: {
		name: "Nível de Turma",
	},
	campanhaCupomDesconto_descontinuado: {
		name: "Campanha Cupom de Desconto",
	},
	campanhaClubeVantagens_descontinuado: {
		name: "Campanha do Clube de Vantagens",
	},
	descontoOcupacao_descontinuado: {
		name: "Desconto por Ocupação na Turma",
	},
	pedidosPinpad_descontinuado: {
		name: "Pedidos Pinpad",
	},
	cadastroCompra_descontinuado: {
		name: "Compra",
	},
	comissaoConsultor_descontinuado: {
		name: "Comissão para Consultor",
	},
	relatorioClienteComCobrancaAutomaticaBloqueada_descontinuado: {
		name: "Cobranças Automáticas Bloqueadas",
	},
	cadastroProduto_descontinuado: {
		name: "Produto",
	},
	lancamentoProdutoColetivo_descontinuado: {
		name: "Lançamento de Produto Coletivo",
	},
	fechamentoAcessos_descontinuado: {
		name: "Fechamento de Acessos",
	},
	taxaComissao_descontinuado: {
		name: "Taxas de Comissão",
	},
};
