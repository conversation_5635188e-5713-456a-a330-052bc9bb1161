import { NavModuleI18n } from "ui-kit";

const cadastroAdquirenteEs: NavModuleI18n = {
	cadastroAdquirente: {
		name: "Adquirente",
		description: "Cadastrar adquirente",
		searchTokens: "cadastro, adquirente",
	},
};

const cadastroImpostoProdutoEs: NavModuleI18n = {
	cadastroAdquirente: {
		name: "Imposto Produto",
		description: "Cadastrar imposto produto",
		searchTokens: "cadastro, imposto produto",
	},
};
const cadastroTamanhoArmarioEs: NavModuleI18n = {
	cadastroTamanhoArmario: {
		name: "<PERSON><PERSON><PERSON> Armário",
		description: "Taman<PERSON> de Armário",
		searchTokens: "cadastro, tamanho, armário",
	},
};

const cadastroAmbienteEs: NavModuleI18n = {
	cadastroAmbiente: {
		name: "Ambiente",
		description: "Ambiente",
		searchTokens: "ambiente",
	},
};

const cadastroAutorizacaoAcessoEs: NavModuleI18n = {
	cadastroAutorizacaoAcesso: {
		name: "Autorização de Acesso",
		description: "Autorização de acesso",
		searchTokens: "autorização, acesso",
	},
};

const cadastroBalancoEs: NavModuleI18n = {
	cadastroBalanco: {
		name: "Balanço",
		description: "Balanço",
		searchTokens: "balanço, balanco",
	},
};

const cadastroBancoEs: NavModuleI18n = {
	cadastroBanco: {
		name: "Banco",
		description: "Banco",
		searchTokens: "banco",
	},
};

const cadastroBrindesEs: NavModuleI18n = {
	cadastroBrindes: {
		name: "Brindes",
		description: "Cadastro de brindes",
		searchTokens: "brindes, cadastro",
	},
};

const cadastroCampanhasEs: NavModuleI18n = {
	cadastroCampanhas: {
		name: "Campanha do Clube de Vantagens",
		description: "Campanha do Clube de Vantagens",
		searchTokens: "campanhas, cadastro, clube de vantagens",
	},
};

const cadastroPinPadEs: NavModuleI18n = {
	cadastroPinPad: {
		name: "Pinpad",
		description: "Pinpad",
		searchTokens: "pinpad, cadastro",
	},
};

const cadastroCardexEs: NavModuleI18n = {
	cadastroCardex: {
		name: "Cardex",
		description: "Cardex",
		searchTokens: "cardex",
	},
};

const cadastroCategoriaClienteEs: NavModuleI18n = {
	cadastroCategoriaCliente: {
		name: "Categoria de Clientes",
		description: "Categoria de clientes",
		searchTokens: "categoria, cliente, clientes",
	},
};

const grupoComDescontoEs: NavModuleI18n = {
	grupoComDesconto: {
		name: "Grupo Com Desconto",
		description: "Grupo Com Desconto",
		searchTokens: "grupo, desconto",
	},
};

const cadastroCategoriaProdutoEs: NavModuleI18n = {
	cadastroCategoriaProduto: {
		name: "Categoria de Produtos",
		description: "Categoria de produtos",
		searchTokens: "categoria, produto, produtos",
	},
};

const cadastroCidadeEs: NavModuleI18n = {
	cadastroCidade: {
		name: "Cidade",
		description: "Cadastro de cidades",
		searchTokens: "cidade, cadastro",
	},
};

const cadastroClassificacaoEs: NavModuleI18n = {
	cadastroClassificacao: {
		name: "Classificação",
		description: "Classificação",
		searchTokens: "classificação",
	},
};

const cadastroClienteEs: NavModuleI18n = {
	cadastroCliente: {
		name: "Cliente",
		description: "Cliente",
		searchTokens: "cliente",
	},
};

const cadastroColaboradorEs: NavModuleI18n = {
	cadastroColaborador: {
		name: "Colaborador",
		description: "Cadastro de colaboradores",
		searchTokens: "colaborador, colaborador, cadastro",
	},
};

const cadastroCompraEs: NavModuleI18n = {
	cadastroCompra: {
		name: "Compra",
		description: "Compra",
		searchTokens: "compra",
	},
};

const cadastroCondicaoPagamentoEs: NavModuleI18n = {
	cadastroCondicaoPagamento: {
		name: "Condição de Pagamento",
		description: "Condição de pagamento",
		searchTokens: "condição, pagamento, condição de pagamento",
	},
};

const cadastroConfigNotaFiscalEs: NavModuleI18n = {
	cadastroConfigNotaFiscal: {
		name: "Config. Nota Fiscal",
		description: "Configuração de nota fiscal",
		searchTokens: "configuração, nota, fiscal, nf, nota fiscal",
	},
};

const solicitacaoCompraMenuItemEs: NavModuleI18n = {
	solicitacaoCompraMenuItem: {
		name: "Solicitação de Compra",
		description: "Solicitação de compra",
		searchTokens: "solicitação, compra, solicitacao, soli",
	},
};

const cadastroConfigurarProdutoEstoqueEs: NavModuleI18n = {
	cadastroConfigurarProdutoEstoque: {
		name: "Configurar Produto Estoque",
		description: "Configurar produto estoque",
		searchTokens: "configurar, produto, estoque",
	},
};

const cadastroConfiguracaoClubeVantagensEs: NavModuleI18n = {
	cadastroConfiguracaoClubeVantagens: {
		name: "Configuração do Clube de Vantagens",
		description: "Config. do Clube de Vantagens",
		searchTokens: "configuração, clube, vantagens",
	},
};

const cadastroContaCorrenteEs: NavModuleI18n = {
	cadastroContaCorrente: {
		name: "Conta Corrente",
		description: "Conta corrente",
		searchTokens: "conta, corrente",
	},
};

const cadastroControleLogEs: NavModuleI18n = {
	cadastroControleLog: {
		name: "Controle de Log",
		description: "Controle de log",
		searchTokens: "controle, log, logs",
	},
};

const cadastroConvenioCobrancaEs: NavModuleI18n = {
	cadastroConvenioCobranca: {
		name: "Convênio de Cobrança",
		description: "Convênio de cobrança",
		searchTokens: "convênio, cobrança, convênio de cobrança",
	},
};

const cadastroConvenioDescontoEs: NavModuleI18n = {
	cadastroConvenioDesconto: {
		name: "Convênio de Desconto",
		description: "Convênio de desconto",
		searchTokens: "convênio, desconto, convênio de desconto",
	},
};

const cadastroDepartamentoEs: NavModuleI18n = {
	cadastroDepartamento: {
		name: "Departamento",
		description: "Departamento",
		searchTokens: "departamento",
	},
};

const cadastroDescontoEs: NavModuleI18n = {
	cadastroDesconto: {
		name: "Desconto",
		description: "Desconto",
		searchTokens: "desconto",
	},
};

const cadastroEmpresaEs: NavModuleI18n = {
	cadastroEmpresa: {
		name: "Empresa",
		description: "Empresa",
		searchTokens: "empresa",
	},
};

const cadastroFormaPagamentoEs: NavModuleI18n = {
	cadastroFormaPagamento: {
		name: "Forma de Pagamento",
		description: "Forma de Pagamento",
		searchTokens: "forma, pagamento, forma de pagamento",
	},
};

const cadastroGrauInstrucaoEs: NavModuleI18n = {
	cadastroGrauInstrucao: {
		name: "Grau de Instrução",
		description: "Grau de instrução",
		searchTokens: "grau, instrução",
	},
};

const cadastroGrupoDescontoEs: NavModuleI18n = {
	cadastroGrupoDesconto: {
		name: "Grupo com desconto",
		description: "Grupo de desconto",
		searchTokens: "grupo, desconto, grupo com, grupo com desconto",
	},
};

const cadastroHorarioEs: NavModuleI18n = {
	cadastroHorario: {
		name: "Horário",
		description: "Horário",
		searchTokens: "horário",
	},
};

const cadastroImportacaoEs: NavModuleI18n = {
	cadastroImportacao: {
		name: "Importação",
		description: "Importação",
		searchTokens: "importação, importacao",
	},
};

const cadastroImprimeReciboEmBrancoEs: NavModuleI18n = {
	cadastroImprimeReciboEmBranco: {
		name: "Imprime Recibo em Branco",
		description: "Imprime Recibo em Branco",
		searchTokens: "imprime, imprimir, recibo",
	},
};

const cadastroIntegracaoAcessoEs: NavModuleI18n = {
	cadastroIntegracaoAcesso: {
		name: "Integração de Acesso",
		description: "Integração de acesso",
		searchTokens: "integração, acesso, integração de acesso",
	},
};

const cadastroJustificativaOperacaoEs: NavModuleI18n = {
	cadastroJustificativaOperacao: {
		name: "Justificativa de Operação",
		description: "Justificativa de operação ",
		searchTokens: "justificativa, oeperacao",
	},
};

const cadastroLancamentoProdutoColetivoEs: NavModuleI18n = {
	cadastroLancamentoProdutoColetivo: {
		name: "Lanzamiento Producto Colectivo",
		description: "Lanzamiento Producto Colectivo",
		searchTokens: "lanzamiento, producto, colectivo, operaciones colectivas",
	},
};

const cadastroLocalAcessoEs: NavModuleI18n = {
	cadastroLocalAcesso: {
		name: "Local de Acesso",
		description: "Local de acesso",
		searchTokens: "local, acesso",
	},
};

const cadastroLocalImpressaoEs: NavModuleI18n = {
	cadastroLocalImpressao: {
		name: "Local de Impressão",
		description: "Local de impressão",
		searchTokens: "local, impressão",
	},
};

const cadastroMetasFinanceiroEs: NavModuleI18n = {
	cadastroMetasFinanceiro: {
		name: "Metas Financeiras de Vendas",
		description: "Metas do financeiro",
		searchTokens: "metas, financeiro",
	},
};

const cadastroModalidadeEs: NavModuleI18n = {
	cadastroModalidade: {
		name: "Modalidade",
		description: "Modalidade",
		searchTokens: "modalidade",
	},
};

const cadastroModeloContratoEs: NavModuleI18n = {
	cadastroModeloContrato: {
		name: "Modelo de Contrato",
		description: "Modelo de contrato",
		searchTokens: "modelo, contrato",
	},
};

const cadastroModeloOrcamentoEs: NavModuleI18n = {
	cadastroModeloOrcamento: {
		name: "Modelo de Orçamento",
		description: "Modelo de Orçamento",
		searchTokens: "modelo de orçamento, modelo, orçamento",
	},
};

const cadastroMovContaCorrenteClienteEs: NavModuleI18n = {
	cadastroMovContaCorrenteCliente: {
		name: "Mov. CC do Cliente",
		description: "Mov. CC do cliente",
		searchTokens: "movimentação, conta, corrente, conta corrente, cliente",
	},
};

const cadastroMovimentoProdutoEs: NavModuleI18n = {
	cadastroMovimentoProduto: {
		name: "Movimento do Produto",
		description: "Movimento do Produto",
		searchTokens: "movimento, produto",
	},
};

const cadastroNivelTurmaEs: NavModuleI18n = {
	cadastroNivelTurma: {
		name: "Nível de Turma",
		description: "Nível de Turma",
		searchTokens: "nível, turma",
	},
};

const cadastroOperadoraCartaoEs: NavModuleI18n = {
	cadastroOperadoraCartao: {
		name: "Operadora de Cartão",
		description: "Operadora de cartão",
		searchTokens: "operadora, cartão",
	},
};

const cadastroPacoteEs: NavModuleI18n = {
	cadastroPacote: {
		name: "Pacote",
		description: "Pacote",
		searchTokens: "pacote",
	},
};

const cadastroParentescoEs: NavModuleI18n = {
	cadastroParentesco: {
		name: "Parentesco",
		description: "Parentesco",
		searchTokens: "parentesco",
	},
};

const cadastroPaisEs: NavModuleI18n = {
	cadastroPais: {
		name: "País",
		description: "País",
		searchTokens: "país",
	},
};

const cadastroPerfilAcessoEs: NavModuleI18n = {
	cadastroPerfilAcesso: {
		name: "Perfil de Acesso (ADM)",
		description: "Perfil de acesso (ADM)",
		searchTokens: "perfil, acesso",
	},
};

const cadastroNpsEs: NavModuleI18n = {
	cadastroNps: {
		name: "Pesquisa de Satisfação / NPS",
		description: "Pesquisa de satisfação / NPS",
		searchTokens: "pesquisa, satisfação, nps",
	},
};

const cadastroPlanoEs: NavModuleI18n = {
	cadastroPlano: {
		name: "Plano",
		description: "Plano",
		searchTokens: "plano",
	},
};

const cadastroPosicaoEstoqueEs: NavModuleI18n = {
	cadastroPosicaoEstoque: {
		name: "Posição do Estoque",
		description: "Posição do estoque",
		searchTokens: "posição, estoque",
	},
};

const cadastroProdutoEs: NavModuleI18n = {
	cadastroProduto: {
		name: "Produto",
		description: "Produto",
		searchTokens: "produto",
	},
};

const cadastroProfissaoEs: NavModuleI18n = {
	cadastroProfissao: {
		name: "Profissão",
		description: "Profissão",
		searchTokens: "profissão",
	},
};

const cadastroServidorFacialEs: NavModuleI18n = {
	cadastroServidorFacial: {
		name: "Servidor Facial",
		description: "Servidor facial",
		searchTokens: "servidor, facial",
	},
};

const cadastroTaxaComissaoEs: NavModuleI18n = {
	cadastroTaxaComissao: {
		name: "Taxas de Comissão",
		description: "Taxas de comissão",
		searchTokens: "taxas, comissão",
	},
};

const cadastroTipoModalidadeEs: NavModuleI18n = {
	cadastroTipoModalidade: {
		name: "Tipo de Modalidade",
		description: "Tipo de modalidade",
		searchTokens: "tipo, modalidade",
	},
};

const cadastroTipoPlanoEs: NavModuleI18n = {
	cadastroTipoPlano: {
		name: "Tipo de Plano",
		description: "Tipo de Plano",
		searchTokens: "tipo, plano",
	},
};

const cadastroTipoRemessaEs: NavModuleI18n = {
	cadastroTipoRemessa: {
		name: "Tipo de Remessa",
		description: "Tipo de Remessa",
		searchTokens: "tipo, remessa",
	},
};

const cadastroTipoRetornoEs: NavModuleI18n = {
	cadastroTipoRetorno: {
		name: "Tipo de Retorno",
		description: "Tipo de retorno",
		searchTokens: "tipo, retorno",
	},
};

const cadastroUsuarioEs: NavModuleI18n = {
	cadastroUsuario: {
		name: "Usuário",
		description: "Usuário",
		searchTokens: "usuario",
	},
};

const cadastroVendaConsumidorEs: NavModuleI18n = {
	cadastroVendaConsumidor: {
		name: "Venda de Consumidor",
		description: "Venda de consumidor",
		searchTokens: "venda, consumidor",
	},
};

const cadastroIndiceFinanceiroEs: NavModuleI18n = {
	cadastroIndiceFinanceiro: {
		name: "Índice Finaceiro - Reajuste de preço",
		description: "Índice Finaceiro - Reajuste de preço",
		searchTokens: "índice, financeiro, preço",
	},
};

const cadastroCampanhaCupomDescontoEs: NavModuleI18n = {
	cadastroCampanhaCupomDesconto: {
		name: "Campanha Cupom de Desconto",
		description: "Campanha Cupom de Desconto",
		searchTokens: "campanha, cupom, desconto",
	},
};

const cadastroPerguntaEs: NavModuleI18n = {
	cadastroPergunta: {
		name: "Pergunta",
		description: "Lista de perguntas",
		searchTokens: "pergunta",
	},
};

const cadastroQuestionarioEs: NavModuleI18n = {
	cadastroQuestionario: {
		name: "Questionário",
		description: `
        No campo de cadastro de QUESTIONÁRIOS poderão ser criados vários Boletins de Visitas diferentes.
        O colaborador poderá criar o questionário de acordo com as necessidades da academia.
        De forma simplificada o colaborador conseguirá definir um nome e
        escolher as perguntas que farão parte do seu boletim e pronto`,
		searchTokens: "questionario",
	},
};

const cadastroAcessoMenuEs: NavModuleI18n = {
	"cadastro-acesso": {
		name: "Cadastro de Acessos",
	},
};

const cadastroClubeVantagemMenuEs: NavModuleI18n = {
	"cadastro-clube-vantagens": {
		name: "Cadastros Clube de Vantagens",
	},
};

const cadastroContratoMenuEs: NavModuleI18n = {
	"cadastro-contrato": {
		name: "Cadastros de Contrato",
	},
};

const cadastroFinanceiroMenuEs: NavModuleI18n = {
	"cadastro-financeiro": {
		name: "Cadastros Financeiros",
	},
};

const cadastroPlanoMenuEs: NavModuleI18n = {
	"cadastro-plano": {
		name: "Cadastros de Planos",
	},
};

const cadastroProdutoParentMenuEs: NavModuleI18n = {
	"cadastro-produto": {
		name: "Cadastros de Produtos",
	},
};

const cadastroOutrosMenuEs: NavModuleI18n = {
	"cadastro-outros": {
		name: "Outros Cadastros",
	},
};

const cadastroPerfilAcessoUnificadoEs: NavModuleI18n = {
	cadastroPerfilAcessoUnificado: {
		name: "Perfil de Acesso (Unificado)",
		description: "Perfil de acesso Unificado",
		searchTokens: "perfil, acesso, perfil de acesso, perfil acesso",
	},
};

export const cadastroMenuEs: NavModuleI18n = {
	cadastros: {
		name: "Cadastros",
		description: `Cadastros`,
		searchTokens: "cadastro, cadastros",
	},
	...cadastroPerfilAcessoUnificadoEs,
	...cadastroAcessoMenuEs,
	...cadastroClubeVantagemMenuEs,
	...cadastroContratoMenuEs,
	...cadastroFinanceiroMenuEs,
	...cadastroPlanoMenuEs,
	...cadastroProdutoParentMenuEs,
	...cadastroOutrosMenuEs,
	...cadastroAdquirenteEs,
	...cadastroAmbienteEs,
	...cadastroAutorizacaoAcessoEs,
	...cadastroBalancoEs,
	...cadastroBancoEs,
	...cadastroBrindesEs,
	...cadastroCampanhasEs,
	...cadastroCardexEs,
	...cadastroCategoriaClienteEs,
	...grupoComDescontoEs,
	...cadastroCategoriaProdutoEs,
	...cadastroCidadeEs,
	...cadastroClassificacaoEs,
	...cadastroClienteEs,
	...cadastroColaboradorEs,
	...cadastroCompraEs,
	...cadastroCondicaoPagamentoEs,
	...cadastroConfigNotaFiscalEs,
	...cadastroConfigurarProdutoEstoqueEs,
	...solicitacaoCompraMenuItemEs,
	...cadastroConfiguracaoClubeVantagensEs,
	...cadastroContaCorrenteEs,
	...cadastroControleLogEs,
	...cadastroConvenioCobrancaEs,
	...cadastroConvenioDescontoEs,
	...cadastroDepartamentoEs,
	...cadastroDescontoEs,
	...cadastroEmpresaEs,
	...cadastroFormaPagamentoEs,
	...cadastroGrauInstrucaoEs,
	...cadastroGrupoDescontoEs,
	...cadastroHorarioEs,
	...cadastroImportacaoEs,
	...cadastroImpostoProdutoEs,
	...cadastroImprimeReciboEmBrancoEs,
	...cadastroIntegracaoAcessoEs,
	...cadastroJustificativaOperacaoEs,
	...cadastroLancamentoProdutoColetivoEs,
	...cadastroLocalAcessoEs,
	...cadastroLocalImpressaoEs,
	...cadastroMetasFinanceiroEs,
	...cadastroModalidadeEs,
	...cadastroModeloContratoEs,
	...cadastroModeloOrcamentoEs,
	...cadastroMovContaCorrenteClienteEs,
	...cadastroMovimentoProdutoEs,
	...cadastroNivelTurmaEs,
	...cadastroOperadoraCartaoEs,
	...cadastroPacoteEs,
	...cadastroPaisEs,
	...cadastroParentescoEs,
	...cadastroPerfilAcessoEs,
	...cadastroPerguntaEs,
	...cadastroNpsEs,
	...cadastroPlanoEs,
	...cadastroPosicaoEstoqueEs,
	...cadastroProdutoEs,
	...cadastroProfissaoEs,
	...cadastroQuestionarioEs,
	...cadastroServidorFacialEs,
	...cadastroTaxaComissaoEs,
	...cadastroTipoModalidadeEs,
	...cadastroTipoPlanoEs,
	...cadastroTipoRemessaEs,
	...cadastroTipoRetornoEs,
	...cadastroUsuarioEs,
	...cadastroVendaConsumidorEs,
	...cadastroIndiceFinanceiroEs,
	...cadastroPinPadEs,
	...cadastroCampanhaCupomDescontoEs,
	...cadastroTamanhoArmarioEs,
};
