import { NavModuleI18n } from "ui-kit";

const relatorioGeralClientePt: NavModuleI18n = {
	relatorioGeralCliente: {
		name: "G<PERSON> de Clientes",
		description: "Relatório geral de clientes",
		searchTokens:
			"relatorio, relatório, clientes, geral, relatorio geral de clientes",
	},
};

const relatorioClientePt: NavModuleI18n = {
	relatorioCliente: {
		name: "Relatório de Clientes",
		description: "Relatório de Clientes",
		searchTokens: "relatorio, clientes",
	},
};

const clientePt: NavModuleI18n = {
	cliente: {
		name: "Cliente",
		description: "Cliente",
		searchTokens: "cliente, clientes",
	},
};

const relatorioClienteSimplificadoPt: NavModuleI18n = {
	relatorioClienteSimplificado: {
		name: "Lista de Clientes Simplificada",
		description: "Cliente Simplificado",
		searchTokens: "cliente, simplificado",
	},
};

const relatorioClientesVisitantesPt: NavModuleI18n = {
	relatorioClientesVisitantes: {
		name: "Visitant<PERSON>",
		description: "Relatório de Visitantes",
		searchTokens: "relatorio, visitantes",
	},
};

const relatorioClientesCanceladosPt: NavModuleI18n = {
	relatorioClientesCancelados: {
		name: "Clientes Cancelados",
		description: "Relatório de clientes cancelados",
		searchTokens: "relatorio, clientes, cancelados",
	},
};

const relatorioClientesTrancadosPt: NavModuleI18n = {
	relatorioClientesTrancados: {
		name: "Clientes Trancados",
		description: "relatorio, clientes, trancados",
		searchTokens: "",
	},
};

const relatorioClientesComBonusPt: NavModuleI18n = {
	relatorioClientesComBonus: {
		name: "Clientes com Bônus",
		description: "Relatório de clientes com bônus",
		searchTokens: "relatorio, clientes, bonus",
	},
};

const relatorioClientesComAtestadoPt: NavModuleI18n = {
	relatorioClientesComAtestado: {
		name: "Clientes com Atestado",
		description: "Relatório de clientes com atestado",
		searchTokens: "relatorio, clientes, atestado",
	},
};

const relarioClientesAniversariantesPt: NavModuleI18n = {
	relatorioClientesAniversariantes: {
		name: "Aniversariantes",
		description: "Relatório de aniversariantes",
		searchTokens:
			"relatorio, aviversariante, aniversariantes, aniversário, aniversário",
	},
};

const relatorioContratosPorDuracaoPt: NavModuleI18n = {
	relatorioContratosPorDuracao: {
		name: "Contratos por Duração",
		description: "Relatório de contratos por duração",
		searchTokens: "contrato, relatorio, duração",
	},
};

const relatorioGympassPt: NavModuleI18n = {
	relatorioGympass: {
		name: "Gympass",
		description: "Gympass",
		searchTokens: "relatorio, gympass",
	},
};

const relatorioSaldoCreditoPt: NavModuleI18n = {
	relatorioSaldoCredito: {
		name: "Saldo de Créditos",
		description: "Relatório de saldo de créditos",
		searchTokens: "relatorio, saldo, credito",
	},
};

const relatorioHistoricoPontoPt: NavModuleI18n = {
	relatorioHistoricoPonto: {
		name: "Histórico de Pontos",
		description: "Relatório de histórico de pontos",
		searchTokens: "relatorio, historico, pontos",
	},
};

const relatorioIndicadorAcessoPt: NavModuleI18n = {
	relatorioIndicadorAcesso: {
		name: "Indicador de Acesso",
		description: "Indicador de Acesso",
		searchTokens: "indicador, acesso",
	},
};

const relatorioOrcamentoPt: NavModuleI18n = {
	relatorioOrcamento: {
		name: "Orçamentos",
		description: "Orçamentos",
		searchTokens: "relatorio, orçamento",
	},
};

const relatorioConvidadoPt: NavModuleI18n = {
	relatorioConvidado: {
		name: "Convidados",
		description: "Convidados",
		searchTokens: "relatorio, convidados",
	},
};

const relatorioClienteComCobrancaAutomaticaBloqueadaPt: NavModuleI18n = {
	relatorioClienteComCobrancaAutomaticaBloqueada: {
		name: "Cobranças Automáticas Bloqueadas",
		description: "Cobranças Automáticas Bloqueadas",
		searchTokens:
			"clientes, cobrança, automática, bloqueada, cobrança bloqueada",
	},
};

const relatorioFechamentoAcessoPt: NavModuleI18n = {
	relatorioFechamentoAcesso: {
		name: "Fechamento Acessos",
		description: "Relatório de fechamento de acessos",
		searchTokens: "relatorio, fechamento, acessos",
	},
};

const relatorioTotalizadorAcessoPt: NavModuleI18n = {
	relatorioTotalizadorAcesso: {
		name: "Totalizador de Acessos",
		description: "Totalizador de Acessos",
		searchTokens: "totalizador, acessos",
	},
};

const relatorioTotalizadorTicketsPt: NavModuleI18n = {
	relatorioTotalizadorTickets: {
		name: "Totalizador de Tickets",
		description: "Totalizador de tickets",
		searchTokens: "totalizador, tickets",
	},
};

const relatorioArmarioPt: NavModuleI18n = {
	relatorioArmario: {
		name: "Armários",
		description: "Armários",
		searchTokens: "relatorio, armario",
	},
};

const relatorioListaAcessoPt: NavModuleI18n = {
	relatorioListaAcesso: {
		name: "Lista de Acessos",
		description: "Lista de Acessos",
		searchTokens: "lista, acesso",
	},
};

const relatorioListaChamadaPt: NavModuleI18n = {
	relatorioListaChamada: {
		name: "Lista de Chamada",
		description: "Lista de chamada",
		searchTokens: "lista, chamada",
	},
};

const relatorioFrequenciaOcupacaoPt: NavModuleI18n = {
	relatorioFrequenciaOcupacao: {
		name: "Frequência e Ocupação",
		description: "Frequência e Ocupação",
		searchTokens: "frequência, ocupação",
	},
};

const relatorioFrequenciaTurmasPt: NavModuleI18n = {
	relatorioFrequenciaTurmas: {
		name: "Relatório de Frequência de Turmas",
		description: "Frequência",
		searchTokens:
			"frequência, frequencia, turmas, relatório, relatório de frequência de turmas",
	},
};

const relatorioDescontoOcupacaoPt: NavModuleI18n = {
	relatorioDescontoOcupacao: {
		name: "Desconto por Ocupação na Turma",
		description: "Desconto por Ocupação na Turma",
		searchTokens: "desconto, ocupação, turma",
	},
};

const relatorioMapaTurmasPt: NavModuleI18n = {
	relatorioMapaTurma: {
		name: "Mapa de Turmas",
		description: "Mapa de Turmas",
		searchTokens: "mapa, turma, turmas",
	},
};

const relatorioConsultaTurmaPt: NavModuleI18n = {
	relatorioConsultaTurma: {
		name: "Consulta de Turmas",
		description: "Consulta de Turmas",
		searchTokens: "consulta, turma, turmas",
	},
};

const relatorioFechamentoCaixaPt: NavModuleI18n = {
	relatorioFechamentoCaixa: {
		name: "Fechamento de Caixa por Operador",
		description: "Fechamento de Caixa por Operador",
		searchTokens: "fechamento, caixa, operador",
	},
};

const relatorioCompetenciaMensalPt: NavModuleI18n = {
	relatorioCompetenciaMensal: {
		name: "Competência Mensal",
		description: "Competência Mensal",
		searchTokens: "competência, mensal",
	},
};

const relatorioFaturamentoPt: NavModuleI18n = {
	relatorioFaturamento: {
		name: "Faturamento",
		description: "Relatório de faturamento",
		searchTokens: "relatorio, faturamento",
	},
};

const relatorioFaturamentoRecebidoPt: NavModuleI18n = {
	relatorioFaturamentoRecebido: {
		name: "Faturamento Recebido",
		description: "Faturamento Recebido",
		searchTokens: "faturamento, recebido",
	},
};

const relatorioReceitaPorPeriodoPt: NavModuleI18n = {
	relatorioReceitaPorPeriodo: {
		name: "Receita por Período",
		description: "Receita por período",
		searchTokens: "receita, período",
	},
};

const relatorioParcelasPt: NavModuleI18n = {
	relatorioParcelas: {
		name: "Parcelas",
		description: "Relatório de Parcelas",
		searchTokens: "relatorio, parcelas",
	},
};

const pedidosPinPadPt: NavModuleI18n = {
	pedidosPinPad: {
		name: "Pedidos Pinpad",
		description: "Pedidos Pinpad",
		searchTokens: "pinpad, pedidos, pedidos pinpad",
	},
};

const relatorioSaldoContaCorrentePt: NavModuleI18n = {
	relatorioSaldoContaCorrente: {
		name: "Saldo Conta Corrente",
		description: "Relatório de saldo de conta corrente",
		searchTokens: "relatorio, saldo, conta corrente, conta, corrente",
	},
};

const relatorioTransacoesPixPt: NavModuleI18n = {
	relatorioTransacoesPix: {
		name: "Transações PIX",
		description: "Relatório de Transações PIX",
		searchTokens: "relatório, transações, pix",
	},
};

const relatorioConsultaRecibosPt: NavModuleI18n = {
	relatorioConsultaRecibos: {
		name: "Consulta de Recibos",
		description: "Consulta de recibos",
		searchTokens: "consulta, recibos",
	},
};

const relatorioCupomFiscalPt: NavModuleI18n = {
	relatorioCupomFiscal: {
		name: "Cupom Fiscal",
		description: "Cupom Fiscal",
		searchTokens: "cupom, fiscal",
	},
};

const relatorioComissaoProfessorPt: NavModuleI18n = {
	relatorioComissaoProfessor: {
		name: "Comissão para Professor",
		description: "Comissão para professor",
		searchTokens: "comissão, professor",
	},
};

const relatorioComissaoConsultorPt: NavModuleI18n = {
	relatorioComissaoConsultor: {
		name: "Comissão para Consultor",
		description: "Comissão para consultor",
		searchTokens: "comissão, consultor",
	},
};

const relatorioPrevisaoRenovacaoPt: NavModuleI18n = {
	relatorioPrevisaoRenovacao: {
		name: "Previsão de Renovação",
		description: "Previsão de Renovação",
		searchTokens:
			"previsão, renovação, relatório de previsão de renovação por contrato",
	},
};

const relatorioBvsPt: NavModuleI18n = {
	relatorioBvs: {
		name: "BVs",
		description: "Relatório de BVs",
		searchTokens: "bv, bvs",
	},
};

const relatorioRepassePt: NavModuleI18n = {
	relatorioRepasse: {
		name: "Repasse",
		description: "Repasse",
		searchTokens: "repasse",
	},
};

const relatorioPesquisaPt: NavModuleI18n = {
	relatorioPesquisa: {
		name: "Pesquisas",
		description: "Pesquisas",
		searchTokens: "pesquisas",
	},
};

const transacoesPixPt: NavModuleI18n = {
	transacoesPix: {
		name: "Transações Pix",
		description: "Transações Pix",
		searchTokens: "transacoes pix, pix, transações",
	},
};

const relatorioPersonalPt: NavModuleI18n = {
	relatorioPersonal: {
		name: "Personal",
		description: "Personal",
		searchTokens: "relatorio, personal",
	},
};

const relatorioProdutoComVigenciaPt: NavModuleI18n = {
	relatorioProdutoComVigencia: {
		name: "Produtos (com vigência)",
		description: "Produtos (com vigência)",
		searchTokens: "produto, com vigência, relatorio, vigência",
	},
};

const relatorioMovimentoProdutoPt: NavModuleI18n = {
	relatorioMovimentoProduto: {
		name: "Movimento de Produto",
		description: "Movimento de Produto",
		searchTokens: "movimento, produto",
	},
};

const relatorioCardexPt: NavModuleI18n = {
	relatorioCardex: {
		name: "Cardex",
		description: "Cardex",
		searchTokens: "cardex",
	},
};

const relatorioControleLogsPt: NavModuleI18n = {
	relatorioControleLogs: {
		name: "Controle de Logs",
		description: "Controle de Logs",
		searchTokens: "controle, logs",
	},
};

const relatorioComissaoPt: NavModuleI18n = {
	relatorioComissao: {
		name: "Comissão",
		description: "Comissão",
		searchTokens: "comissão",
	},
};

const relatorioDiarioPt: NavModuleI18n = {
	relatorioDiario: {
		name: "Diário",
		description: "Diário",
		searchTokens: "diario, diário",
	},
};

const relatorioAgendamentosPt: NavModuleI18n = {
	relatorioAgendamentos: {
		name: "Agendamentos",
		description: "Agendamentos",
		searchTokens: "agendamentos",
	},
};

const conviteAulaExperimentalOcupacaoPt: NavModuleI18n = {
	conviteAulaExperimentalOcupacao: {
		name: "Convite Aula Experimental",
		description: "Convite Aula Experimental",
		searchTokens: "convites, aula, experimental",
	},
};

const relatorioClientesSemSessaoPt: NavModuleI18n = {
	relatorioClientesSemSessao: {
		name: "Clientes sem Sessão",
		description: "Clientes sem Sessão",
		searchTokens: "clientes, sem sessão, sessão",
	},
};

export const relatorioSgpModalidadesSemTurmaMenuPt: NavModuleI18n = {
	relatorioSgpModalidadesSemTurma: {
		name: "SGP Modalidades sem Turma",
	},
};

export const relatorioSgpModalidadesComTurmaMenuPt: NavModuleI18n = {
	relatorioSgpModalidadesComTurma: {
		name: "SGP Modalidades com Turma",
	},
};

export const relatorioSgpTurmaMenuPt: NavModuleI18n = {
	relatorioSgpTurma: {
		name: "SGP Mapa Estatístico",
	},
};

export const relatorioSgpAvaliacoesFisicasMenuPt: NavModuleI18n = {
	relatorioSgpAvaliacoesFisicas: {
		name: "SGP Avaliações Fisicas",
	},
};

export const relatorioAcessoParentMenuPt: NavModuleI18n = {
	"relatorio-acesso": {
		name: "Relatórios de Acessos",
	},
};

export const relatorioClienteParentMenuPt: NavModuleI18n = {
	"relatorio-cliente": {
		name: "Relatórios de Clientes",
	},
};

export const relatorioComissaoParentMenuPt: NavModuleI18n = {
	"relatorio-comissao": {
		name: "Relatórios de Comissão",
	},
};

export const relatorioEstatisticoParentMenuPt: NavModuleI18n = {
	"relatorio-estatistico": {
		name: "Relatório Estatístico",
	},
};

export const relatorioFinanceiroParentMenuPt: NavModuleI18n = {
	"relatorio-financeiro": {
		name: "Relatório Financeiro",
	},
};

export const relatorioTurmaParentMenuPt: NavModuleI18n = {
	"relatorio-turma": {
		name: "Relatório de Turmas",
	},
};

export const relatorioOutrosParentMenuPt: NavModuleI18n = {
	"relatorio-outros": {
		name: "Outros relatórios",
	},
};

export const relatorioClientesComRestricoesPt: NavModuleI18n = {
	relatorioClientesComRestricoes: {
		name: "Clientes com restrições",
	},
};

const relatorioSMDPt: NavModuleI18n = {
	relatorioSMD: {
		name: "SMD",
		description: "SMD",
		searchTokens: "relatorio, smd",
	},
};

export const relatorioMenuPt: NavModuleI18n = {
	relatorios: {
		name: "Relatórios",
		description: `Relatórios`,
		searchTokens: "relatorios, relatórios",
	},
	...relatorioAcessoParentMenuPt,
	...relatorioClienteParentMenuPt,
	...relatorioComissaoParentMenuPt,
	...relatorioEstatisticoParentMenuPt,
	...relatorioFinanceiroParentMenuPt,
	...relatorioTurmaParentMenuPt,
	...relatorioOutrosParentMenuPt,
	...relatorioAgendamentosPt,
	...relarioClientesAniversariantesPt,
	...relatorioArmarioPt,
	...relatorioBvsPt,
	...relatorioCardexPt,
	...relatorioClienteSimplificadoPt,
	...relatorioClientePt,
	...relatorioClientesCanceladosPt,
	...relatorioClientesTrancadosPt,
	...relatorioClientesComAtestadoPt,
	...relatorioClientesComBonusPt,
	...relatorioClienteComCobrancaAutomaticaBloqueadaPt,
	...relatorioClientesSemSessaoPt,
	...relatorioComissaoPt,
	...relatorioComissaoConsultorPt,
	...relatorioComissaoProfessorPt,
	...relatorioCompetenciaMensalPt,
	...relatorioMapaTurmasPt,
	...relatorioConsultaTurmaPt,
	...relatorioConsultaRecibosPt,
	...relatorioContratosPorDuracaoPt,
	...relatorioControleLogsPt,
	...relatorioConvidadoPt,
	...relatorioCupomFiscalPt,
	...relatorioDescontoOcupacaoPt,
	...relatorioDiarioPt,
	...relatorioFaturamentoPt,
	...relatorioFaturamentoRecebidoPt,
	...relatorioFechamentoAcessoPt,
	...relatorioFechamentoCaixaPt,
	...relatorioFrequenciaOcupacaoPt,
	...relatorioFrequenciaTurmasPt,
	...relatorioGeralClientePt,
	...relatorioGympassPt,
	...relatorioHistoricoPontoPt,
	...relatorioIndicadorAcessoPt,
	...relatorioListaAcessoPt,
	...relatorioListaChamadaPt,
	...relatorioMapaTurmasPt,
	...relatorioMovimentoProdutoPt,
	...relatorioOrcamentoPt,
	...relatorioParcelasPt,
	...relatorioPersonalPt,
	...relatorioPesquisaPt,
	...relatorioPrevisaoRenovacaoPt,
	...relatorioProdutoComVigenciaPt,
	...relatorioReceitaPorPeriodoPt,
	...relatorioRepassePt,
	...relatorioSaldoContaCorrentePt,
	...relatorioSaldoCreditoPt,
	...relatorioTotalizadorAcessoPt,
	...relatorioTotalizadorTicketsPt,
	...relatorioTransacoesPixPt,
	...relatorioClientesVisitantesPt,
	...relatorioSgpModalidadesComTurmaMenuPt,
	...relatorioSgpTurmaMenuPt,
	...relatorioSgpModalidadesSemTurmaMenuPt,
	...relatorioSgpAvaliacoesFisicasMenuPt,
	...clientePt,
	...pedidosPinPadPt,
	...transacoesPixPt,
	...conviteAulaExperimentalOcupacaoPt,
	...relatorioClientesComRestricoesPt,
	...relatorioSMDPt,
};
