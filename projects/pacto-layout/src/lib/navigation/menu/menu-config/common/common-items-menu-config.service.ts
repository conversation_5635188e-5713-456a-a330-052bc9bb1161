import { Injectable } from "@angular/core";
import { PlataformModuleConfig, PlatformMenuItem } from "../../../models";
import { PermissaoService } from "../../../permissao/permissao.service";

@Injectable({
	providedIn: "root",
})
export class CommonItemsMenuConfigService {
	constructor(private permissaoService: PermissaoService) {}

	get admCadastroBalancoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroBalanco",
			permissaoAdm: "12.03 - Balanço",
			permitido: this.permissaoService.temPermissaoAdm("12.03"),
			favoriteIdentifier: "BALANCO",
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/produtos/balanco",
			},
		};
	}

	get admCadastroCardexMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCardex",
			permissaoAdm: "12.05 - Visualizar Cardex",
			permitido: this.permissaoService.temPermissaoAdm("12.05"),
			module: PlataformModuleConfig.ADM_LEGADO,
			favoriteIdentifier: "CARDEX",
			route: {
				queryParams: {
					funcionalidadeNome: "CARDEX",
					windowTitle: "Cardex",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get admCadastroCompraMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCompra",
			permissaoAdm: "12.01 - Cadastrar Compra",
			permitido: this.permissaoService.temPermissaoAdm("12.01"),
			module: PlataformModuleConfig.ADM_LEGADO,
			favoriteIdentifier: "COMPRA",
			route: {
				internalLink: "/adm/compra-estoque",
			},
		};
	}

	get admCadastroConfigurarProdutoEstoqueMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroConfigurarProdutoEstoque",
			permissaoAdm: "12.06 - Adicionar Produto ao Controle de Estoque",
			permitido: this.permissaoService.temPermissaoAdm("12.06"),
			favoriteIdentifier: "CONFIGURAR_PRODUTO_ESTOQUE",
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/produtos/produto-estoque",
			},
		};
	}

	get admSolicitacaoCompraMenuItem(): PlatformMenuItem {
		return {
			id: "solicitacaoCompraMenuItem",
			permissaoAdm: "10.09 - Permitir cadastrar solicitação de compras",
			permitido: this.permissaoService.temPermissaoAdm("10.09"),
			favoriteIdentifier: "SOLICITACAO_COMPRA",
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/solicitacao-compra",
			},
		};
	}

	get admCadastroPosicaoEstoqueMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroPosicaoEstoque",
			permissaoAdm: "12.08 - Visualizar Posição do Estoque",
			permitido: this.permissaoService.temPermissaoAdm("12.08"),
			favoriteIdentifier: "POSICAO_DO_ESTOQUE",
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/posicao-estoque",
			},
		};
	}

	get admCadastroAutorizacaoAcessoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroAutorizacaoAcesso",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm:
				"1.10 - Incluir / Alterar autorização de acesso em grupo empresarial",
			permitido: this.permissaoService.temPermissaoAdm("1.10"),
			favoriteIdentifier: "AUTORIZACAO_ACESSO",
			route: {
				queryParams: {
					funcionalidadeNome: "AUTORIZACAO_ACESSO",
					windowTitle: "Autorização de acesso",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get admRelatorioHistoricoPontoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioHistoricoPonto",
			permissaoConfiguracaoEmpresaAdm:
				"Empresa configurada para trabalhar com pontuação",
			permissaoAdm: "5.67 - Visualizar relatório de histórico de pontos",
			permitido:
				this.permissaoService.temPermissaoAdm("5.67") &&
				this.permissaoService.temConfiguracaoEmpresaAdm(
					"trabalharComPontuacao"
				),
			module: PlataformModuleConfig.ADM_LEGADO,
			favoriteIdentifier: "HISTORICO_PONTOS_ALUNO",
			route: {
				queryParams: {
					funcionalidadeNome: "HISTORICO_PONTOS_ALUNO",
					openAsPopup: true,
					windowTitle: "Relatório Gympass",
					windowWidth: 850,
					windowHeight: 595,
				},
			},
		};
	}

	get admCadastroVendaConsumidorMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroVendaConsumidor",
			permissaoAdm: "4.16 - Venda de Consumidor",
			permitido: this.permissaoService.temPermissaoAdm("4.16"),
			module: PlataformModuleConfig.ADM_LEGADO,
			favoriteIdentifier: "VENDA_CONSUMIDOR",
			route: {
				queryParams: {
					funcionalidadeNome: "VENDA_CONSUMIDOR",
					windowTitle: "Venda de Consumidor",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get admCadastroMovContaCorrenteClienteMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroMovContaCorrenteCliente",
			module: PlataformModuleConfig.ADM_LEGADO,
			favoriteIdentifier: "MOVIMENTO_CC_CLIENTE",
			permissaoAdm: "2.16 - Movimento de Conta Corrente do Cliente",
			permitido: this.permissaoService.temRecursoAdm("2.16"),
			route: {
				queryParams: {
					funcionalidadeNome: "MOVIMENTO_CC_CLIENTE",
					windowTitle: "Mov. de CC do Cliente",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}
}
