import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PlataformModuleConfig, PlatformMenuItem } from "../../../../models";
import { PermissaoService } from "../../../../permissao/permissao.service";
import { CommonItemsMenuConfigService } from "../../common/common-items-menu-config.service";
import { MenuConfigService } from "../../menu-config.service";
import { PactoLayoutSDKWrapper } from "../../../../../sdk-wrapper/sdk-wrappers";

@Injectable({
	providedIn: "root",
})
export class OperacoesMenuConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private commonItemsMenuConfigService: CommonItemsMenuConfigService,
		private permissaoService: PermissaoService,
		private sdkWrapper: PactoLayoutSDKWrapper
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([
			this.commonItemsMenuConfigService.admCadastroAutorizacaoAcessoMenuItem,
			this.commonItemsMenuConfigService.admCadastroBalancoMenuItem,
			this.caixaEmAbertoMenuItem,
			this.caixaAbertoMenuItem,
			this.commonItemsMenuConfigService.admCadastroCardexMenuItem,
			this.commonItemsMenuConfigService.admCadastroCompraMenuItem,
			this.commonItemsMenuConfigService
				.admCadastroConfigurarProdutoEstoqueMenuItem,
			this.commonItemsMenuConfigService.admSolicitacaoCompraMenuItem,
			this.orcamentoMenuItem,
			this.diariaMenuItem,
			this.freepassMenuItem,
			this.gestaoArmarioMenuItem,
			this.gestaoNegativacoesMenuItem,
			this.gestaoPersonalMenuItem,
			this.gestaoRemessasMenuItem,
			this.gestaoBoletosOnlineMenuItem,
			this.gestaoTransacoesMenuItem,
			this.gestaoTurmaMenuItem,
			this.gestaoVendasOnlineMenuItem,
			this.imprimirReciboEmBrancoMenuItem,
			this.negociacaoMenuItem,
			this.operacoesColetivaMenuItem,
			this.commonItemsMenuConfigService.admCadastroPosicaoEstoqueMenuItem,
			this.registroAcessoManualMenuItem,
			this.sorteioMenuItem,
			this.vendaAvulsaMenuItem,
			this.vendaRapidaMenuItem,

			this.operacoesParentItem,
		]);
	}

	get operacoesParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes",
			configMenuExplorar: {
				submenus: [
					this.operacoesEstoqueParentItem,
					this.operacoesGestaoParentItem,
					this.operacoesVendaParentItem,
					this.operacoesOutrosParentItem,
				],
			},
		};

		this.setParentIdMenuExplorar(menu);

		return menu;
	}

	get operacoesEstoqueParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes-estoque",
			configMenuExplorar: {
				submenus: [
					this.commonItemsMenuConfigService.admCadastroBalancoMenuItem,
					this.commonItemsMenuConfigService.admCadastroCardexMenuItem,
					this.commonItemsMenuConfigService.admCadastroCompraMenuItem,
					this.commonItemsMenuConfigService
						.admCadastroConfigurarProdutoEstoqueMenuItem,
					this.commonItemsMenuConfigService.admSolicitacaoCompraMenuItem,
					this.commonItemsMenuConfigService.admCadastroPosicaoEstoqueMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get operacoesGestaoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes-gestao",
			configMenuExplorar: {
				submenus: [
					this.gestaoArmarioMenuItem,
					this.gestaoNegativacoesMenuItem,
					this.gestaoPersonalMenuItem,
					this.gestaoRemessasMenuItem,
					this.gestaoBoletosOnlineMenuItem,
					this.gestaoTransacoesMenuItem,
					this.gestaoTurmaMenuItem,
					this.gestaoVendasOnlineMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get operacoesVendaParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes-venda",
			configMenuExplorar: {
				submenus: [
					this.negociacaoMenuItem,
					this.caixaEmAbertoMenuItem,
					this.caixaAbertoMenuItem,
					this.diariaMenuItem,
					this.freepassMenuItem,
					this.orcamentoMenuItem,
					this.vendaAvulsaMenuItem,
					this.vendaRapidaMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get operacoesOutrosParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes-outros",
			configMenuExplorar: {
				submenus: [
					this.commonItemsMenuConfigService
						.admCadastroAutorizacaoAcessoMenuItem,
					this.imprimirReciboEmBrancoMenuItem,
					this.operacoesColetivaMenuItem,
					this.registroAcessoManualMenuItem,
					this.sorteioMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get freepassMenuItem(): PlatformMenuItem {
		return {
			id: "freepass",
			permissaoAdm: "2.51 - Lançar Free Pass",
			permitido: this.permissaoService.temPermissaoAdm("2.51"),
			favoriteIdentifier: "FREE_PASS",
			route: {
				internalLink: "/adm/freepass",
			},
		};
	}

	get diariaMenuItem(): PlatformMenuItem {
		return {
			id: "diaria",
			permissaoAdm: "4.01 - Aula Avulsa",
			permitido: this.permissaoService.temRecursoAdm("4.01"),
			favoriteIdentifier: "DIARIA",
			route: {
				internalLink: "/adm/diarias",
			},
		};
	}

	get vendaAvulsaMenuItem(): PlatformMenuItem {
		return {
			id: "vendaAvulsa",
			permissaoAdm: "4.11 - Venda Avulsa",
			permitido: this.permissaoService.temRecursoAdm("4.11"),
			favoriteIdentifier: "VENDA_AVULSA",
			route: {
				internalLink: "/adm/venda-avulsa",
			},
		};
	}

	get negociacaoMenuItem(): PlatformMenuItem {
		return {
			id: "negociacao",
			favoriteIdentifier: "NEGOCIACAO",
			permitido: this.permissaoService.temPermissaoAdm("2.53"),
			route: {
				internalLink: "/adm/negociacao/contrato",
			},
		};
	}

	get vendaRapidaMenuItem(): PlatformMenuItem {
		return {
			id: "vendaRapida",
			permissaoAdm: "4.40 - Venda Rápida",
			permitido: this.permissaoService.temPermissaoAdm("4.40"),
			favoriteIdentifier: "ADM_VENDA_RAPIDA",
			route: {
				queryParams: {
					funcionalidadeNome: "ADM_VENDA_RAPIDA",
					jspPage: "inclusaoAlunoVenda.jsp",
				},
			},
		};
	}

	get caixaAbertoMenuItem(): PlatformMenuItem {
		return {
			id: "caixaAberto",
			route: {
				queryParams: {
					funcionalidadeNome: "CAIXA_EM_ABERTO",
					jspPage: "tela8.jsp",
				},
			},
			permitido:
				this.permissaoService.temPermissaoAdm("2.44") &&
				!this.permissaoService.temPermissaoAdm("9999.8"),
		};
	}

	get caixaEmAbertoMenuItem(): PlatformMenuItem {
		return {
			id: "caixaEmAberto",
			permissaoAdm: "2.44 - Operação - Caixa em Aberto",
			permitido:
				this.permissaoService.temPermissaoAdm("2.44") &&
				this.permissaoService.temPermissaoAdm("9999.8"),
			favoriteIdentifier: "CAIXA_EM_ABERTO",
			route: {
				internalLink: "/adm/caixa-em-aberto",
			},
		};
	}

	get fechamentoCaixaOperadorMenuItem(): PlatformMenuItem {
		return {
			id: "fechamentoCaixaOperador",
			permissaoAdm: "6.01 - Fechamento de Caixa Por Operador",
			permitido: this.permissaoService.temPermissaoAdm("6.01"),
			favoriteIdentifier: "FECHAMENTO_CAIXA_OPERADOR",
			route: {
				queryParams: {
					funcionalidadeNome: "FECHAMENTO_CAIXA_OPERADOR",
					windowTitle: "Fechamento de Caixa por Operador",
					openAsPopup: true,
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get registroAcessoManualMenuItem(): PlatformMenuItem {
		return {
			id: "registroAcessoManual",
			permissaoAdm: "9.38 - Permite registrar acesso manual",
			permitido: this.permissaoService.temPermissaoAdm("9.38"),
			favoriteIdentifier: "REGISTRAR_ACESSO_AVULSO",
			route: {
				queryParams: {
					funcionalidadeNome: "REGISTRAR_ACESSO_AVULSO",
					windowTitle: "Registrar Acesso Manual",
					openAsPopup: true,
					windowWidth: 1024,
					windowHeight: 700,
				},
			},
		};
	}

	get sorteioMenuItem(): PlatformMenuItem {
		return {
			id: "sorteio",
			permissaoAdm: "2.62 - Permitir acesso ao recurso de Sorteio",
			permitido: this.permissaoService.temPermissaoAdm("2.62"),
			favoriteIdentifier: "SORTEIO",
			route: {
				internalLink: "/adm/outras-opcoes/sorteios",
			},
		};
	}

	get imprimirReciboEmBrancoMenuItem(): PlatformMenuItem {
		return {
			id: "imprimirReciboEmBranco",
			permitido: true,
			favoriteIdentifier: "IMPRIME_RECIBO_BANCO",
			route: {
				queryParams: {
					funcionalidadeNome: "IMPRIME_RECIBO_BANCO",
					windowTitle: "Imprimir Recibo em Branco",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get operacoesColetivaMenuItem(): PlatformMenuItem {
		return {
			id: "operacoesColetivas",
			permissaoAdm: "2.75 - Permitir acesso ao recurso de Operações Coletivas",
			permitido: this.permissaoService.temPermissaoAdm("2.75"),
			favoriteIdentifier: "OPERACOES_COLETIVAS",
			route: {
				queryParams: {
					funcionalidadeNome: "OPERACOES_COLETIVAS",
					windowTitle: "Operações Coletivas",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get orcamentoMenuItem(): PlatformMenuItem {
		return {
			id: "orcamento",
			permissaoAdm: "5.18 - Orçamento Turmas",
			permitido: this.permissaoService.temRecursoAdm("5.18"),
			favoriteIdentifier: "ORCAMENTO",
			route: {
				queryParams: {
					funcionalidadeNome: "ORCAMENTO",
					jspPage: "realizarOrcamento.jsp",
					// valor url enum = semValor
				},
			},
		};
	}

	get gestaoNfceItem(): PlatformMenuItem {
		return {
			id: "gestaoNfce",
			permissaoConfiguracaoEmpresaAdm: "Empresa configurada para utilziar NFCe",
			permissaoAdm: "4.38 - Gestão de NFC-e",
			permitido:
				this.permissaoService.temPermissaoAdm("4.38") &&
				this.permissaoService.temConfiguracaoEmpresaAdm("usarnfce"),
			favoriteIdentifier: "GESTAO_NFCE",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_NFCE",
					windowTitle: "Gestão de NFC-e",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get gestaoNotasItem(): PlatformMenuItem {
		return {
			id: "gestaoNotas",
			permissaoAdm: "4.38 - Gestão de NFC-e",
			permitido: this.permissaoService.temPermissaoAdm("4.38"),
			favoriteIdentifier: "GESTAO_NOTAS",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_NOTAS",
					windowTitle: "Gestão de Notas",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get gestaoTurmaMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoTurma",
			permissaoAdm: "9.54 - Permitir acesso ao Gestão de Turma",
			permitido: this.permissaoService.temPermissaoAdm("9.54"),
			// favoriteIdentifier: "GESTAO_DE_TURMA",
			route: {
				internalLink: "/adm/gestao-turma",
			},
		};
	}

	get gestaoTransacoesMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoTransacoes",
			permissaoAdm: "4.20 - Gestão Transações",
			permitido: this.permissaoService.temPermissaoAdm("4.20"),
			favoriteIdentifier: "GESTAO_DE_TRANSACOES",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_DE_TRANSACOES",
					jspPage: "gestaoTransacoes.jsp",
					// valor url enum = semValor
				},
			},
		};
	}

	get gestaoRemessasMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoRemessas",
			favoriteIdentifier: "GESTAO_DE_REMESSAS",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "2.68 Gestão de Remessas",
			permitido: this.permissaoService.temRecursoAdm("2.68"),
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_DE_REMESSAS",
					jspPage: "gestaoRemessas.jsp",
					// valor url enum = gestaoRemessas
				},
			},
		};
	}

	get gestaoBoletosOnlineMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoBoletosOnline",
			favoriteIdentifier: "GESTAO_DE_BOLETOS_ONLINE",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "4.48 - Permitir acesso ao Gestão de Boletos Online",
			permitido: this.permissaoService.temPermissaoAdm("4.48"),
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_DE_BOLETOS_ONLINE",
					jspPage: "gestaoBoletosOnline.jsp",
				},
			},
		};
	}

	get gestaoPersonalMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoPersonal",
			permissaoAdm: "9.41 - Permitir acesso ao Gestão de Personal",
			permitido: this.permissaoService.temPermissaoAdm("9.41"),
			favoriteIdentifier: "GESTAO_PERSONAL",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_PERSONAL",
					jspPage: "gestaoPersonal.jsp",
					// valor url enum = semValor
				},
			},
		};
	}

	get gestaoNegativacoesMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoNegativacoes",
			permissaoAdm: "2.81 - Visualizar Gestão de Negativações",
			permitido: this.permissaoService.temPermissaoAdm("2.81"),
			favoriteIdentifier: "GESTAO_NEGATIVACOES",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_NEGATIVACOES",
					jspPage: "relatorio/parcelaSPC.jsp",
				},
			},
		};
	}

	get gestaoArmarioMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoArmario",
			permissaoAdm: "9.43 - Permitir acesso ao Gestão de Armários",
			permitido: this.permissaoService.temPermissaoAdm("9.43"),
			favoriteIdentifier: "GESTAO_DE_ARMARIOS",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_DE_ARMARIOS",
					jspPage: "gestaoArmario.jsp",
					// valor url enum = gestaoArmario
				},
			},
		};
	}

	get gestaoVendasOnlineMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoVendasOnline",
			permissaoAdm: "4.43 - Gestão de Vendas Online",
			permissaoModulo: "Vendas online habilitado",
			permitido:
				this.permissaoService.temPermissaoAdm("4.43") &&
				this.permissaoService.temModuloHabilitado("NVO"),
			favoriteIdentifier: "VENDAS_ONLINE",
			route: {
				queryParams: {
					funcionalidadeNome: "VENDAS_ONLINE",
					jspPage: "gestaoVendasOnline.jsp",
				},
			},
		};
	}

	get gestaoVendasOnlineAdquiraMenuItem(): PlatformMenuItem {
		return {
			id: "gestaoVendasOnline",
			permissaoModulo: "Vendas online desabilitado",
			permitido: this.permissaoService.temModuloHabilitado("NVO"),
			favoriteIdentifier: "VENDAS_ONLINE_ADQUIRA",
			route: {
				queryParams: {
					funcionalidadeNome: "VENDAS_ONLINE_ADQUIRA",
					jspPage: "vendasOnlineAdquira.jsp",
				},
			},
		};
	}

	get socialMailingMenuItem(): PlatformMenuItem {
		return {
			id: "socialMailing",
			permissaoAdm: "2.40 - Acessar o Social Mailing",
			permitido: this.permissaoService.temPermissaoAdm("2.40"),
			favoriteIdentifier: "SOCIAL_MAILING",
			route: {
				queryParams: {
					funcionalidadeNome: "SOCIAL_MAILING",
					jspPage: "socialMailing.jsp",
					windowTitle: "Social Mailing",
					openAsPopup: true,
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get isUserBeta(): boolean {
		return (
			this.sdkWrapper.sessionService.loggedUser.username.toUpperCase() ===
			"PACTOBR"
		);
	}
}
