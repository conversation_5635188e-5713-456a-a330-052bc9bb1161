import { Injectable } from "@angular/core";
import { PlataformModuleConfig, PlatformMenuItem } from "../../../../models";
import { Observable, of } from "rxjs";
import { MenuConfigService } from "../../menu-config.service";
import { CommonItemsMenuConfigService } from "../../common/common-items-menu-config.service";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PermissaoService } from "../../../../permissao/permissao.service";

@Injectable({
	providedIn: "root",
})
export class RelatoriosMenuConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private commonItemsMenuConfigService: CommonItemsMenuConfigService,
		private permissaoService: PermissaoService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([this.relatorioMenuParentItem]);
	}

	get relatorioMenuParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorios",
			configMenuSidebar: {
				submenus: [
					this.relarioClientesAniversariantesMenuItem,
					this.relatorioArmarioMenuItem,
					this.relatorioBvsMenuItem,
					this.clienteMenuItem,
					this.relatorioClienteMenuItem,
					this.relatorioClientesCanceladosMenuItem,
					this.relatorioClientesComAtestadoMenuItem,
					this.relatorioClientesComBonusMenuItem,
					this.relatorioClientesComRestricoesMenuItem,
					this.relatorioClientesTrancadosMenuItem,
					this.relatorioClienteComCobrancaAutomaticaBloqueadaMenuItem,
					this.relatorioComissaoConsultorMenuItem,
					this.relatorioComissaoProfessorMenuItem,
					this.relatorioCompetenciaMensalMenuItem,
					this.relatorioConsultaRecibosMenuItem,
					this.relatorioConsultaTurmaMenuItem,
					this.relatorioContratosPorDuracaoMenuItem,
					this.relatorioControleLogsMenuItem,
					this.relatorioConvidadoMenuItem,
					this.conviteAulaExperimentalMenuItem,
					this.relatorioDescontoOcupacaoMenuItem,
					this.relatorioFaturamentoMenuItem,
					this.relatorioFaturamentoRecebidoMenuItem,
					this.relatorioFechamentoAcessoMenuItem,
					this.relatorioFechamentoCaixaMenuItem,
					this.relatorioFrequenciaOcupacaoMenuItem,
					this.relatorioFrequenciaTurmasMenuItem,
					this.relatorioGeralClienteMenuItem,
					this.relatorioGympassMenuItem,
					this.commonItemsMenuConfigService.admRelatorioHistoricoPontoMenuItem,
					this.relatorioIndicadorAcessoMenuItem,
					this.relatorioListaAcessoMenuItem,
					this.relatorioListaChamadaMenuItem,
					this.relatorioClienteSimplificadoMenuItem,
					this.relatorioMapaTurmasMenuItem,
					this.commonItemsMenuConfigService
						.admCadastroMovContaCorrenteClienteMenuItem,
					this.relatorioOrcamentoMenuItem,
					this.relatorioParcelasMenuItem,
					this.pedidosPinPadMenuItem,
					this.relatorioPersonalMenuItem,
					this.relatorioPesquisaMenuItem,
					this.relatorioPrevisaoRenovacaoMenuItem,
					this.relatorioProdutoComVigenciaMenuItem,
					this.relatorioReceitaPorPeriodoMenuItem,
					this.relatorioRepasseMenuItem,
					this.relatorioSaldoContaCorrenteMenuItem,
					this.relatorioSaldoCreditoMenuItem,
					this.relatorioSgpAvaliacoesFisicasItem,
					this.relatorioSgpModalidadesComTurmaItem,
					this.relatorioSgpTurmaItem,
					this.relatorioSgpModalidadesSemTurmaItem,
					this.relatorioTotalizadorAcessoMenuItem,
					this.relatorioTotalizadorTicketsMenuItem,
					this.relatorioTransacoesPixMenuItem,
					this.commonItemsMenuConfigService.admCadastroVendaConsumidorMenuItem,
					this.relatorioClientesVisitantesMenuItem,
					this.relatorioSMDMenuItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.relatorioAcessoParentItem,
					this.relatorioClienteParentItem,
					this.relatorioComissaoParentItem,
					this.relatorioEstatisticoParentItem,
					this.relatorioFinanceiroParentItem,
					this.relatorioTurmaParentItem,
					this.relatorioOutrosParentItem,
				],
			},
		};
		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioAcessoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorio-acesso",
			configMenuExplorar: {
				submenus: [
					this.relatorioFechamentoAcessoMenuItem,
					this.relatorioIndicadorAcessoMenuItem,
					this.relatorioListaAcessoMenuItem,
					this.relatorioTotalizadorAcessoMenuItem,
					this.relatorioTotalizadorTicketsMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioClienteParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorio-cliente",
			configMenuExplorar: {
				submenus: [
					this.relarioClientesAniversariantesMenuItem,
					this.clienteMenuItem,
					this.relatorioClientesCanceladosMenuItem,
					this.relatorioClientesComAtestadoMenuItem,
					this.relatorioClientesComBonusMenuItem,
					this.relatorioClientesComRestricoesMenuItem,
					this.relatorioClientesTrancadosMenuItem,
					this.relatorioClienteComCobrancaAutomaticaBloqueadaMenuItem,
					this.relatorioContratosPorDuracaoMenuItem,
					this.relatorioConvidadoMenuItem,
					this.relatorioGeralClienteMenuItem,
					this.relatorioGympassMenuItem,
					this.commonItemsMenuConfigService.admRelatorioHistoricoPontoMenuItem,
					this.relatorioClienteSimplificadoMenuItem,
					this.commonItemsMenuConfigService.admCadastroVendaConsumidorMenuItem,
					this.relatorioClientesVisitantesMenuItem,
					this.relatorioSMDMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioComissaoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorio-comissao",
			configMenuExplorar: {
				submenus: [
					this.relatorioComissaoConsultorMenuItem,
					this.relatorioComissaoProfessorMenuItem,
					this.relatorioRepasseMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioEstatisticoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorio-estatistico",
			configMenuExplorar: {
				submenus: [
					this.relatorioPrevisaoRenovacaoMenuItem,
					this.relatorioBvsMenuItem,
					this.relatorioClienteMenuItem,
					this.relatorioPesquisaMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioFinanceiroParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorio-financeiro",
			configMenuExplorar: {
				submenus: [
					this.relatorioCompetenciaMensalMenuItem,
					this.relatorioConsultaRecibosMenuItem,
					this.relatorioFaturamentoMenuItem,
					this.relatorioFaturamentoRecebidoMenuItem,
					this.relatorioFechamentoCaixaMenuItem,
					this.commonItemsMenuConfigService
						.admCadastroMovContaCorrenteClienteMenuItem,
					this.relatorioParcelasMenuItem,
					this.pedidosPinPadMenuItem,
					this.relatorioReceitaPorPeriodoMenuItem,
					this.relatorioSaldoContaCorrenteMenuItem,
					this.relatorioSaldoCreditoMenuItem,
					this.transacoesPixMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioTurmaParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorio-turma",
			configMenuExplorar: {
				submenus: [
					this.relatorioConsultaTurmaMenuItem,
					this.conviteAulaExperimentalMenuItem,
					this.relatorioDescontoOcupacaoMenuItem,
					this.relatorioFrequenciaOcupacaoMenuItem,
					this.relatorioFrequenciaTurmasMenuItem,
					this.relatorioListaChamadaMenuItem,
					this.relatorioMapaTurmasMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioOutrosParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorio-outros",
			configMenuExplorar: {
				submenus: [
					this.relatorioArmarioMenuItem,
					this.relatorioControleLogsMenuItem,
					this.relatorioOrcamentoMenuItem,
					this.relatorioPersonalMenuItem,
					this.relatorioProdutoComVigenciaMenuItem,
					this.relatorioSgpModalidadesComTurmaItem,
					this.relatorioSgpTurmaItem,
					this.relatorioSgpModalidadesSemTurmaItem,
					this.relatorioSgpAvaliacoesFisicasItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioClientesVisitantesMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesVisitantes",
			favoriteIdentifier: "RELATORIO_VISITANTES",
			permissaoAdm: "9.82 - Relatório de Visitantes",
			permitido: this.permissaoService.temPermissaoAdm("9.82"),
			route: {
				internalLink: "/adm/relatorios/relatorio-visitantes",
			},
		};
	}

	get relatorioContratosPorDuracaoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioContratosPorDuracao",
			favoriteIdentifier: "CONTRATOS_DURACAO",
			permissaoAdm: "6.10 - Relatório de Contratos por Duração",
			permitido: this.permissaoService.temPermissaoAdm("6.10"),
			route: {
				internalLink: "/adm/relatorios/contratos-por-duracao",
			},
		};
	}

	get relatorioGympassMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioGympass",
			permissaoAdm: "9.37 - Relatório de GymPass por Período",
			permitido: this.permissaoService.temPermissaoAdm("9.37"),
			favoriteIdentifier: "GYM_PASS_RELATORIO",
			route: {
				internalLink: "/adm/relatorios/relatorio-gympass",
			},
		};
	}

	get relatorioSaldoCreditoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioSaldoCredito",
			favoriteIdentifier: "SALDO_CREDITO",
			permissaoAdm: "6.26 - Relatório de Saldo de Créditos",
			permitido: this.permissaoService.temPermissaoAdm("6.26"),
			route: {
				internalLink: "/adm/relatorios/relatorio-saldo-credito",
			},
		};
	}

	get transacoesPixMenuItem(): PlatformMenuItem {
		return {
			id: "transacoesPix",
			favoriteIdentifier: "TRANSACOES_PIX",
			permissaoAdm: "9.85 - Relatório de Transações Pix",
			permitido: this.permissaoService.temPermissaoAdm("9.85"),
			route: {
				queryParams: {
					funcionalidadeNome: "TRANSACOES_PIX",
					popupFullscreen: true,
					windowTitle: "Transações Pix",
				},
			},
		};
	}

	get relatorioIndicadorAcessoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioIndicadorAcesso",
			permissaoAdm: "9.80 - Relatório de Indicador de Acessos",
			permitido: this.permissaoService.temPermissaoAdm("9.80"),
			favoriteIdentifier: "INDICADOR_ACESSOS",
			route: {
				internalLink: "/adm/relatorios/relatorios-acessos/indicador-acessos",
			},
		};
	}

	get relatorioConvidadoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioConvidado",
			permissaoAdm: "9.49 - Relatório de convidados",
			permitido: this.permissaoService.temPermissaoAdm("9.49"),
			favoriteIdentifier: "RELATORIO_CONVIDADOS",
			route: {
				internalLink: "/adm/relatorios/relatorio-convidados",
			},
		};
	}

	get relatorioTotalizadorAcessoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioTotalizadorAcesso",
			permissaoAdm: "9.96 - Relatório de Totalizador de Acessos",
			permitido: this.permissaoService.temPermissaoAdm("9.96"),
			favoriteIdentifier: "TOTALIZADOR_ACESSOS",
			route: {
				internalLink: "/adm/relatorios/relatorios-acessos/totalizador-acessos",
			},
		};
	}

	get relatorioListaAcessoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioListaAcesso",
			permissaoAdm: "6.09 - Relatório de lista de Acessos",
			permitido: this.permissaoService.temPermissaoAdm("6.09"),
			favoriteIdentifier: "LISTA_ACESSOS",
			route: {
				internalLink: "/adm/relatorios/relatorios-acessos/lista-acessos",
			},
		};
	}

	get relatorioArmarioMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioArmario",
			permissaoAdm: "9.90 - Relatório de Armários",
			permitido: this.permissaoService.temPermissaoAdm("9.90"),
			favoriteIdentifier: "RELATORIO_GERAL_ARMARIOS",
			route: {
				internalLink: "/adm/relatorios/relatorio-armario",
			},
		};
	}

	get relatorioProdutoComVigenciaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioProdutoComVigencia",
			permissaoAdm: "9.94 - Relatório de Produtos com Vigência",
			permitido: this.permissaoService.temPermissaoAdm("9.94"),
			favoriteIdentifier: "RELATORIO_PRODUTOS",
			route: {
				internalLink: "/adm/relatorios/relatorio-produtos-vigencia",
			},
		};
	}

	get relatorioPersonalMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioPersonal",
			permissaoAdm: "9.42 - Relatório de Personal",
			permitido: this.permissaoService.temPermissaoAdm("9.42"),
			favoriteIdentifier: "RELATORIO_DE_PERSONAL",
			route: {
				internalLink: "/adm/relatorios/relatorio-personal",
			},
		};
	}

	get relatorioGeralClienteMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioGeralCliente",
			permissaoAdm: "6.22 - Relatório Geral de Clientes",
			permitido: this.permissaoService.temPermissaoAdm("6.22"),
			favoriteIdentifier: "GERAL_CLIENTES",
			route: {
				queryParams: {
					funcionalidadeNome: "GERAL_CLIENTES",
					jspPage: "listasRelatorios.jsp",
				},
			},
		};
	}

	get clienteMenuItem(): PlatformMenuItem {
		return {
			id: "cliente",
			permissaoAdm: "9.88 - Relatório de Cliente",
			permitido: this.permissaoService.temRecursoAdm("9.88"),
			favoriteIdentifier: "CLIENTE",
			route: {
				queryParams: {
					funcionalidadeNome: "CLIENTE",
					openAsPopup: true,
					windowTitle: "Cliente",
					windowWidth: 980,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioClienteMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioCliente",
			permissaoAdm: "6.23 - Relatório de Clientes",
			permitido: this.permissaoService.temPermissaoAdm("6.23"),
			favoriteIdentifier: "RELATORIO_CLIENTES",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_CLIENTES",
					openAsPopup: true,
					windowTitle: "Relatório de Clientes",
					windowWidth: 980,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioClienteSimplificadoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClienteSimplificado",
			permissaoAdm: "6.02 - Relatório de Lista de Clientes Simplificada",
			permitido: this.permissaoService.temPermissaoAdm("6.02"),
			favoriteIdentifier: "LISTA_CLIENTES_SIMPLIFICADA",
			route: {
				queryParams: {
					funcionalidadeNome: "LISTA_CLIENTES_SIMPLIFICADA",
					jspPage: "listaClientesDadosBasicos.jsp",
				},
			},
		};
	}

	get relatorioClientesCanceladosMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesCancelados",
			permissaoAdm: "9.95 - Relatório de Clientes Cancelados",
			permitido: this.permissaoService.temPermissaoAdm("9.95"),
			favoriteIdentifier: "RELATORIO_CLIENTES_CANCELADOS",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_CLIENTES_CANCELADOS",
					jspPage: "listasRelatoriosAlunosCancelados.jsp",
				},
			},
		};
	}

	get relatorioClientesTrancadosMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesTrancados",
			permissaoAdm: "9.77 - Relatório de Clientes Trancados",
			permitido: this.permissaoService.temPermissaoAdm("9.77"),
			favoriteIdentifier: "RELATORIO_CLIENTES_TRANCADOS",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_CLIENTES_TRANCADOS",
					jspPage: "listasRelatoriosAlunosTrancados.jsp",
				},
			},
		};
	}

	get relatorioClientesComBonusMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesComBonus",
			permissaoAdm: "9.76 - Relatório de Clientes com Bônus",
			permitido: this.permissaoService.temPermissaoAdm("9.76"),
			favoriteIdentifier: "RELATORIO_BONUS",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_BONUS",
					jspPage: "listasRelatoriosAlunosBonus.jsp",
				},
			},
		};
	}

	get relatorioClientesComAtestadoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesComAtestado",
			permissaoAdm: "9.75 - Relatório de Clientes com Atestado",
			permitido: this.permissaoService.temPermissaoAdm("9.75"),
			favoriteIdentifier: "RELATORIO_ATESTADO",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_ATESTADO",
					jspPage: "listasRelatoriosAlunosAtestado.jsp",
				},
			},
		};
	}

	get relarioClientesAniversariantesMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesAniversariantes",
			permissaoAdm: "6.29 - Relatório de Aniversariantes",
			permitido: this.permissaoService.temPermissaoAdm("6.29"),
			favoriteIdentifier: "ANIVERSARIANTES",
			route: {
				queryParams: {
					funcionalidadeNome: "ANIVERSARIANTES",
					openAsPopup: true,
					windowTitle: "Aniversariantes",
					windowWidth: 820,
					windowHeight: 595,
				},
			},
		};
	}

	// get relatorioOrcamentoMenuItem(): PlatformMenuItem {
	//     return {
	//         id: 'relatorioOrcamento',
	//         favoriteIdentifier: 'ORCAMENTO',
	//         route: {
	//             queryParams: {
	//                 funcionalidadeNome: 'ORCAMENTO',
	//                 jspPage: 'realizarOrcamento.jsp'
	//             },
	//         }
	//     };
	// }

	get relatorioOrcamentoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioOrcamento",
			permissaoAdm: "5.18 - Orçamento Turmas",
			permitido: this.permissaoService.temPermissaoAdm("5.18"),
			favoriteIdentifier: "RELATORIO_CLIENTES_ORCAMENTOS",
			route: {
				queryParams: {
					funcionalidadeNome: "relatorioOrcamento",
					jspPage: "orcamentoCons.jsp",
					openAsPopup: true,
					windowTitle: "Relatório de Orçamentos",
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioFechamentoAcessoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioFechamentoAcesso",
			permissaoAdm: "9.79 - Relatório de Fechamento de Acesso",
			permitido: this.permissaoService.temPermissaoAdm("9.79"),
			favoriteIdentifier: "FECHAMENTO_ACESSOS",
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/fechamento-acessos",
			},
		};
	}

	get relatorioTotalizadorTicketsMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioTotalizadorTickets",
			permissaoAdm: "9.84 - Relatório de Totalizador de Tickets",
			permitido: this.permissaoService.temPermissaoAdm("9.84"),
			favoriteIdentifier: "TOTALIZADOR_TICKETS",
			route: {
				queryParams: {
					funcionalidadeNome: "TOTALIZADOR_TICKETS",
					openAsPopup: true,
					windowTitle: "Totalizador de Tickets",
					windowWidth: 850,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioListaChamadaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioListaChamada",
			permissaoAdm: "6.03 - Lista Chamada",
			permitido: this.permissaoService.temPermissaoAdm("6.03"),
			favoriteIdentifier: "LISTA_CHAMADA",
			route: {
				queryParams: {
					funcionalidadeNome: "LISTA_CHAMADA",
					openAsPopup: true,
					windowTitle: "Lista de Chamada",
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioFrequenciaOcupacaoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioFrequenciaOcupacao",
			permissaoAdm: "9.73 - Relatório Frequência e Ocupação de Turmas",
			permitido: this.permissaoService.temPermissaoAdm("9.73"),
			favoriteIdentifier: "FREQUENCIA_OCUPACAO_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "FREQUENCIA_OCUPACAO_TURMAS",
					openAsPopup: true,
					windowTitle: "Frequência e Ocupação de Turmas",
					windowWidth: 1000,
					windowHeight: 760,
				},
			},
		};
	}

	get relatorioFrequenciaTurmasMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioFrequenciaTurmas",
			permissaoAdm: "10.08 - Consultar relatório de frequência de turmas",
			permitido: this.permissaoService.temPermissaoAdm("10.08"),
			favoriteIdentifier: "RELATORIO_FREQUENCIA_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_FREQUENCIA_TURMAS",
					openAsPopup: true,
					windowTitle: "Relatório de Frequência de Turmas",
					windowWidth: 1000,
					windowHeight: 760,
				},
			},
		};
	}

	get conviteAulaExperimentalMenuItem(): PlatformMenuItem {
		return {
			id: "conviteAulaExperimentalOcupacao",
			permissaoAdm: "5.61 - Convite Aula Experimental",
			permitido: this.permissaoService.temRecursoAdm("5.61"),
			favoriteIdentifier: "CONVITE_AULA_EXPERIMENTAL",
			route: {
				queryParams: {
					funcionalidadeNome: "CONVITE_AULA_EXPERIMENTAL",
					openAsPopup: true,
					windowTitle: "Convites de Aula Experimental",
					windowWidth: 1000,
					windowHeight: 760,
				},
			},
		};
	}

	get relatorioDescontoOcupacaoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioDescontoOcupacao",
			permissaoAdm: "9.78 - Relatório de Desconto por Ocupação na Turma",
			permitido: this.permissaoService.temPermissaoAdm("9.78"),
			favoriteIdentifier: "DESCONTO_OCUPACAO_TURMAS",
			route: {
				internalLink: "/adm/desconto-ocupacao",
			},
		};
	}

	get relatorioMapaTurmasMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioMapaTurma",
			permissaoAdm: "9.40 - Relatório de Mapa de Turma",
			permitido: this.permissaoService.temPermissaoAdm("9.40"),
			favoriteIdentifier: "MAPA_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "MAPA_TURMAS",
					openAsPopup: true,
					windowTitle: "Mapa de Turmas",
					windowWidth: 905,
					windowHeight: 660,
				},
			},
		};
	}

	get relatorioConsultaTurmaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioConsultaTurma",
			permissaoAdm: "9.39 - Relatório de Consulta de Turma",
			permitido: this.permissaoService.temPermissaoAdm("9.39"),
			favoriteIdentifier: "CONSULTA_DE_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "CONSULTA_DE_TURMAS",
					openAsPopup: true,
					windowTitle: "Consulta de Turma",
					windowWidth: 905,
					windowHeight: 660,
				},
			},
		};
	}

	get relatorioFechamentoCaixaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioFechamentoCaixa",
			permissaoAdm: "6.01 - Fechamento de Caixa Por Operador",
			permitido: this.permissaoService.temPermissaoAdm("6.01"),
			favoriteIdentifier: "FECHAMENTO_CAIXA_OPERADOR",
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/fechamento-caixa-operador",
			},
		};
	}

	get relatorioCompetenciaMensalMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioCompetenciaMensal",
			permissaoAdm: "6.05 - Competência Mensal",
			permitido: this.permissaoService.temPermissaoAdm("6.05"),
			favoriteIdentifier: "COMPETENCIA_MENSAL",
			route: {
				queryParams: {
					funcionalidadeNome: "COMPETENCIA_MENSAL",
					openAsPopup: true,
					windowTitle: "Competência Mensal",
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioFaturamentoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioFaturamento",
			permissaoAdm: "6.06 - Faturamento Sintético",
			permitido: this.permissaoService.temPermissaoAdm("6.06"),
			favoriteIdentifier: "FATURAMENTO_PERIODO",
			route: {
				queryParams: {
					funcionalidadeNome: "FATURAMENTO_PERIODO",
					openAsPopup: true,
					windowTitle: "Faturamento por Período",
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioFaturamentoRecebidoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioFaturamentoRecebido",
			permissaoAdm: "9.92 - Relatório de Faturamento Recebido Por Período",
			permitido: this.permissaoService.temPermissaoAdm("9.92"),
			favoriteIdentifier: "FATURAMENTO_RECEBIDO_PERIODO",
			route: {
				queryParams: {
					funcionalidadeNome: "FATURAMENTO_RECEBIDO_PERIODO",
					openAsPopup: true,
					windowTitle: "Faturamento Recebido por Período",
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get relatorioReceitaPorPeriodoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioReceitaPorPeriodo",
			permissaoAdm: "6.04 - Receita Por Período Sintético",
			permitido: this.permissaoService.temPermissaoAdm("6.04"),
			favoriteIdentifier: "RECEITA_PERIODO",
			route: {
				queryParams: {
					funcionalidadeNome: "RECEITA_PERIODO",
					openAsPopup: true,
					windowTitle: "Receita por Período",
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get pedidosPinPadMenuItem(): PlatformMenuItem {
		return {
			id: "pedidosPinPad",
			permissaoAdm: "9.83 - Relatório de Pedidos Pinpad",
			permitido: this.permissaoService.temPermissaoAdm("9.83"),
			favoriteIdentifier: "PEDIDOS_PINPAD",
			route: {
				internalLink: "/adm/pedidos-pinpad",
			},
		};
	}

	get relatorioParcelasMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioParcelas",
			permissaoAdm: "6.07 - Relatório de Parcelas",
			permitido: this.permissaoService.temPermissaoAdm("6.07"),
			favoriteIdentifier: "RELATORIO_PARCELAS",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_PARCELAS",
					popupFullscreen: true,
					windowTitle: "Relatório de Parcelas",
				},
			},
		};
	}

	get relatorioSaldoContaCorrenteMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioSaldoContaCorrente",
			permissaoAdm: "6.08 - Relatório de Saldo de Conta Corrente",
			permitido: this.permissaoService.temPermissaoAdm("6.08"),
			favoriteIdentifier: "SALDO_CONTA_CORRENTE",
			route: {
				queryParams: {
					funcionalidadeNome: "SALDO_CONTA_CORRENTE",
					openAsPopup: true,
					windowTitle: "Saldo Conta Corrente",
					windowWidth: 780,
					windowHeight: 585,
				},
			},
		};
	}

	get relatorioTransacoesPixMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioTransacoesPix",
			permissaoAdm: "9.85 - Relatório de Transações Pix",
			permitido: this.permissaoService.temPermissaoAdm("9.85"),
			favoriteIdentifier: "TRANSACOES_PIX",
			route: {
				queryParams: {
					funcionalidadeNome: "TRANSACOES_PIX",
					popupFullscreen: true,
					windowTitle: "Transações Pix",
				},
			},
		};
	}

	get relatorioConsultaRecibosMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioConsultaRecibos",
			permissaoAdm: "9.89 - Relatório de Consulta de Recibos",
			permitido: this.permissaoService.temPermissaoAdm("9.89"),
			favoriteIdentifier: "CONSULTA_DE_RECIBOS",
			route: {
				queryParams: {
					funcionalidadeNome: "CONSULTA_DE_RECIBOS",
					openAsPopup: true,
					windowTitle: "Consulta de Recibos",
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get relatorioComissaoProfessorMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioComissaoProfessor",
			permissaoAdm: "4.21 - Comissão para Professor",
			permitido: this.permissaoService.temPermissaoAdm("4.21"),
			favoriteIdentifier: "GESTAO_DE_COMISSAO",
			route: {
				queryParams: {
					funcionalidadeNome: "GESTAO_DE_COMISSAO",
					jspPage: "gestaoComissao.jsp",
				},
			},
		};
	}

	get relatorioComissaoConsultorMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioComissaoConsultor",
			permissaoAdm: "4.27 - Comissão para Consultor",
			permitido: this.permissaoService.temPermissaoAdm("4.27"),
			favoriteIdentifier: "COMISSAO_VARIAVEL",
			route: {
				internalLink: "/adm/comissao-consultor",
			},
		};
	}

	get relatorioPrevisaoRenovacaoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioPrevisaoRenovacao",
			permissaoAdm: "9.91 - Relatório de Previsão de Renovação",
			permitido: this.permissaoService.temPermissaoAdm("9.91"),
			favoriteIdentifier: "PREVISAO_RENOVACAO_CONTRATO",
			route: {
				queryParams: {
					funcionalidadeNome: "PREVISAO_RENOVACAO_CONTRATO",
					openAsPopup: true,
					windowTitle: "Previsão de Renovação por Contrato",
					windowWidth: 980,
					windowHeight: 700,
				},
			},
		};
	}

	get relatorioBvsMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioBvs",
			permissaoAdm: "9.74 - Relatório de BVs",
			permitido: this.permissaoService.temPermissaoAdm("9.74"),
			favoriteIdentifier: "RELATORIO_BVS",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_BVS",
					openAsPopup: true,
					windowTitle: "Relatório de BVs",
					windowWidth: 1135,
					windowHeight: 683,
				},
			},
		};
	}

	get relatorioRepasseMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioRepasse",
			permissaoAdm: "9.81 - Relatório de Repasse",
			permitido: this.permissaoService.temPermissaoAdm("9.81"),
			favoriteIdentifier: "RELATORIO_REPASSE",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_REPASSE",
					openAsPopup: true,
					windowTitle: "Relatório de Repasse",
					windowWidth: 1135,
					windowHeight: 683,
				},
			},
		};
	}

	get relatorioPesquisaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioPesquisa",
			permissaoAdm: "9.57 - Relatório de Pesquisas",
			permitido: this.permissaoService.temPermissaoAdm("9.57"),
			favoriteIdentifier: "RELATORIO_PESQUISA",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_PESQUISA",
					openAsPopup: true,
					windowTitle: "Relatório de Pesquisas",
					windowWidth: 1135,
					windowHeight: 683,
				},
			},
		};
	}

	get relatorioMovimentoProdutoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioMovimentoProduto",
			permissaoAdm: "3.05 - Movimentação do Produto",
			permitido: this.permissaoService.temPermissaoAdm("3.05"),
			favoriteIdentifier: "MOVIMENTO_PRODUTO",
			route: {
				queryParams: {
					funcionalidadeNome: "MOVIMENTO_PRODUTO",
					openAsPopup: true,
					windowTitle: "Movimento do Produto",
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get relatorioCardexMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioCardex",
			permissaoAdm: "12.05 - Visualizar Cardex",
			permitido: this.permissaoService.temPermissaoAdm("12.05"),
			favoriteIdentifier: "CARDEX",
			route: {
				queryParams: {
					funcionalidadeNome: "CARDEX",
					openAsPopup: true,
					windowTitle: "Cardex",
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get relatorioControleLogsMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioControleLogs",
			permissaoAdm: "1.01 - Controle de Log",
			permitido: this.permissaoService.recursoAdm("1.01"),
			favoriteIdentifier: "CONTROLE_LOG",
			route: {
				queryParams: {
					funcionalidadeNome: "CONTROLE_LOG",
					openAsPopup: true,
					windowTitle: "Controle de Log",
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get relatorioComissaoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioComissao",
			permissaoModulo: "Modulo studio habilitado",
			permitido: this.permissaoService.temModuloHabilitado("EST"),
			favoriteIdentifier: "COMISSAO_EST",
			route: {
				queryParams: {
					funcionalidadeNome: "COMISSAO_EST",
					jspPage: "relatorioComissaoEstudio.jsp",
				},
			},
		};
	}

	get relatorioDiarioMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioDiario",
			permissaoModulo: "Modulo studio habilitado",
			permitido: this.permissaoService.temModuloHabilitado("EST"),
			favoriteIdentifier: "DIARIO",
			route: {
				queryParams: {
					funcionalidadeNome: "DIARIO",
					jspPage: "relatorioFechamentoDiario.jsp",
				},
			},
		};
	}

	get relatorioAgendamentosMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioAgendamentos",
			permissaoModulo: "Modulo studio habilitado",
			permitido: this.permissaoService.temModuloHabilitado("EST"),
			favoriteIdentifier: "AGENDAMENTOS",
			route: {
				queryParams: {
					funcionalidadeNome: "AGENDAMENTOS",
					jspPage: "relatorioGeralAgendamentos.jsp",
				},
			},
		};
	}

	get relatorioClientesSemSessaoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesSemSessao",
			permissaoModulo: "Modulo studio habilitado",
			permitido: this.permissaoService.temModuloHabilitado("EST"),
			favoriteIdentifier: "CLIENTES_SEM_SESSAO",
			route: {
				queryParams: {
					funcionalidadeNome: "CLIENTES_SEM_SESSAO",
					jspPage: "relatorioGeralClientesInativos.jsp",
				},
			},
		};
	}

	get relatorioSgpModalidadesComTurmaItem() {
		return {
			id: "relatorioSgpModalidadesComTurma",
			permissaoConfiguracaoSistema: "Configuração de sistema SESC habilitado",
			permitido: this.permissaoService.temConfiguracaoSistemaAdm("sesc"),
			favoriteIdentifier: "SGP_MODALIDADES_COM_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "SGP_MODALIDADES_COM_TURMAS",
					openAsPopup: true,
					windowTitle: "SGP Modalidades com Turmas",
					windowHeight: 600,
					windowWidth: 900,
				},
			},
		};
	}

	get relatorioSgpTurmaItem() {
		return {
			id: "relatorioSgpTurma",
			permissaoConfiguracaoSistema: "Configuração de sistema SESC habilitado",
			permitido: this.permissaoService.temConfiguracaoSistemaAdm("sesc"),
			favoriteIdentifier: "SGP_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "SGP_TURMAS",
					openAsPopup: true,
					windowTitle: "SGP Mapa Estatístico",
					windowHeight: 600,
					windowWidth: 900,
				},
			},
		};
	}

	get relatorioSgpModalidadesSemTurmaItem() {
		return {
			id: "relatorioSgpModalidadesSemTurma",
			permissaoConfiguracaoSistema: "Configuração de sistema SESC habilitado",
			permitido: this.permissaoService.temConfiguracaoSistemaAdm("sesc"),
			favoriteIdentifier: "SGP_MODALIDADES_SEM_TURMAS",
			route: {
				queryParams: {
					funcionalidadeNome: "SGP_MODALIDADES_SEM_TURMAS",
					openAsPopup: true,
					windowTitle: "SGP Modalidades sem Turmas",
					windowHeight: 600,
					windowWidth: 900,
				},
			},
		};
	}

	get relatorioSgpAvaliacoesFisicasItem() {
		return {
			id: "relatorioSgpAvaliacoesFisicas",
			permissaoConfiguracaoSistema: "Configuração de sistema SESC habilitado",
			permitido: this.permissaoService.temConfiguracaoSistemaAdm("sesc"),
			favoriteIdentifier: "SGP_AVALIACOES_FISICAS",
			route: {
				queryParams: {
					funcionalidadeNome: "SGP_AVALIACOES_FISICAS",
					openAsPopup: true,
					windowTitle: "SGP Avaliações físicas",
					windowHeight: 600,
					windowWidth: 900,
				},
			},
		};
	}

	get relatorioClientesComRestricoesMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClientesComRestricoes",
			permissaoAdm:
				"Configuração Empresa - Utiliza Gestão de Clientes com Restrições e Empresa Franqueadora",
			permitido:
				this.permissaoService.temConfiguracaoEmpresaAdm(
					"utilizaGestaoClientesComRestricoes"
				) &&
				(this.permissaoService.empresaLogadoIsFranqueadora ||
					this.permissaoService.empresaLogadaNaoPossuiFranqueadora),
			favoriteIdentifier: "CLIENTES_COM_RESTRICOES",
			route: {
				internalLink: "/adm/relatorios/clientes-com-restricoes",
			},
		};
	}

	get relatorioSMDMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioSMD",
			permissaoAdm: "10.11 - Relatório de SMD por Período",
			permitido: this.permissaoService.temPermissaoAdm("10.11"),
			favoriteIdentifier: "SMD_RELATORIO",
			route: {
				internalLink: "/adm/relatorios/relatorio-smd",
			},
		};
	}

	get relatorioClienteComCobrancaAutomaticaBloqueadaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioClienteComCobrancaAutomaticaBloqueada",
			permissaoAdm:
				"9.99 - Relatório de Clientes com Cobrança Automática Bloqueada",
			permitido: this.permissaoService.temPermissaoAdm("9.99"),
			favoriteIdentifier: "CLIENTES_COBRANCA_AUTOMATICA_BLOQUEADA",
			route: {
				internalLink: "/adm/relatorios/clientes-cobranca-automatica-bloqueada",
			},
		};
	}
}
