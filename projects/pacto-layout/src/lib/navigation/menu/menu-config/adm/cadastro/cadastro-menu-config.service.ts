import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PlataformModuleConfig, PlatformMenuItem } from "../../../../models";
import { PermissaoService } from "../../../../permissao/permissao.service";
import { CommonItemsMenuConfigService } from "../../common/common-items-menu-config.service";
import { MenuConfigService } from "../../menu-config.service";
import { FeatureManagerService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class CadastroMenuConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private commonItemsMenuConfigService: CommonItemsMenuConfigService,
		private permissaoService: PermissaoService,
		private featureManagerService: FeatureManagerService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([this.cadastroMenuParentItem]);
	}

	get cadastroMenuParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastros",
			configMenuSidebar: {
				submenus: [
					this.cadastroAdquirenteMenuItem,
					this.cadastroAmbienteMenuItem,
					this.cadastroAtivarClubeVantagensMenuItem,
					this.cadastroBancoMenuItem,
					this.cadastroBrindesMenuItem,
					this.cadastroCampanhaCupomDescontoItem,
					this.cadastroCampanhasClubeVantagensMenuItem,
					this.cadastroCategoriaClienteMenuItem,
					this.grupoComDescontoMenuItem,
					this.cadastroCategoriaProdutoMenuItem,
					this.cadastroCidadeMenuItem,
					this.cadastroClassificacaoMenuItem,
					this.cadastroColaboradorMenuItem,
					this.cadastroCondicaoPagamentoMenuItem,
					this.cadastroConfiguracaoClubeVantagensMenuItem,
					this.cadastroContaCorrenteMenuItem,
					this.cadastroConvenioCobrancaMenuItem,
					this.cadastroConvenioDescontoMenuItem,
					this.cadastroDepartamentoMenuItem,
					this.cadastroDescontoMenuItem,
					this.cadastroEmpresaMenuItem,
					this.cadastroFormaPagamentoMenuItem,
					this.cadastroGrauInstrucaoMenuItem,
					this.cadastroCampanhaCupomDescontoItem,
					this.commonItemsMenuConfigService.admRelatorioHistoricoPontoMenuItem,
					this.cadastroHorarioMenuItem,
					this.cadastroImportacaoMenuItem,
					this.cadastroImpostoProdutoMenuItem,
					this.cadastroIndiceFinanceiroMenuItem,
					this.cadastroIntegracaoAcessoMenuItem,
					this.cadastroJustificativaOperacaoMenuItem,
					this.cadastroLancamentoProdutoColetivoMenuItem,
					this.cadastroLocalAcessoMenuItem,
					this.cadastroLocalImpressaoMenuItem,
					this.cadastroMetasFinanceiroMenuItem,
					this.cadastroModalidadeMenuItem,
					this.cadastroModeloContratoMenuItem,
					this.cadastroModeloOrcamentoMenuItem,
					this.cadastroMovimentoProdutoMenuItem,
					this.cadastroNivelTurmaMenuItem,
					this.cadastroOperadoraCartaoMenuItem,
					this.cadastroPacoteMenuItem,
					this.cadastroPaisMenuItem,
					this.cadastroParentescoMenuItem,
					this.cadastroPerguntaMenuItem,
					this.cadastroNpsMenuItem,
					this.cadastroPinPadMenuItem,
					this.cadastroPlanoMenuItem,
					this.cadastroProdutoMenuItem,
					this.cadastroProfissaoMenuItem,
					this.cadastroQuestionarioMenuItem,
					this.cadastroServidorFacialMenuItem,
					this.cadastroTamanhoArmarioMenuItem,
					this.cadastroTaxaComissaoMenuItem,
					this.cadastroTipoModalidadeMenuItem,
					this.cadastroTipoPlanoMenuItem,
					this.cadastroTipoRemessaMenuItem,
					this.cadastroTipoRetornoMenuItem,
					this.cadastroUsuarioMenuItem,
					this.cadastroPerfilAcessoMenuItem,
					this.cadastroPerfilAcessoUnificadoMenuItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.cadastroAcessoParentItem,
					this.cadastroClubeVantagensParentItem,
					this.cadastroContratoParentItem,
					this.cadastroFinanceiroParentItem,
					this.cadastroPlanoParentItem,
					this.cadastroProdutoParentItem,
					this.cadastroOutrosParentItem,
					this.cadastroPerfilAcessoMenuItem,
					this.cadastroPerfilAcessoUnificadoMenuItem,
				],
			},
		};
		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cadastroAcessoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastro-acesso",
			configMenuExplorar: {
				submenus: [
					this.cadastroIntegracaoAcessoMenuItem,
					this.cadastroLocalAcessoMenuItem,
					this.cadastroLocalImpressaoMenuItem,
					// this.cadastroPerfilAcessoMenuItem,
					this.cadastroServidorFacialMenuItem,
					this.cadastroPerfilAcessoUnificadoMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cadastroClubeVantagensParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastro-clube-vantagens",
			configMenuExplorar: {
				submenus: [
					this.cadastroAtivarClubeVantagensMenuItem,
					this.cadastroBrindesMenuItem,
					this.cadastroCampanhasClubeVantagensMenuItem,
					this.cadastroConfiguracaoClubeVantagensMenuItem,
					this.commonItemsMenuConfigService.admRelatorioHistoricoPontoMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cadastroContratoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastro-contrato",
			configMenuExplorar: {
				submenus: [
					this.cadastroJustificativaOperacaoMenuItem,
					this.cadastroModeloContratoMenuItem,
					this.cadastroModeloOrcamentoMenuItem,
					this.cadastroMovimentoProdutoMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cadastroFinanceiroParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastro-financeiro",
			configMenuExplorar: {
				submenus: [
					this.cadastroAdquirenteMenuItem,
					this.cadastroBancoMenuItem,
					this.cadastroCondicaoPagamentoMenuItem,
					this.cadastroContaCorrenteMenuItem,
					this.cadastroConvenioCobrancaMenuItem,
					this.cadastroPinPadMenuItem,
					this.cadastroFormaPagamentoMenuItem,
					this.cadastroImpostoProdutoMenuItem,
					this.cadastroIndiceFinanceiroMenuItem,
					this.cadastroMetasFinanceiroMenuItem,
					this.cadastroOperadoraCartaoMenuItem,
					this.cadastroTaxaComissaoMenuItem,
					this.cadastroTipoRemessaMenuItem,
					this.cadastroTipoRetornoMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cadastroPlanoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastro-plano",
			configMenuExplorar: {
				submenus: [
					this.cadastroCampanhaCupomDescontoItem,
					this.cadastroConvenioDescontoMenuItem,
					this.cadastroDescontoMenuItem,
					this.cadastroHorarioMenuItem,
					this.cadastroModalidadeMenuItem,
					this.cadastroPacoteMenuItem,
					this.cadastroPlanoMenuItem,
					this.cadastroTipoModalidadeMenuItem,
					this.cadastroTipoPlanoMenuItem,
					this.cadastroNivelTurmaMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioPrevisaoRenovacaoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioPrevisaoRenovacao",
			permissaoAdm: "9.91 - Relatório de Previsão de Renovação",
			permitido: this.permissaoService.temPermissaoAdm("9.91"),
			favoriteIdentifier: "PREVISAO_RENOVACAO_CONTRATO",
			route: {
				queryParams: {
					funcionalidadeNome: "PREVISAO_RENOVACAO_CONTRATO",
					openAsPopup: true,
					windowTitle: "Previsão de Renovação por Contrato",
					windowWidth: 980,
					windowHeight: 700,
				},
			},
		};
	}

	get cadastroProdutoParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastro-produto",
			configMenuExplorar: {
				submenus: [
					this.cadastroCategoriaProdutoMenuItem,
					this.cadastroLancamentoProdutoColetivoMenuItem,
					this.cadastroProdutoMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cadastroOutrosParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "cadastro-outros",
			configMenuExplorar: {
				submenus: [
					this.cadastroCategoriaClienteMenuItem,
					this.grupoComDescontoMenuItem,
					this.cadastroCidadeMenuItem,
					this.cadastroClassificacaoMenuItem,
					this.cadastroColaboradorMenuItem,
					this.cadastroDepartamentoMenuItem,
					this.cadastroEmpresaMenuItem,
					this.cadastroGrauInstrucaoMenuItem,
					this.cadastroGrupoDescontoMenuItem,
					this.cadastroImportacaoMenuItem,
					this.cadastroPaisMenuItem,
					this.cadastroParentescoMenuItem,
					this.cadastroPerguntaMenuItem,
					this.cadastroNpsMenuItem,
					this.cadastroProfissaoMenuItem,
					this.cadastroQuestionarioMenuItem,
					this.cadastroTamanhoArmarioMenuItem,
					this.cadastroUsuarioMenuItem,
				],
			},
		};
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get cadastroTamanhoArmarioMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroTamanhoArmario",
			permissaoAdm: "2.55 - Tamanho de Armário",
			permitido: this.permissaoService.temPermissaoAdm("2.55"),
			favoriteIdentifier: "TAMANHO_ARMARIO",
			route: {
				internalLink: "/adm/produtos/tamanho-armario",
			},
		};
	}

	get cadastroAdquirenteMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroAdquirente",
			permitido: true,
			favoriteIdentifier: "ADQUIRENTE",
			route: {
				internalLink: "/adm/config-financeiras/adquirente",
			},
		};
	}
	get cadastroImpostoProdutoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroImpostoProduto",
			permitido: this.permissaoService.temPermissaoAdm("10.12"),
			favoriteIdentifier: "IMPOSTO",
			route: {
				internalLink: "/adm/config-financeiras/imposto-produto",
			},
		};
	}

	get cadastroBancoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroBanco",
			permissaoAdm: "4.02 - Bancos",
			permitido: this.permissaoService.temRecursoAdm("4.02"),
			favoriteIdentifier: "BANCO",
			route: {
				internalLink: "/adm/config-financeiras/banco",
			},
		};
	}

	get cadastroBrindesMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroBrindes",
			permissaoAdm: "5.66 - Cadastro de brinde",
			permitido: this.permissaoService.temRecursoAdm("5.66"),
			favoriteIdentifier: "BRINDE",
			route: {
				internalLink: "/adm/clube-vantagens/brinde",
			},
		};
	}

	get cadastroCategoriaClienteMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCategoriaCliente",
			permitido: true,
			favoriteIdentifier: "CATEGORIA_CLIENTES",
			route: {
				internalLink: "/adm/cad-aux/categoria",
			},
		};
	}

	get grupoComDescontoMenuItem(): PlatformMenuItem {
		return {
			id: "grupoComDesconto",
			permitido: true,
			favoriteIdentifier: "GRUPO_DESCONTO",
			route: {
				internalLink: "/adm/cad-aux/grupo",
			},
		};
	}

	get cadastroOperadoraCartaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroOperadoraCartao",
			permissaoAdm: "4.15 - Operadora de Cartão",
			permitido: this.permissaoService.temRecursoAdm("4.15"),
			favoriteIdentifier: "OPERADORA_CARTAO",
			route: {
				internalLink: "/adm/config-financeiras/operadora-cartao",
			},
		};
	}

	get cadastroCategoriaProdutoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCategoriaProduto",
			permissaoAdm: "5.02 - Categoria de Produto",
			permitido: this.permissaoService.temRecursoAdm("5.02"),
			favoriteIdentifier: "CATEGORIA_PRODUTO",
			route: {
				internalLink: "/adm/produtos/categoria-produto",
			},
		};
	}

	get cadastroCidadeMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCidade",
			permissaoAdm: "2.02 - Cidade",
			permitido: this.permissaoService.temRecursoAdm("2.02"),
			favoriteIdentifier: "CIDADE",
			route: {
				internalLink: "/adm/cad-aux/cidade",
			},
		};
	}

	get cadastroClassificacaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroClassificacao",
			permissaoAdm: "2.03 - Classificações",
			permitido: this.permissaoService.temRecursoAdm("2.03"),
			favoriteIdentifier: "CLASSIFICACAO",
			route: {
				internalLink: "/adm/cad-aux/classificacao",
			},
		};
	}

	get cadastroCondicaoPagamentoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCondicaoPagamento",
			permissaoAdm: "5.03 - Condições de pagamento",
			permitido: this.permissaoService.temRecursoAdm("5.03"),
			favoriteIdentifier: "CONDICAO_DE_PAGAMENTO",
			route: {
				internalLink: "/adm/planos/condicao-pagamento",
			},
		};
	}

	get cadastroContaCorrenteMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroContaCorrente",
			permissaoAdm: "4.03 - Conta Corrente",
			permitido: this.permissaoService.temRecursoAdm("4.03"),
			favoriteIdentifier: "CONTA_CORRENTE",
			route: {
				internalLink: "/adm/config-financeiras/conta-corrente",
			},
		};
	}

	get cadastroConvenioDescontoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroConvenioDesconto",
			permissaoAdm: "5.04 - Convênio de desconto",
			permitido: this.permissaoService.temRecursoAdm("5.04"),
			favoriteIdentifier: "CONVENIO_DE_DESCONTO",
			route: {
				internalLink: "/adm/planos/convenio-desconto",
			},
		};
	}

	get cadastroDepartamentoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroDepartamento",
			permitido: true,
			favoriteIdentifier: "DEPARTAMENTO",
			route: {
				internalLink: "/adm/cad-aux/departamentos",
			},
		};
	}

	get cadastroDescontoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroDesconto",
			permissaoAdm: "5.05 - Desconto",
			permitido: this.permissaoService.temRecursoAdm("5.05"),
			favoriteIdentifier: "DESCONTO",
			route: {
				internalLink: "/adm/planos/desconto",
			},
		};
	}

	get cadastroGrauInstrucaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroGrauInstrucao",
			permissaoAdm: "2.13 -  Grau de Instrução",
			permitido: this.permissaoService.temRecursoAdm("2.13"),
			favoriteIdentifier: "GRAU_DE_INSTRUCAO",
			route: {
				internalLink: "/adm/cad-aux/instrucao",
			},
		};
	}

	get cadastroGrupoDescontoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroGrupoDesconto",
			permissaoAdm: "2.14 - Grupo",
			permitido: this.permissaoService.temRecursoAdm("2.14"),
			favoriteIdentifier: "GRUPO_DESCONTO",
			route: {
				internalLink: "/adm/cad-aux/grupo",
			},
		};
	}

	get cadastroHorarioMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroHorario",
			permissaoAdm: "5.05 - Horário",
			permitido: this.permissaoService.temRecursoAdm("5.05"),
			favoriteIdentifier: "HORARIO",
			route: {
				internalLink: "/adm/planos/horarios",
			},
		};
	}

	get cadastroIndiceFinanceiroMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroIndiceFinanceiro",
			permissaoAdm: "5.63 - Índice Financeiro para Reajuste Preços",
			permitido: this.permissaoService.temRecursoAdm("5.63"),
			favoriteIdentifier: "INDICE_FINANCEIRO_REAJUSTE_PRECO",
			route: {
				internalLink: "/adm/cad-aux/indice-financeiro",
			},
		};
	}

	get cadastroIntegracaoAcessoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroIntegracaoAcesso",
			permissaoAdm: "1.05 - Local de Acesso",
			permitido: this.permissaoService.temRecursoAdm("1.05"),
			favoriteIdentifier: "INTEGRACAO_ACESSO",
			route: {
				internalLink: "/adm/acesso-sistema/integracao-acesso",
			},
		};
	}

	get cadastroJustificativaOperacaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroJustificativaOperacao",
			permissaoAdm: "3.04 - Justificativa de Operação",
			permitido: this.permissaoService.temRecursoAdm("3.04"),
			favoriteIdentifier: "JUSTIFICATIVA_OPERACAO",
			route: {
				internalLink: "/adm/config-contrato/justificativa-operacao",
			},
		};
	}

	get cadastroModeloContratoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroModeloContrato",
			permissaoAdm: "5.11 - Modelo de Contrato e Recibo",
			permitido: this.permissaoService.temRecursoAdm("5.11"),
			favoriteIdentifier: "MODELO_CONTRATO",
			route: {
				internalLink: "/adm/config-contrato/modelo-contrato",
			},
		};
	}

	get cadastroPerguntaMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroPergunta",
			permissaoAdm: "2.19 - Pergunta",
			permitido: this.permissaoService.temRecursoAdm("2.19"),
			favoriteIdentifier: "PERGUNTA",
			route: {
				internalLink: "/adm/cad-aux/pergunta",
			},
		};
	}

	get cadastroQuestionarioMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroQuestionario",
			permissaoAdm: "2.23 - Questionário",
			permitido: this.permissaoService.temRecursoAdm("2.23"),
			favoriteIdentifier: "QUESTIONARIO",
			route: {
				internalLink: "/adm/cad-aux/questionario",
			},
		};
	}

	get cadastroPacoteMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroPacote",
			permissaoAdm: "5.08 - Pacote",
			permitido: this.permissaoService.temRecursoAdm("5.08"),
			favoriteIdentifier: "PACOTE",
			route: {
				internalLink: "/adm/planos/pacotes",
			},
		};
	}

	get cadastroPaisMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroPais",
			permissaoAdm: "2.17 - País",
			permitido: this.permissaoService.temRecursoAdm("2.17"),
			favoriteIdentifier: "PAIS",
			route: {
				internalLink: "/adm/cad-aux/pais",
			},
		};
	}

	get cadastroParentescoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroParentesco",
			permissaoAdm: "2.18 - Parentesco",
			permitido: this.permissaoService.temRecursoAdm("2.18"),
			favoriteIdentifier: "PARENTESCO",
			route: {
				internalLink: "/adm/cad-aux/parentesco",
			},
		};
	}

	get cadastroNpsMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroNps",
			permissaoAdm: "9.57 - Pesquisa",
			permitido: this.permissaoService.temPermissaoAdm("9.57"),
			favoriteIdentifier: "PESQUISA",
			route: {
				internalLink: "/adm/cad-aux/pesquisa-satisfacao",
			},
		};
	}

	get cadastroPlanoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroPlano",
			permissaoAdm: "5.07 - Plano",
			permitido: this.permissaoService.temRecursoAdm("5.07"),
			favoriteIdentifier: "PLANO",
			route: {
				internalLink: "/adm/planos",
			},
		};
	}

	get cadastroProfissaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroProfissao",
			permissaoAdm: "2.22 - Profissão",
			permitido: this.permissaoService.temRecursoAdm("2.22"),
			favoriteIdentifier: "PROFISSAO",
			route: {
				internalLink: "/adm/cad-aux/profissao",
			},
		};
	}

	get cadastroServidorFacialMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroServidorFacial",
			permissaoAdm: "1.15 - Servidor Facial",
			permitido: this.permissaoService.temPermissaoAdm("1.15"),
			favoriteIdentifier: "SERVIDOR_FACIAL",
			route: {
				internalLink: "/adm/acesso-sistema/servidor-facial",
			},
		};
	}

	get cadastroMetasFinanceiroMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroMetasFinanceiro",
			permissaoAdm: "9.15 - Cadastro de Metas",
			permitido: this.permissaoService.temPermissaoAdm("9.15"),
			favoriteIdentifier: "METAS_FINANCEIRO_VENDA",
			route: {
				internalLink: "/adm/config-financeiras/meta-financeira",
			},
		};
	}

	get cadastroTipoPlanoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroTipoPlano",
			permissaoAdm: "5.16 - Tipos de plano",
			permitido: this.permissaoService.temRecursoAdm("5.16"),
			favoriteIdentifier: "TIPO_PLANO",
			route: {
				internalLink: "/adm/planos/tipo-plano",
			},
		};
	}

	get cadastroAmbienteMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroAmbiente",
			permissaoAdm: "5.01 - Ambiente",
			permitido: this.permissaoService.temRecursoAdm("5.01"),
			favoriteIdentifier: "AMBIENTE",
			route: {
				internalLink: "/adm/ambiente",
			},
		};
	}

	get cadastroCampanhasClubeVantagensMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCampanhas",
			permissaoAdm: "5.70 - Cadastro de campanha pontuação",
			permitido: this.permissaoService.temRecursoAdm("5.70"),
			favoriteIdentifier: "CLUBE_VANTAGENS_CAMPANHA",
			route: {
				internalLink: "/adm/campanha-clube-vantagens",
			},
		};
	}

	get cadastroClienteMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroCliente",
			permissaoAdm: "2.04 - Cliente",
			permitido: this.permissaoService.temPermissaoAdm("2.04"),
			favoriteIdentifier: "CLIENTE",
			route: {
				queryParams: {
					funcionalidadeNome: "CLIENTE",
					windowTitle: "Cliente",
					openAsPopup: true,
					windowWidth: 800,
					windowHeight: 595,
				},
			},
		};
	}

	get cadastroColaboradorMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroColaborador",
			permissaoAdm: "2.07 - Colaborador",
			permitido: this.permissaoService.temRecursoAdm("2.07"),
			favoriteIdentifier: "COLABORADOR",
			route: {
				queryParams: {
					funcionalidadeNome: "COLABORADOR",
					windowTitle: "Colaborador",
					openAsPopup: true,
					windowWidth: 1090,
					windowHeight: 650,
				},
			},
		};
	}

	get cadastroConfigNotaFiscalMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroConfigNotaFiscal",
			permissaoConfiguracaoEmpresaAdm:
				"Empresa configurada para usar NFCE ou NFSE",
			permitido:
				this.permissaoService.temConfiguracaoEmpresaAdm("usarnfce") ||
				this.permissaoService.temConfiguracaoEmpresaAdm("usarnfse"),
			favoriteIdentifier: "CONFIGURACAO_NOTAFISCAL",
			route: {
				queryParams: {
					funcionalidadeNome: "CONFIGURACAO_NOTAFISCAL",
					windowTitle: "Config. Nota Fiscal",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get cadastroConfiguracaoClubeVantagensMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroConfiguracaoClubeVantagens",
			permissaoConfiguracaoEmpresaAdm:
				"Empresa configurada para trabalhar com pontuação",
			permissaoAdm: "9.60 - Configurar Clube de Vantagens",
			permitido:
				this.permissaoService.temConfiguracaoEmpresaAdm(
					"trabalharComPontuacao"
				) && this.permissaoService.temPermissaoAdm("9.60"),
			favoriteIdentifier: "CLUBE_VANTAGENS_CONFIGURACOES",
			route: {
				queryParams: {
					funcionalidadeNome: "CLUBE_VANTAGENS_CONFIGURACOES",
					jspPage: "indicadores.jsp",
				},
			},
		};
	}

	get cadastroAtivarClubeVantagensMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroAtivarClubeVantagens",
			favoriteIdentifier: "CLUBE_VANTAGENS_ATIVAR",
			permissaoConfiguracaoEmpresaAdm:
				"Empresa configurada para trabalhar com pontuação",
			permitido: !this.permissaoService.temConfiguracaoEmpresaAdm(
				"trabalharComPontuacao"
			),
			route: {
				internalLink: "/adm/ativar-clube-vantagens",
			},
		};
	}

	get cadastroControleLogMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroControleLog",
			permissaoAdm: "1.01 - Controle de Log",
			permitido: this.permissaoService.temPermissaoAdm("1.01"),
			favoriteIdentifier: "CONTROLE_LOG",
			route: {
				queryParams: {
					funcionalidadeNome: "CONTROLE_LOG",
					windowTitle: "Controle de Log",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get cadastroConvenioCobrancaMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroConvenioCobranca",
			favoriteIdentifier: "CONVENIO_COBRANCA",
			module: PlataformModuleConfig.ADM_LEGADO,
			permissaoAdm: "4.04 - Convênio de Cobrança",
			permitido: this.permissaoService.temRecursoAdm("4.04"),
			route: {
				queryParams: {
					funcionalidadeNome: "CONVENIO_COBRANCA",
					windowTitle: "Convênio de Cobrança",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 750,
				},
			},
		};
	}

	get cadastroCampanhaCupomDescontoItem(): PlatformMenuItem {
		return {
			id: "cadastroCampanhaCupomDesconto",
			favoriteIdentifier: "CAMPANHA_CUPOM_DESCONTO",
			permissaoAdm: "5.62 - Campanha Cupom Desconto",
			permitido: this.permissaoService.temRecursoAdm("5.62"),
			route: {
				internalLink: "/adm/cad-aux/campanha-cupom-desconto",
			},
		};
	}

	get cadastroEmpresaMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroEmpresa",
			permissaoAdm: "2.10 - Empresa",
			permitido: this.permissaoService.temRecursoAdm("2.10"),
			favoriteIdentifier: "EMPRESA",
			route: {
				queryParams: {
					funcionalidadeNome: "EMPRESA",
					windowTitle: "Empresa",
					openAsPopup: true,
					windowWidth: 800,
					windowHeight: 595,
				},
			},
		};
	}

	get cadastroFormaPagamentoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroFormaPagamento",
			permissaoAdm: "4.06 - Formas de pagamento",
			permitido: this.permissaoService.temRecursoAdm("4.06"),
			favoriteIdentifier: "FORMA_PAGAMENTO",
			route: {
				internalLink: "/adm/forma-pagamento",
			},
		};
	}

	get cadastroPinPadMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroPinPad",
			permissaoAdm: "4.06 - Forma de Pagamento",
			permitido: this.permissaoService.temRecursoAdm("4.06"),
			favoriteIdentifier: "PINPAD",
			route: {
				internalLink: "/adm/config-financeiras/pinpad",
			},
		};
	}

	get cadastroImportacaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroImportacao",
			permissaoAdm:
				"1.16 - Importação de Clientes ou 1.17 - Importação de Produtos ou 1.18 - Importação de Colaborador ou 1.19 - Importação de Fornecedor ou 1.20 - Importação de Contas Financeiro",
			permitido:
				this.permissaoService.temPermissaoAdm("1.16") ||
				this.permissaoService.temPermissaoAdm("1.17") ||
				this.permissaoService.temPermissaoAdm("1.18") ||
				this.permissaoService.temPermissaoAdm("1.19") ||
				this.permissaoService.temPermissaoAdm("1.20"),
			favoriteIdentifier: "IMPORTACAO",
			route: {
				queryParams: {
					funcionalidadeNome: "IMPORTACAO",
					windowTitle: "Importação",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 800,
				},
			},
		};
	}

	get cadastroImprimeReciboEmBrancoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroImprimeReciboEmBranco",
			permitido: true,
			favoriteIdentifier: "IMPRIME_RECIBO_BANCO",
			route: {
				queryParams: {
					funcionalidadeNome: "IMPRIME_RECIBO_BANCO",
					windowTitle: "Imprime Recibo em Branco",
					openAsPopup: true,
					windowWidth: 780,
					windowHeight: 595,
				},
			},
		};
	}

	get cadastroLancamentoProdutoColetivoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroLancamentoProdutoColetivo",
			permissaoAdm: "2.75 - Permitir acesso ao recurso de Operações Coletivo",
			permitido: this.permissaoService.temPermissaoAdm("2.75"),
			favoriteIdentifier: "LANCAMENTO_PRODUTO_COLETIVO",
			route: {
				internalLink: "/adm/lancamento-produto-coletivo",
			},
		};
	}

	get cadastroLocalAcessoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroLocalAcesso",
			permissaoAdm: "1.05 - Local de Acesso",
			permitido: this.permissaoService.temRecursoAdm("1.05"),
			favoriteIdentifier: "LOCAL_ACESSO",
			route: {
				queryParams: {
					funcionalidadeNome: "LOCAL_ACESSO",
					windowTitle: "Local de acesso",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get cadastroLocalImpressaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroLocalImpressao",
			permissaoAdm: "Empresa com pacto print",
			permitido:
				this.permissaoService.temConfiguracaoEmpresaAdm("utilizarPactoPrint"),
			favoriteIdentifier: "LOCAL_IMPRESSAO",
			route: {
				queryParams: {
					funcionalidadeNome: "LOCAL_IMPRESSAO",
					windowTitle: "Local de impressão",
					openAsPopup: true,
					windowWidth: 1000,
					windowHeight: 650,
				},
			},
		};
	}

	get cadastroModalidadeMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroModalidade",
			permissaoAdm: "5.06 - Modalidade",
			permitido: this.permissaoService.temRecursoAdm("5.06"),
			favoriteIdentifier: "MODALIDADE",
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/modalidade",
			},
		};
	}

	get cadastroModeloOrcamentoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroModeloOrcamento",
			permissaoAdm: "5.18 - Orçamento Turmas",
			permitido: this.permissaoService.temRecursoAdm("5.18"),
			favoriteIdentifier: "MODELO_ORCAMENTO",
			route: {
				internalLink: "/adm/config-financeiras/modelo-orcamento",
			},
		};
	}

	get cadastroMovimentoProdutoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroMovimentoProduto",
			permissaoAdm: "3.05 - Movimentação do Produto",
			permitido: this.permissaoService.temRecursoAdm("3.05"),
			favoriteIdentifier: "MOVIMENTO_PRODUTO",
			route: {
				internalLink: "/adm/movimento-produto",
			},
		};
	}

	get cadastroPerfilAcessoMenuItem(): PlatformMenuItem {
		let temPerfilUnificado = false;
		if (this.sessionService && this.sessionService.funcionalidadesAtivas) {
			temPerfilUnificado = this.sessionService.funcionalidadesAtivas.includes(
				"PERFIL_ACESSO_UNIFICADO"
			);
		}

		return {
			id: "cadastroPerfilAcesso",
			permissaoAdm: "1.02 - Perfil de Acesso",
			permitido:
				this.permissaoService.temRecursoAdm("1.02") && !temPerfilUnificado,
			favoriteIdentifier: "PERFIL_ACESSO",
			route: {
				queryParams: {
					funcionalidadeNome: "PERFIL_ACESSO",
					windowTitle: "Perfil de Acesso",
					openAsPopup: true,
					windowWidth: 850,
					windowHeight: 595,
				},
			},
		};
	}

	get cadastroPerfilAcessoUnificadoMenuItem(): PlatformMenuItem {
		let temPerfilUnificado = false;
		if (this.sessionService && this.sessionService.funcionalidadesAtivas) {
			temPerfilUnificado = this.sessionService.funcionalidadesAtivas.includes(
				"PERFIL_ACESSO_UNIFICADO"
			);
		}

		return {
			id: "cadastroPerfilAcessoUnificado",
			permissaoAdm: "1.02 - Perfil de Acesso",
			favoriteIdentifier: "PERFIL_ACESSO_UNIFICADO",
			permitido:
				this.permissaoService.temRecursoAdm("1.02") && temPerfilUnificado,
			module: PlataformModuleConfig.ADM,
			route: {
				internalLink: "/adm/perfil-acesso-unificado",
			},
		};
	}

	get cadastroProdutoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroProduto",
			permissaoAdm: "5.10 - Produto",
			permitido: this.permissaoService.temRecursoAdm("5.10"),
			favoriteIdentifier: "PRODUTO",
			route: {
				internalLink: "/adm/produto",
			},
		};
	}

	get cadastroTaxaComissaoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroTaxaComissao",
			permissaoAdm: "4.26 - Taxas de Comissão para Consultor",
			permitido: this.permissaoService.temPermissaoAdm("4.26"),
			favoriteIdentifier: "TAXA_COMISSAO",
			route: {
				internalLink: "/adm/taxas-comissao",
			},
		};
	}

	get cadastroTipoModalidadeMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroTipoModalidade",
			permissaoAdm: "5.17 - Tipo de Modalidade",
			permitido: this.permissaoService.temRecursoAdm("5.17"),
			favoriteIdentifier: "TIPO_MODALIDADE",
			route: {
				internalLink: "/adm/tipo-modalidade",
			},
		};
	}

	get cadastroNivelTurmaMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroNivelTurma",
			permitido: true,
			favoriteIdentifier: "NIVEL_TURMA",
			route: {
				internalLink: "/adm/nivel-turma",
			},
		};
	}

	get cadastroTipoRemessaMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroTipoRemessa",
			permissaoAdm: "4.09 - Tipo de Remessa",
			permitido: this.permissaoService.temRecursoAdm("4.09"),
			favoriteIdentifier: "TIPO_REMESSA",
			route: {
				internalLink: "/adm/config-financeiras/tipo-remessa",
			},
		};
	}

	get cadastroTipoRetornoMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroTipoRetorno",
			permissaoAdm: "4.10 - Tipo de Retorno",
			permitido: this.permissaoService.temRecursoAdm("4.10"),
			favoriteIdentifier: "TIPO_RETORNO",
			route: {
				internalLink: "/adm/config-financeiras/tipo-retorno",
			},
		};
	}

	get cadastroUsuarioMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroUsuario",
			permissaoAdm: "1.03 - Usuário",
			permitido: this.permissaoService.temRecursoAdm("1.03"),
			favoriteIdentifier: "USUARIO",
			route: {
				queryParams: {
					funcionalidadeNome: "USUARIO",
					windowTitle: "Usuário",
					openAsPopup: true,
					windowWidth: 1070,
					windowHeight: 600,
				},
			},
		};
	}
}
