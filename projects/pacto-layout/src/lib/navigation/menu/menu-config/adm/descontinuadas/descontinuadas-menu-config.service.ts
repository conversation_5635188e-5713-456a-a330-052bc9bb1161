import { Injectable } from "@angular/core";
import { MenuConfigService } from "../../menu-config.service";
import { PlatformMenuItem } from "../../../../models";
import { Observable, of } from "rxjs";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PermissaoService } from "../../../../permissao/permissao.service";

@Injectable({
	providedIn: "root",
})
export class DescontinuadasMenuConfigService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([this.descontinuadoMenuItem]);
	}

	get descontinuadoMenuItem(): PlatformMenuItem {
		return {
			id: "funcionalidades_descontinuadas",
			favoriteIdentifier: "FUNCIONALIDADES_DESCONTINUADAS",
			permitido: true,
			route: {
				internalLink: "/adm/configuracao/v2/descontinuadas",
			},
			hideInSearch: true,
		};
	}
}
