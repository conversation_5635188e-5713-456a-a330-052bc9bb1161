import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { JwtHelperService } from "@auth0/angular-jwt";

import { MovideskChatTreinoService } from "@base-core/client/movidesk-chat-treino.service";
import { NotificarRecursoEmpresaService } from "@base-core/client/notificar-recurso-empresa.service";
import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";
import { ApiValidateTokenAdmResponse, PefilUsuarioAdm } from "adm-legado-api";

import { EmpresaAcesso } from "login-app-api";
import { ClubeDeBeneficiosMarketing } from "marketing-api";
import { from, Observable, of } from "rxjs";
import { catchError, map, switchMap, tap } from "rxjs/operators";
import {
	CryptoService,
	FeatureManagerService,
	MovideskChatService,
	PerfilAcessoUnificado,
} from "sdk";
import { SessionLogin } from "src/app/microservices/autenticacao/session-login.model";
import {
	ClientDiscoveryData,
	PlataformaModulo,
} from "src/app/microservices/client-discovery/client-discovery.model";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";
import {
	ConfigsTreinoRede,
	EmpresaFinanceiro,
	PefilAcessoDetalhe,
	UsuarioBase,
} from "treino-api";
import { environment } from "../../../../environments/environment";
import { CacheEmpresaDTO } from "./cache-empresa.model";
import { LoginUrlQueries, ValidTokenResponse } from "./client-model";
import { FuncionalidadeVO } from "./funcionalidade.model";
import { OamdService } from "./oamd.service";
import { LoginAppApiLoginService } from "login-app-api";
import { InfoMigracao } from "sdk";

@Injectable({
	providedIn: "root",
})
export class SessionService {
	modulosHabilitados: Array<PlataformaModulo> = [];
	linksAcessoApps: string;
	empresas: EmpresaFinanceiro[] = [];
	financeiroEmpresas: EmpresaFinanceiro[] = [];
	empresaId: string;
	chave: string;
	urllogin: string;
	colaborador: any;
	public codUsuarioZW: any;
	configsTreinoRede: ConfigsTreinoRede;
	public empresasAcesso: EmpresaAcesso[] = [];
	perfilUsuario: PefilAcessoDetalhe;
	perfilUsuarioTreino: PefilAcessoDetalhe;
	perfilUsuarioAdm: PefilUsuarioAdm;
	loggedUser: UsuarioBase = undefined;
	usuarioOamd: string;
	jwtService: JwtHelperService;
	goBackPage?: string;
	goBackModule?: string;
	funcionalidadesInativas: string[];
	clubeDeBeneficiosMarketing: ClubeDeBeneficiosMarketing;
	funcionalidadesAtivas: string[] = [];
	usaPrescricaoIA: boolean = false;
	_zwJSId?: string;
	infoMigracaoHabilitados: Array<InfoMigracao> = new Array<InfoMigracao>();

	getCacheFuncionalidadesInativas(
		chave: string,
		empresa: number
	): CacheEmpresaDTO {
		try {
			const cacheKey = this.cacheKey(chave, empresa);
			const storage = localStorage.getItem(cacheKey);
			const cache: CacheEmpresaDTO =
				storage !== null && storage !== undefined ? JSON.parse(storage) : null;

			if (cache && cache.data) {
				const dataCache = new Date(cache.data);
				let diferencaEmMilissegundos = Math.abs(
					new Date().getTime() - dataCache.getTime()
				);
				let diferencaEmMinutos = Math.floor(
					diferencaEmMilissegundos / (1000 * 60)
				);
				if (diferencaEmMinutos < environment.validadeCacheNichoEmMinutos) {
					return cache;
				} else {
					this.clearCacheFuncionalidadesInativas(chave, empresa);
					return null;
				}
			}
		} catch (e) {
			console.error(e);
			return null;
		}
	}

	clearCacheFuncionalidadesInativas(chave: string, empresa: number) {
		const cacheKey = this.cacheKey(chave, empresa);
		localStorage.removeItem(cacheKey);
	}

	setCacheFuncionalidadesInativas(
		chave: string,
		empresa: number,
		cacheEmpresa: CacheEmpresaDTO
	) {
		const cacheKey = this.cacheKey(chave, empresa);
		const storage = localStorage.setItem(
			cacheKey,
			JSON.stringify(cacheEmpresa)
		);
	}

	cacheKey(chave: string, empresa: number) {
		return `chave:${chave}:empresa:${empresa}`;
	}

	get isMormaii(): boolean {
		if (this.chave === "a46fc753befe2c9cfdd7b3b0908bdfed") {
			return true;
		} else {
			return false;
		}
	}

	get isUsuarioPacto(): boolean {
		return (
			this.loggedUser && this.loggedUser.username.toLowerCase() === "pactobr"
		);
	}

	get isUsuarioPactoSolucoes(): boolean {
		return (
			this.loggedUser &&
			["pactobr", "admin", "recor"].includes(
				this.loggedUser.username.toLowerCase()
			)
		);
	}

	get multiUnidade(): boolean {
		return (
			(this.empresas && this.empresas.length > 1) ||
			(this.empresasAcesso && this.empresasAcesso.length > 1)
		);
	}

	get currentEmpresa(): EmpresaFinanceiro {
		return this.empresas.find(
			(item) =>
				parseInt(`${item.codigo}`, 10) === parseInt(`${this.empresaId}`, 10)
		);
	}

	get apiToken(): any {
		return localStorage.getItem("apiToken");
	}

	get decodedApiToken(): any {
		const apiToken = localStorage.getItem("apiToken");
		return this.jwtService.decodeToken(apiToken);
	}

	get decodedApiTokenContent() {
		return JSON.parse(this.decodedApiToken.content);
	}

	get tokenChave() {
		if (this.decodedApiToken) {
			return this.decodedApiToken.chave;
		} else {
			return null;
		}
	}

	get tokenColaboradorId() {
		if (this.decodedApiToken && this.decodedApiToken.colaboradorid) {
			return this.decodedApiToken.colaboradorid;
		} else if (this.decodedApiToken && this.decodedApiToken.content) {
			return JSON.parse(this.decodedApiToken.content).ci;
		} else {
			return null;
		}
	}

	get tokenProvider() {
		if (this.decodedApiToken) {
			return this.decodedApiToken.provider;
		} else {
			return null;
		}
	}

	get apiTokenExpiration() {
		return this.decodedApiToken.exp;
	}

	get integracaoZW() {
		if (this.modulosHabilitados) {
			return this.modulosHabilitados.includes(PlataformaModulo.ZW);
		} else {
			return false;
		}
	}

	get getKey() {
		return this.chave ? this.chave : null;
	}

	get codigoUsuarioZw() {
		if (!this.isTreinoIndependente()) {
			if (this.loggedUser.usuarioZw > 0) {
				return this.loggedUser.usuarioZw;
			} else if (this.codUsuarioZW > 0) {
				return this.codUsuarioZW;
			}
		}
		return null;
	}

	get pathUrlZw() {
		return this.clientDiscoveryService.getUrlMap().zwUrl
			? this.clientDiscoveryService.getUrlMap().zwUrl
			: null;
	}

	get oamdUrl() {
		return this.clientDiscoveryService.getUrlMap().oamdUrl
			? this.clientDiscoveryService.getUrlMap().oamdUrl
			: null;
	}

	get treinoUrl() {
		return this.clientDiscoveryService.getUrlMap().treinoUrl
			? this.clientDiscoveryService.getUrlMap().treinoUrl
			: null;
	}

	get codigoEmpresa() {
		return this.empresaId ? this.empresaId : null;
	}

	get recursos() {
		if (this.perfilUsuario) {
			return this.perfilUsuario.recursos;
		} else {
			return null;
		}
	}

	get funcionalidades() {
		return this.perfilUsuario.funcionalidades;
	}

	constructor(
		private http: HttpClient,
		private cryptoService: CryptoService,
		private clientDiscoveryService: ClientDiscoveryService,
		private movideskChatService: MovideskChatService,
		private movideskChatTreinoService: MovideskChatTreinoService,
		@Inject(LOCALE_ID) private locale,
		private notificarRecursoEmpresaService: NotificarRecursoEmpresaService,
		private featureManagerService: FeatureManagerService,
		private oamdService: OamdService,
		private loginAppApiLoginService: LoginAppApiLoginService
	) {
		this.jwtService = new JwtHelperService();
	}

	/**
	 * Verifica se o modulo está habilidatado
	 */
	isModuloHabilitado(modulo: PlataformaModulo): boolean {
		if (modulo === PlataformaModulo.CCL) {
			return true;
		}
		return this.modulosHabilitados.includes(modulo);
	}

	isUserLogged(): boolean {
		return this.loggedUser === undefined ? false : true;
	}

	isTreinoIndependente() {
		return !this.integracaoZW;
	}

	get zwJSId() {
		return this.decodedApiTokenContent.zwJSId || this._zwJSId;
	}

	set zwJSId(zwJSId: string) {
		this._zwJSId = zwJSId;
	}

	logOut(): void {
		localStorage.removeItem("global-user");
		if (this.isTreinoIndependente()) {
			this.loggedUser = undefined;
			const urlMap = this.clientDiscoveryService.getUrlMap();
			if (
				environment.newLoginEnabled &&
				urlMap.loginFrontUrl !== undefined &&
				urlMap.loginFrontUrl !== ""
			) {
				location.replace(`${urlMap.loginFrontUrl}/${this.locale}/logout`);
			} else {
				const login =
					this.urllogin && this.urllogin !== ""
						? this.urllogin
						: urlMap.loginAppUrl;
				if (this.tokenChave) {
					location.replace(`${login}/${this.tokenChave}`);
				} else {
					location.replace(`${login}/${this.chave}`);
				}
			}

			localStorage.clear();
			sessionStorage.clear();
		} else {
			const zwJSId = this.zwJSId;
			this.clientDiscoveryService
				.linkZw(this.usuarioOamd, this.empresaId)
				.pipe(
					switchMap((response) => {
						response += `&logout=true&zwJSId=${zwJSId}`;
						const headers = new HttpHeaders().set(
							"Content-Type",
							"text/plain;charset=utf-8"
						);
						return this.http.get(response, { headers, responseType: "text" });
					}),
					catchError((_) => of(null))
				)
				.subscribe((response) => {
					this.loggedUser = undefined;
					const urlMap = this.clientDiscoveryService.getUrlMap();
					let url = "";
					if (
						environment.newLoginEnabled &&
						urlMap.loginFrontUrl !== undefined &&
						urlMap.loginFrontUrl !== ""
					) {
						url = `${urlMap.loginFrontUrl}/${this.locale}/logout`;
					} else {
						const login =
							this.urllogin && this.urllogin !== ""
								? this.urllogin
								: urlMap.loginAppUrl;
						if (this.tokenChave) {
							url = `${login}/${this.tokenChave}`;
						} else {
							url = `${login}/${this.chave}`;
						}
					}
					localStorage.clear();
					sessionStorage.clear();
					location.replace(url);
				});
		}
	}

	private fetchTokenFromLogin_novo(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		console.log("2_fetchTokenFromLogin executando...");
		return this.loginAppApiLoginService.cfgLoginUrl(loginUrl).pipe(
			tap((response) => {}),
			switchMap(() => {
				return this.loginAppApiLoginService.isRequestSec() ||
					this.modulosHabilitados.includes(PlataformaModulo.SEC)
					? this.fetchTokenFromLogin_new(loginUrl, sessionId)
					: this.fetchTokenFromLogin_old(loginUrl, sessionId);
			})
		);
	}

	private fetchTokenFromLogin_old(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		const session$ = fetch(`${loginUrl}/prest/session/${sessionId}`).then((r) =>
			r.json()
		);
		return from(session$).pipe(
			tap((session) => {
				console.log("fetchTokenFromLogin_old executado com sucesso:", session);
			}),
			catchError((error) => {
				const errorMessage = `Erro no método fetchTokenFromLogin_old: ${
					error.message || error.statusText || "Erro desconhecido"
				}`;
				console.error(
					"ERRO CAPTURADO NO fetchTokenFromLogin_old:",
					errorMessage,
					error
				);
				throw new Error(errorMessage);
			})
		);
	}

	private fetchTokenFromLogin_new(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		const body = {
			sessionId,
		};
		const bodyCrip = this.cryptoService.encrypt(JSON.stringify(body));
		const url = `${loginUrl}/prest/session-auth`;
		return this.http
			.post<ApiResponseSingle<SessionLogin>>(url, { data: bodyCrip })
			.pipe(
				map((response: any) => {
					const resp = this.cryptoService.decrypt(response.content);
					return JSON.parse(resp);
				}),
				catchError((error) => {
					const errorMessage = `Erro no método fetchTokenFromLogin_new: ${
						error.message || error.statusText || "Erro desconhecido"
					}`;
					console.error(
						"ERRO CAPTURADO NO fetchTokenFromLogin_new:",
						errorMessage,
						error
					);
					throw new Error(errorMessage);
				})
			);
	}

	private fetchTokenFromLogin(
		loginUrl: string,
		sessionId: string
	): Observable<SessionLogin> {
		const session$ = fetch(`${loginUrl}/prest/session/${sessionId}`).then((r) =>
			r.json()
		);
		return from(session$).pipe(
			tap((session) => session),
			catchError(() => of(null))
		);
	}

	private decodeToken(token: string): any {
		if (token) {
			return this.jwtService.decodeToken(token);
		} else {
			return null;
		}
	}

	/**
	 * Starts up SPA
	 *
	 * Caso o token não for definido é necessário definir a chave.
	 * Nesse caso o token é obtido através de uma chamada para a API do Login.
	 *
	 * Returns false if able to load the system.
	 */
	loadSession(loginParams: LoginUrlQueries): Observable<any> {
		// this.loginAppApiLoginService.cfgLogin();
		localStorage.removeItem("global-user");
		localStorage.removeItem("client-ip");
		if (loginParams.idiomabanco) {
			localStorage.setItem("idiomaBanco", loginParams.idiomabanco);
		} else {
			localStorage.setItem("idiomaBanco", "0");
		}
		this.empresaId = loginParams.empresaId;
		let discovery: ClientDiscoveryData;
		this.goBackPage = loginParams.goBackPage;
		this.goBackModule = loginParams.goBackModule;
		this._zwJSId = loginParams.zwJSId;
		if (loginParams.token) {
			this.chave = this.decodeToken(loginParams.token).chave;
			if (!this.chave) {
				try {
					this.chave = JSON.parse(
						this.decodeToken(loginParams.token).content
					).k;
					this.codUsuarioZW = JSON.parse(
						this.decodeToken(loginParams.token).content
					).cz;
				} catch (e) {
					console.log(e);
				}
			}
			localStorage.setItem("apiToken", loginParams.token);
			return this.clientDiscoveryService
				.discover(this.chave, loginParams.token)
				.pipe(
					tap((result) => {
						discovery = result;
						this.empresas = result.empresas as any;
						this.financeiroEmpresas = result.financeiroEmpresas as any;
						this.modulosHabilitados = result.modulosHabilitados;
						this.modulosHabilitados.push(PlataformaModulo.AGN);
						this.tooltips();
						this.checkRede(discovery, this.chave);
						this.temPermissaoTreinoPorIA(this.chave);
					}),

					switchMap(() => {
						return this.oamdService
							.detalhesEmpresa(this.chave, this.empresaId)
							.pipe(
								catchError((err) => {
									console.error(
										"Error on prepare find company details on OAMD",
										err
									);
									return of(null);
								})
							);
					}),
					tap((response: any) => {
						if (response) {
							let objRetorno = response.return;
							if (objRetorno && objRetorno.nivelAtendimento) {
								localStorage.setItem("nivelAtendimento", response);
							}
						}
					}),

					switchMap(() => {
						return this.validateTokenTreino(discovery).pipe(
							catchError((error) => {
								// Se for erro do validateTokenTreino, para a execução imediatamente
								console.error(
									"ERRO NO validateTokenTreino - PARANDO EXECUÇÃO COMPLETA:",
									error
								);
								console.error("Tipo do erro:", typeof error);
								console.error("Mensagem do erro:", error.message);
								throw error; // Propaga o erro para parar toda a cadeia
							})
						);
					}),
					tap((response: ApiResponseSingle<ValidTokenResponse>) => {
						this.loggedUser = response.content.user;

						const jaVerificou = sessionStorage.getItem(
							"usuarioLogadoJaVerificouPagamentosPendentesFiserv"
						);
						if (!jaVerificou) {
							sessionStorage.setItem(
								"usuarioLogadoJaVerificouPagamentosPendentesFiserv",
								"false"
							);
						}

						this.addModulosPactobr();
						// TODO: este atributo deve ser mantido para manter a retrocompatilibade. Remover após refatorar as dependências.
						this.perfilUsuario = new PefilAcessoDetalhe(
							response.content.perfilUsuario
						);
						this.perfilUsuarioTreino = new PefilAcessoDetalhe(
							response.content.perfilUsuario
						);
					}),
					switchMap(() => {
						return this.validateTokenAdm(discovery);
					}),
					tap((response: ApiValidateTokenAdmResponse) => {
						this.perfilUsuarioAdm = response.content;
					}),
					switchMap(() => {
						const url = `${discovery.serviceUrls.admMsUrl}/v1/perfildeacesso/perfis?unificado=1&page=0&size=1`;
						return this.http.get(url);
					}),
					map((response: ApiResponseList<FuncionalidadeVO>) => {
						if (
							response.content &&
							response.content &&
							response.content.length > 0
						) {
							this.funcionalidadesAtivas.push("PERFIL_ACESSO_UNIFICADO");
						}
					}),
					switchMap(() => {
						return this.obterInfoMigracao(discovery.serviceUrls.admCoreUrl);
					}),
					switchMap(() => {
						const cache = this.getCacheFuncionalidadesInativas(
							this.chave,
							parseInt(this.empresaId)
						);
						if (cache) {
							return of({
								content: cache.funcionalidadesInativas,
								cache: true,
							});
						} else {
							const url = `${discovery.serviceUrls.recursoMsUrl}/v1/funcionalidade/inativas?empresa=${this.empresaId}&chave=${this.chave}`;
							return this.http.get(url);
						}
					}),
					map((response: ApiResponseList<FuncionalidadeVO>) => {
						if (response.content) {
							this.funcionalidadesInativas = response.content.map(
								(funcionalidade) => funcionalidade.name
							);
						}

						if (!response.cache) {
							this.setCacheFuncionalidadesInativas(
								this.chave,
								parseInt(this.empresaId),
								{
									chave: this.chave,
									empresa: parseInt(this.empresaId),
									data: new Date(),
									funcionalidadesInativas: response.content,
								}
							);
						}
					}),
					// Buscar clube de benefícios apenas se chegou até aqui (sem erro no validateTokenTreino)
					switchMap(() => {
						const url = `${discovery.serviceUrls.urlMarketingMs}/v1/clube-beneficios/find-by-filters?chaveEmpresa=${this.chave}&tipoPerfil=${this.perfilUsuarioAdm.perfilUsuario.tipo}`;
						return this.http.get(url).pipe(
							catchError((err) => {
								console.error("Error on clube-beneficios request", err);
								// Em caso de erro no clube de benefícios, continua a execução
								return of({ content: null });
							})
						);
					}),
					map((response: ApiResponseSingle<ClubeDeBeneficiosMarketing>) => {
						if (response.content) {
							this.clubeDeBeneficiosMarketing = response.content;
						}
						return true;
					}),
					catchError((e) => {
						console.error("Error on prepare session from token ", e);
						// Se for erro do validateTokenTreino, propaga o erro para o componente
						if (e.message && e.message.includes("validateTokenTreino")) {
							throw e; // Propaga o erro específico
						}
						// Retorna false para indicar falha, permitindo tratamento no componente
						return of(true);
					})
				);
		} else if (loginParams.sessionId) {
			return this.clientDiscoveryService.discoverUrls().pipe(
				switchMap((resultDiscovery) => {
					this.urllogin = loginParams.urllogin
						? loginParams.urllogin
						: resultDiscovery.serviceUrls.loginAppUrl;
					return this.fetchTokenFromLogin(
						this.urllogin,
						loginParams.sessionId
					).pipe(
						catchError((error) => {
							// Se for erro do fetchTokenFromLogin, para a execução imediatamente
							console.error(
								"ERRO NO fetchTokenFromLogin - PARANDO EXECUÇÃO COMPLETA:",
								error
							);
							console.error("Tipo do erro:", typeof error);
							console.error("Mensagem do erro:", error.message);
							throw error; // Propaga o erro para parar toda a cadeia
						})
					);
				}),
				tap((resultSessionLogin) => {
					if (!resultSessionLogin) {
						throw new Error(
							"fetchTokenFromLogin retornou null - sessão inválida"
						);
					}
					localStorage.setItem("apiToken", resultSessionLogin.loginToken);
					this.chave = resultSessionLogin.key;
					this.codUsuarioZW = resultSessionLogin.usuarioId;
				}),

				switchMap(() => {
					if (!localStorage.getItem("apiToken")) {
						throw new Error(
							"fetchTokenFromLogin retornou null - sessão inválida"
						);
					}
					return this.clientDiscoveryService.discover(
						this.chave,
						localStorage.getItem("apiToken")
					);
				}),
				tap((result) => {
					discovery = result;
					this.tooltips();
					this.checkRede(discovery, this.chave);
					this.temPermissaoTreinoPorIA(this.chave);
					this.empresas = new Array();
					this.modulosHabilitados = result.modulosHabilitados;
					this.modulosHabilitados.push(PlataformaModulo.AGN);
					const decoded = this.decodeToken(localStorage.getItem("apiToken"));
					if (decoded.empresas) {
						try {
							const empresasPermitidas = decoded.empresas.split(",");
							result.empresas.forEach((item: any) => {
								empresasPermitidas.forEach((cod: any) => {
									if (cod === item.codigo.toString()) {
										this.empresas.push(item);
									}
								});
							});
						} catch (e) {
							this.empresas = result.empresas as any;
						}
					} else {
						this.empresas = result.empresas as any;
						this.financeiroEmpresas = result.financeiroEmpresas as any;
						this.modulosHabilitados = result.modulosHabilitados;
					}
				}),

				switchMap(() => {
					return this.oamdService
						.detalhesEmpresa(this.chave, this.empresaId)
						.pipe(
							catchError((err) => {
								console.error(
									"Error on prepare find company details on OAMD",
									err
								);
								return of(null);
							})
						);
				}),
				tap((response: any) => {
					if (response) {
						let objRetorno = response.return;
						if (objRetorno && objRetorno.nivelAtendimento) {
							localStorage.setItem("nivelAtendimento", response);
						}
					}
				}),

				switchMap(() => {
					return this.clientDiscoveryService
						.linkZw(this.usuarioOamd, this.empresaId)
						.pipe(
							switchMap((result) => {
								const cache = this.clientDiscoveryService.cache;
								cache.serviceUrls.zwUrlFull = result;
								this.clientDiscoveryService.cache = cache;
								return this.clientDiscoveryService.moduloUrl(
									PlataformaModulo.GOR,
									this.empresaId
								);
							})
						);
				}),
				switchMap(() => {
					return this.validateTokenTreino(discovery).pipe(
						catchError((error) => {
							// Se for erro do validateTokenTreino, para a execução imediatamente
							console.error(
								"🚨 ERRO NO validateTokenTreino (fluxo sessionId) - PARANDO EXECUÇÃO COMPLETA:",
								error
							);
							console.error("🚨 Tipo do erro:", typeof error);
							console.error("🚨 Mensagem do erro:", error.message);
							throw error; // Propaga o erro para parar toda a cadeia
						})
					);
				}),
				tap((response: ApiResponseSingle<ValidTokenResponse>) => {
					const validatedToken = response.content;
					this.loggedUser = validatedToken.user;
					this.addModulosPactobr();
					// TODO: este atributo deve ser mantido para manter a retrocompatilibade. Remover após refatorar as dependências.
					this.perfilUsuario = new PefilAcessoDetalhe(
						validatedToken.perfilUsuario
					);

					this.perfilUsuarioTreino = new PefilAcessoDetalhe(
						validatedToken.perfilUsuario
					);
				}),
				switchMap(() => {
					return this.validateTokenAdm(discovery);
				}),
				tap((response: ApiValidateTokenAdmResponse) => {
					this.perfilUsuarioAdm = response.content;
				}),
				switchMap(() => {
					const url = `${discovery.serviceUrls.admMsUrl}/v1/perfildeacesso/perfis?unificado=1&page=0&size=1`;
					return this.http.get(url);
				}),
				map((response: ApiResponseList<PerfilAcessoUnificado>) => {
					if (
						response.content &&
						response.content &&
						response.content.length > 0
					) {
						this.funcionalidadesAtivas.push("PERFIL_ACESSO_UNIFICADO");
					}
				}),
				switchMap(() => {
					const cache = this.getCacheFuncionalidadesInativas(
						this.chave,
						parseInt(this.empresaId)
					);
					if (cache) {
						return of({
							content: cache.funcionalidadesInativas,
							cache: true,
						});
					} else {
						const url = `${discovery.serviceUrls.recursoMsUrl}/v1/funcionalidade/inativas?empresa=${this.empresaId}&chave=${this.chave}`;
						return this.http.get(url);
					}
				}),
				map((response: ApiResponseList<FuncionalidadeVO>) => {
					if (response.content) {
						this.funcionalidadesInativas = response.content.map(
							(funcionalidade) => funcionalidade.name
						);
					}

					if (!response.cache) {
						this.setCacheFuncionalidadesInativas(
							this.chave,
							parseInt(this.empresaId),
							{
								chave: this.chave,
								empresa: parseInt(this.empresaId),
								data: new Date(),
								funcionalidadesInativas: response.content,
							}
						);
					}
				}),
				switchMap(() => {
					const url = `${discovery.serviceUrls.urlMarketingMs}/v1/clube-beneficios/find-by-filters?chaveEmpresa=${this.chave}&tipoPerfil=${this.perfilUsuarioAdm.perfilUsuario.tipo}`;
					return this.http.get(url).pipe(
						catchError((err) => {
							console.error("Error on clube-beneficios request", err);
							// Em caso de erro no clube de benefícios, continua a execução
							return of({ content: null });
						})
					);
				}),
				map((response: ApiResponseSingle<ClubeDeBeneficiosMarketing>) => {
					if (response.content) {
						this.clubeDeBeneficiosMarketing = response.content;
					}
					return true;
				}),
				// Buscar detalhes da empresa no OAMD apenas se chegou até aqui (sem erro no validateTokenTreino)
				switchMap(() => {
					return this.oamdService
						.detalhesEmpresa(this.chave, this.empresaId)
						.pipe(
							catchError((err) => {
								console.error(
									"Error on prepare find company details on OAMD",
									err
								);
								// Em caso de erro no OAMD, continua a execução
								return of(null);
							})
						);
				}),
				tap((response: any) => {
					if (response) {
						let objRetorno = response.return;
						if (objRetorno && objRetorno.nivelAtendimento) {
							localStorage.setItem("nivelAtendimento", response);
						}
					}
				}),
				map(() => true),
				catchError((err) => {
					console.error("Error on prepare session from session_id", err);
					// Se for erro do validateTokenTreino ou fetchTokenFromLogin, propaga o erro para o componente
					if (
						err.message &&
						(err.message.includes("validateTokenTreino") ||
							err.message.includes("fetchTokenFromLogin"))
					) {
						throw err; // Propaga o erro específico
					}
					return of(true);
				})
			);
		} else {
			console.log(
				`>>>> SPA SETUP ERROR: CASO O TOKEN NÃO SEJA FORNECIDO PELA URL A SESSION_ID DEVE SER INFORMADA.`
			);
			return of(false);
		}
	}

	private tooltips() {
		try {
			const url = `https://ms1.pactosolucoes.com.br/pontointerrogacao/tooltips`;
			this.http
				.get(url)
				.pipe(
					map((result: any) => {
						localStorage.setItem("tooltips", JSON.stringify(result.content));
					})
				)
				.subscribe();
		} catch (e) {
			console.error(e);
		}
	}

	private temPermissaoTreinoPorIA(chave: string) {
		// estava consultando a url: 'https://app-do-aluno-unificado.web.app/pactoIa/consultarConfigsIA?chave=' + chave;
		// mas foi solicitada a remoção desta consulta, com isso o menu de cadastros no treino: "Atividades Treino por IA"
		// e a configuração: "Atividades do Treino por IA na prescrição de treino" ficarão ocultas
		this.usaPrescricaoIA = false;
	}

	private checkRede(discovery: ClientDiscoveryData, chave: string) {
		try {
			this.configsRede(discovery, chave).subscribe((response) => {
				this.configsTreinoRede = response;
				this.configsTreinoRede.empresaLogadoIsFranqueadora =
					response.chaveFranqueadora === chave;
				this.configsTreinoRede.empresaLogadaNaoPossuiFranqueadora =
					!response.chaveFranqueadora || response.chaveFranqueadora === "";
				console.log(this.configsTreinoRede);
			});
		} catch (e) {
			this.configsTreinoRede = {
				empresaLogadoIsFranqueadora: false,
				chaveFranqueadora: "",
				configTreinoFranqueadora: false,
				treinoPredefinidoFranqueadora: false,
				empresaLogadaNaoPossuiFranqueadora: false,
			};
			console.log(this.configsTreinoRede);
		}
	}

	private configsRede(
		discovery: ClientDiscoveryData,
		chave: string
	): Observable<ConfigsTreinoRede> {
		const oamdUrl = discovery.serviceUrls.oamdUrl;
		const url = `${oamdUrl}/prest/empresaFinanceiro/configsTreinoRede?`;
		return this.http.get(url, { params: { chaveZW: chave } }).pipe(
			map((result: any) => {
				return result.configsTreinoRede;
			})
		);
	}

	private validateTokenTreino(discovery: ClientDiscoveryData) {
		const token = localStorage.getItem("apiToken");
		if (this.modulosHabilitados.includes(PlataformaModulo.SEC) && token) {
			console.log("Iniciando validateTokenTreino com token...");
			const opt = {
				headers: {
					Authorization: token,
					empresaId: this.empresaId,
				},
			};
			return this.http
				.get(
					`${discovery.serviceUrls.treinoApiUrl}/psec/validateToken/sec`,
					opt
				)
				.pipe(
					map((response: any) => {
						const resp = this.cryptoService.decrypt(response.content);
						return { content: JSON.parse(resp) };
					})
				);
		} else {
			console.log("Iniciando validateTokenTreino...");
			const params = new HttpParams().append("empresaId", this.empresaId);
			let url;
			url = `${
				discovery.serviceUrls.treinoApiUrl
			}/psec/validateToken?${params.toString()}`;
			console.log("Iniciando validateTokenTreino para URL:", url);
			return this.http.get(url).pipe(
				tap((response) => {
					console.log("validateTokenTreino executado com sucesso:", response);
				}),
				catchError((error) => {
					const errorMessage = `Erro no método validateTokenTreino: ${
						error.message || error.statusText || "Erro desconhecido"
					}`;
					console.error(
						"ERRO CAPTURADO NO validateTokenTreino:",
						errorMessage,
						error
					);
					throw new Error(errorMessage);
				})
			);
		}
	}

	private validateTokenAdm(discovery: ClientDiscoveryData) {
		const token = localStorage.getItem("apiToken");
		if (this.modulosHabilitados.includes(PlataformaModulo.SEC) && token) {
			console.log("Iniciando validateTokenAdm com token...");
			const opt = {
				headers: {
					Authorization: token,
					empresaId: this.empresaId,
				},
			};
			const zwBootUrl = discovery.serviceUrls.zwBack;
			return this.http.get(`${zwBootUrl}/adm/validate-token`, opt).pipe(
				map((response: any) => {
					const resp = this.cryptoService.decrypt(response.content);
					return { content: JSON.parse(resp) };
				})
			);
		} else {
			console.log("Iniciando validateTokenAdm...");
			const params = new HttpParams()
				.append("chave", this.chave)
				.append("usuario", this.codUsuarioZW)
				.append("empresaId", this.empresaId);
			const url = `${
				discovery.serviceUrls.zwUrl
			}/insec/validateToken?${params.toString()}`;
			return this.http.get(url);
		}
	}

	private checkValidatonTokenOrigin(): "treino" | "adm" {
		if (
			this.modulosHabilitados.includes(PlataformaModulo.TR) ||
			this.modulosHabilitados.includes(PlataformaModulo.NTR)
		) {
			return "treino";
		} else {
			return "adm";
		}
	}

	private empresasPermitidas(discovery): Observable<any> {
		let url;
		if (
			this.modulosHabilitados.includes(PlataformaModulo.TR) ||
			this.modulosHabilitados.includes(PlataformaModulo.NTR)
		) {
			url = `${discovery.serviceUrls.treinoApiUrl}/psec/validateToken/empresas/${this.empresaId}`;
		} else {
			const params = new HttpParams()
				.append("chave", this.chave)
				.append("usuario", this.codUsuarioZW)
				.append("op", "obterEmpresasUsuarioCodigos");
			url = `${discovery.serviceUrls.zwUrl}/insec/adm?${params.toString()}`;
		}

		return this.http.get(url);
	}

	public notificarRecursoEmpresa(recursoNotificar: string) {
		this.notificarRecursoEmpresaService.empresaId = this.empresaId;
		this.notificarRecursoEmpresaService.loggedUser = this.loggedUser;
		this.notificarRecursoEmpresaService.financeiroEmpresas =
			this.financeiroEmpresas;
		this.notificarRecursoEmpresaService.chave = this.chave;
		this.notificarRecursoEmpresaService.notificarRecursoEmpresa(
			recursoNotificar
		);
	}

	public montarChatMovDesk() {
		const base = this.clientDiscoveryService.getUrlMap().personagemMsUrl;
		const urlColab =
			this.tokenProvider === "tr"
				? `${base}/colaboradores/tr/${this.tokenColaboradorId}`
				: `${base}/colaboradores/${this.tokenColaboradorId}`;

		this.http.get(urlColab).subscribe((json: any) => {
			this.colaborador = json.content;
			localStorage.setItem("nameMoviDesk", this.colaborador.nome);
			this.financeiroEmpresas.forEach((obj, index) => {
				if (obj.empresazw === Number(this.empresaId)) {
					localStorage.setItem(
						"CodRefAdditional",
						String(obj.codigoFinanceiro)
					);
					localStorage.setItem(
						"OrganizationCodeReference",
						String(obj.codigoFinanceiro)
					);

					const codColaboradorMovidesk = String(
						obj.codigoFinanceiro + "-" + this.tokenColaboradorId
					);
					localStorage.setItem("CodeReference", codColaboradorMovidesk);
				}
			});
			this.colaborador.telefones.forEach((obj, index) => {
				localStorage.setItem("PhoneNumber", obj.number);
			});
			this.colaborador.emails.forEach((obj, index) => {
				localStorage.setItem("email", obj);
			});
		});

		this.initChatMovidesk();
	}

	private initChatMovidesk() {
		if (this.clientDiscoveryService.isUsarChatMovDesk()) {
			localStorage.setItem(
				"movidesk-idchage",
				this.clientDiscoveryService.getIdChatMovDesk()
			);
			this.movideskChatTreinoService.initConfigNotificarRecursoEmpresa(
				this.empresaId,
				this.financeiroEmpresas,
				this.loggedUser,
				this.chave
			);
			this.movideskChatTreinoService.initMovidesk(
				this.clientDiscoveryService.getIdChatMovDesk(),
				false
			);
		}
	}

	public montarPactoPay() {
		try {
			const base = this.clientDiscoveryService.getUrlMap().zwUrl;
			if (base && this.empresaId && this.loggedUser.username) {
				let params = new HttpParams();
				params = params.append("key", this.chave);
				params = params.append("op", "usuario");
				params = params.append("e", this.empresaId);
				if (this.loggedUser.usuarioZw > 0) {
					params = params.append("u", String(this.loggedUser.usuarioZw));
				} else {
					params = params.append("username", this.loggedUser.username);
				}
				const url = `${base}/prest/pactopay/util?${params.toString()}`;
				this.http.get(url).subscribe((json: any) => {
					const temPermissao = json.content;
					if (!temPermissao) {
						const index = this.modulosHabilitados.indexOf(PlataformaModulo.PAY);
						if (index > -1) {
							this.modulosHabilitados.splice(index, 1);
						}
					}
				});
			}
		} catch (e) {
			console.log(e);
		}
	}

	public verificarUsuarioPacto(username: string): boolean {
		if (username) {
			return username.toUpperCase() === "PACTOBR";
		}
		return false;
	}

	public verificarUsuarioLogadoPacto(): boolean {
		if (this.loggedUser && this.loggedUser.username) {
			return this.loggedUser.username.toUpperCase() === "PACTOBR";
		}
		return false;
	}

	public getModulesOrder(): Observable<any> {
		const base = this.clientDiscoveryService.getUrlMap().admMsUrl;

		if (base) {
			const url = `${base}/v1/modulo`;

			return this.http.get(url, {
				headers: {
					Authorization: `Bearer ${localStorage.getItem("apiToken")}`,
				},
			});
		} else {
			return of([]);
		}
	}

	private addModulosPactobr() {
		if (this.verificarUsuarioLogadoPacto()) {
			this.modulosHabilitados.push(PlataformaModulo.NBIS);
		}
	}

	temPermissaoAdm(codigoPermissao: string): boolean {
		let permitido = false;

		if (
			this.perfilUsuarioAdm &&
			this.perfilUsuarioAdm.perfilUsuario &&
			this.perfilUsuarioAdm.perfilUsuario.funcionalidades
		) {
			const funcionalidade =
				this.perfilUsuarioAdm.perfilUsuario.funcionalidades.filter(
					(funcionaliade) => {
						if (
							funcionaliade &&
							funcionaliade.referenciaFuncionalidade &&
							funcionaliade.referenciaFuncionalidade.trim().toLowerCase() ===
								codigoPermissao.trim().toLowerCase()
						) {
							return true;
						}
					}
				);

			if (funcionalidade && funcionalidade.length > 0) {
				permitido = true;
			}
		}

		return permitido;
	}

	obterInfoMigracao(admCoreUrl: string) {
		const url = `${admCoreUrl}/info-migracao/habilitados`;
		return this.http.get(url).pipe(
			map((response: ApiResponseList<InfoMigracao>) => {
				this.infoMigracaoHabilitados =
					response.content || new Array<InfoMigracao>();
			})
		);
	}
}
