import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { AdmRestService } from "../../adm-rest.service";
import { ZwBootFormaPagamentoService, FormaPagamento } from "adm-legado-api";

@Component({
	selector: "adm-forma-pagamento-form",
	templateUrl: "./forma-pagamento-form.component.html",
	styleUrls: ["./forma-pagamento-form.component.scss"],
})
export class FormaPagamentoFormComponent implements OnInit {
	form: FormGroup;
	id: string;
	isEdicao: boolean = false;
	urlLog: string;
	tiposFormaPagamento: any[] = [];
	convenios: any[] = [];
	projetos: any[] = [];
	empresas: any[] = [];
	contasDestino: any[] = [];
	operacoes: any[] = [];

	get tituloFormulario(): string {
		return this.isEdicao
			? "Editar Forma de Pagamento"
			: "Nova Forma de Pagamento";
	}

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private snotifyService: SnotifyService,
		private admRest: AdmRestService,
		private formaPagamentoService: ZwBootFormaPagamentoService
	) {}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id;

		this.form = this.fb.group({
			codigo: [{ value: "", disabled: true }],
			descricao: ["", Validators.required],
			tipoFormaPagamento: [null, Validators.required],
			convenio: [null],
			gerarPontosParceiro: [false],
			defaultDco: [false],
			ativo: [true],
			usarSomenteFinanceiro: [false],
			compensacaoApenasUteis: [false],
			cor: ["#D5F29B"],
			// Configurações de envio
			projeto: [null],
			projetoNumero: ["553"],
			submodulo: ["20/30"],
			// Configurações de empresas
			empresa: [null],
			contaDestino: [null],
			// Taxa Cartão de Crédito/Valet
			antecipacaoAutomatica: [false],
			numeroParcelas: [0],
			taxaCartao: [0],
			ajustamento: [false],
			operacao: [null],
			vigenciaInicial: [""],
			vigenciaFinal: [""],
		});

		this.carregarTiposFormaPagamento();
		this.carregarConvenios();
		this.carregarProjetos();
		this.carregarEmpresas();
		this.carregarContasDestino();
		this.carregarOperacoes();

		if (this.isEdicao) {
			this.carregarFormaPagamento();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`log/FORMA_PAGAMENTO/${this.id}`
			);
		}
	}

	voltarListagem() {
		this.router.navigate(["adm", "forma-pagamento"]);
	}

	salvar() {
		if (this.form.invalid) {
			this.snotifyService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const formaPagamento = new FormaPagamento();

		if (this.isEdicao) {
			const codigoControl = this.form.get("codigo");
			if (codigoControl && codigoControl.value) {
				formaPagamento.codigo = codigoControl.value;
			}
		}

		const descricaoControl = this.form.get("descricao");
		if (
			descricaoControl &&
			descricaoControl.value !== null &&
			descricaoControl.value !== undefined
		) {
			formaPagamento.descricao = descricaoControl.value;
		}

		const tipoFormaPagamentoControl = this.form.get("tipoFormaPagamento");
		if (
			tipoFormaPagamentoControl &&
			tipoFormaPagamentoControl.value !== null &&
			tipoFormaPagamentoControl.value !== undefined
		) {
			formaPagamento.tipoFormaPagamento = tipoFormaPagamentoControl.value;
		}

		const convenioControl = this.form.get("convenio");
		if (
			convenioControl &&
			convenioControl.value !== null &&
			convenioControl.value !== undefined
		) {
			formaPagamento.convenio = convenioControl.value;
		}

		const gerarPontosParceiroControl = this.form.get("gerarPontosParceiro");
		if (
			gerarPontosParceiroControl &&
			gerarPontosParceiroControl.value !== null &&
			gerarPontosParceiroControl.value !== undefined
		) {
			formaPagamento.gerarPontosParceiro = gerarPontosParceiroControl.value;
		}

		const defaultDcoControl = this.form.get("defaultDco");
		if (
			defaultDcoControl &&
			defaultDcoControl.value !== null &&
			defaultDcoControl.value !== undefined
		) {
			formaPagamento.defaultDco = defaultDcoControl.value;
		}

		const ativoControl = this.form.get("ativo");
		if (
			ativoControl &&
			ativoControl.value !== null &&
			ativoControl.value !== undefined
		) {
			formaPagamento.ativo = ativoControl.value;
		}

		const usarSomenteFinanceiroControl = this.form.get("usarSomenteFinanceiro");
		if (
			usarSomenteFinanceiroControl &&
			usarSomenteFinanceiroControl.value !== null &&
			usarSomenteFinanceiroControl.value !== undefined
		) {
			formaPagamento.usarSomenteFinanceiro = usarSomenteFinanceiroControl.value;
		}

		const compensacaoApenasUteisControl = this.form.get(
			"compensacaoApenasUteis"
		);
		if (
			compensacaoApenasUteisControl &&
			compensacaoApenasUteisControl.value !== null &&
			compensacaoApenasUteisControl.value !== undefined
		) {
			formaPagamento.compensacaoApenasUteis =
				compensacaoApenasUteisControl.value;
		}

		const corControl = this.form.get("cor");
		if (
			corControl &&
			corControl.value !== null &&
			corControl.value !== undefined
		) {
			formaPagamento.cor = corControl.value;
		}

		this.formaPagamentoService.save(formaPagamento).subscribe({
			next: (response) => {
				this.snotifyService.success(
					this.isEdicao
						? "Forma de pagamento atualizada com sucesso!"
						: "Forma de pagamento cadastrada com sucesso!"
				);
				this.voltarListagem();
			},
			error: (error) => {
				this.snotifyService.error("Erro ao salvar forma de pagamento.");
				console.error(error);
			},
		});
	}

	private carregarFormaPagamento() {
		this.formaPagamentoService.find(this.id).subscribe({
			next: (response) => {
				const formaPagamento = response.content;
				this.form.patchValue(formaPagamento);
			},
			error: (error) => {
				this.snotifyService.error("Erro ao carregar forma de pagamento.");
				console.error(error);
			},
		});
	}

	private carregarTiposFormaPagamento() {
		this.formaPagamentoService.tiposFormaPagamento().subscribe({
			next: (response) => {
				this.tiposFormaPagamento = response.content || [];
			},
			error: (error) => {
				console.error("Erro ao carregar tipos de forma de pagamento:", error);
			},
		});
	}

	private carregarConvenios() {
		this.formaPagamentoService.convenios().subscribe({
			next: (response) => {
				this.convenios = response.content || [];
			},
			error: (error) => {
				console.error("Erro ao carregar convênios:", error);
			},
		});
	}

	private carregarProjetos() {
		// Mock data based on the screenshot
		this.projetos = [{ codigo: "geral", nome: "Geral" }];
	}

	private carregarEmpresas() {
		// Mock data
		this.empresas = [{ codigo: 1, nome: "COMERCIAL PACTO" }];
	}

	private carregarContasDestino() {
		// Mock data
		this.contasDestino = [{ codigo: 1, nome: "*********.01.001 CAIXA GERAL" }];
	}

	private carregarOperacoes() {
		// Mock data
		this.operacoes = [{ codigo: 1, nome: "AMERICANEXPRESS CRÉDITO" }];
	}

	adicionarNovaEmpresa() {
		// Method to handle adding new empresa
		console.log("Adicionar nova empresa");
	}

	adicionarTaxa() {
		// Method to handle adding new taxa
		console.log("Adicionar taxa");
	}
}
