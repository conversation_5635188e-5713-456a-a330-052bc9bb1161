<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@fechamento-acessos:modulo"
	i18n-pageTitle="@@fechamento-acessos:title"
	modulo="Administrativo"
	pageTitle="Fechamento de Controle de Acessos de Catraca">
	<pacto-cat-card-plain>
		<form [formGroup]="formGroup">
			<div class="table-wrapper pacto-shadow">
				<div class="row-filters">
					<div>
						<ds3-form-field>
							<ds3-field-label>Empresa</ds3-field-label>
							<ds3-select
								[options]="empresas"
								[valueKey]="'codigo'"
								[nameKey]="'nome'"
								[placeholder]="'Selecione a empresa'"
								formControlName="empresa"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>
					<div>
						<ds3-form-field>
							<ds3-field-label>Período</ds3-field-label>
							<ds3-select
								[options]="periodos"
								[valueKey]="'value'"
								[nameKey]="'label'"
								[placeholder]="'Selecione o período'"
								formControlName="periodo"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>
					<div>
						<ds3-form-field>
							<ds3-field-label>Data Inicial</ds3-field-label>
							<ds3-input-date
								id="fechamento-acessos-data-inicial"
								dateType="datepicker"
								[position]="'middle-left'"
								[control]="formGroup.get('dataInicial')"
								ds3Input></ds3-input-date>
						</ds3-form-field>
					</div>
					<div class="col-md-1_5">
						<ds3-form-field>
							<ds3-field-label></ds3-field-label>
							<ds3-input-date
								ds3Input
								dateType="timepicker"
								[control]="formGroup.get('horaInicial')"></ds3-input-date>
						</ds3-form-field>
					</div>
					<div>
						<ds3-form-field>
							<ds3-field-label>Data Final</ds3-field-label>
							<ds3-input-date
								id="fechamento-acessos-data-final"
								dateType="datepicker"
								[position]="'middle-left'"
								[control]="formGroup.get('dataFinal')"
								ds3Input></ds3-input-date>
						</ds3-form-field>
					</div>
					<div>
						<ds3-form-field>
							<ds3-field-label></ds3-field-label>
							<ds3-input-date
								ds3Input
								dateType="timepicker"
								[control]="formGroup.get('horaFinal')"></ds3-input-date>
						</ds3-form-field>
					</div>
					<div class="btn-processar">
						<button ds3-flat-button id="btn-processar" (click)="processar()">
							<span>Pesquisar</span>
						</button>
					</div>
				</div>
			</div>
		</form>

		<!-- Seção de Estatísticas -->
		<div class="estatisticas-container" *ngIf="totalAcessos > 0">
			<div class="total-acessos">
				<span class="label">Total de Acessos:</span>
				<span class="valor">{{ totalAcessos }}</span>
			</div>

			<!-- Seção de Acessos por Tipo -->
			<div class="secao-acessos">
				<div class="titulo-secao">
					<span>
						<span class="percentual">
							{{ percentualClientesColaboradores }}%
						</span>
						<span class="descricao">
							dos Acessos foram de Clientes e Colaboradores
						</span>
					</span>
				</div>

				<div class="tabela-acessos">
					<div class="header-tabela">
						<div class="col-tipo"></div>
						<div class="col-tipo">% Acesso</div>
						<div class="col-total">Total Acesso</div>
					</div>
					<div
						class="linha-tabela"
						[ngClass]="{ 'linha-total-bold': item.tipo === 'Total' }"
						*ngFor="let item of acessosClientesColaboradores">
						<div class="col-tipo">{{ item.tipo }}</div>
						<div class="col-percentual">{{ item.percentual }}%</div>
						<div class="col-total">{{ item.total }}</div>
					</div>
				</div>
			</div>

			<!-- Seção de Liberações -->
			<div class="secao-liberacoes">
				<div class="titulo-secao">
					<span>
						<span class="percentual">{{ percentualLiberacoes }}%</span>
						<span class="descricao">dos Acessos foram Liberações</span>
					</span>

					<span class="total-bv descricao">
						<span class="">Total BV: {{ totalBV }}</span>
					</span>
				</div>

				<div class="tabela-liberacoes">
					<div class="header-tabela">
						<div class="col-tipo"></div>
						<div class="col-tipo">% Acesso</div>
						<div class="col-total">Total Acesso</div>
						<div class="col-justificado">Já Justificado</div>
						<div class="col-falta">Falta Justificar</div>
					</div>
					<div
						class="linha-tabela"
						*ngFor="let item of liberacoes"
						[ngClass]="{ 'linha-total-bold': item.tipo === 'Total' }">
						<div class="col-tipo">{{ item.tipo }}</div>
						<div class="col-percentual">{{ item.percentual }}%</div>
						<div class="col-total">{{ item.total }}</div>
						<div class="col-justificado">{{ item.justificado }}</div>
						<div class="col-falta">{{ item.faltaJustificar }}</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Legenda -->
		<div class="legenda-container" *ngIf="totalAcessos > 0">
			<div class="legenda-header">
				<div class="caixa-justificado" [ngClass]="classeCorJustificado">
					<div class="percentual-justificado">{{ percentualJustificado }}%</div>
					<div class="label-justificado">Total Justificado</div>
				</div>
				<div class="legenda-section">
					<div class="legenda-titulo">Legenda</div>
					<div class="legenda-items">
						<div class="legenda-item ruim">
							<span class="cor-indicador"></span>
							<span class="texto" [style]="estiloLegenda">0 a 40% Ruim</span>
						</div>
						<div class="legenda-item regular">
							<span class="cor-indicador"></span>
							<span class="texto" [style]="estiloLegenda">
								41 a 60% Regular
							</span>
						</div>
						<div class="legenda-item bom">
							<span class="cor-indicador"></span>
							<span class="texto" [style]="estiloLegenda">61 a 85% Bom</span>
						</div>
						<div class="legenda-item otimo">
							<span class="cor-indicador"></span>
							<span class="texto" [style]="estiloLegenda">86 a 100% Ótimo</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Botões de Ação -->
		<div class="acoes" *ngIf="totalAcessos > 0">
			<div>
				<button
					size="lg"
					ds3-outlined-button
					id="btn-lista-email"
					(click)="imprimir()">
					<i class="pct pct-printer"></i>
					Imprimir
				</button>
			</div>
			<div>
				<button
					size="lg"
					ds3-outlined-button
					id="btn-lista-email"
					(click)="listaEmail()">
					<i class="pct pct-mail"></i>
					Lista Email
				</button>
			</div>
			<div>
				<button
					size="lg"
					ds3-flat-button
					id="btn-gerar-email"
					(click)="gerarEmail()">
					<i class="pct pct-send"></i>
					Gerar email de fechamento
				</button>
			</div>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
